package mogames.gameData.good.bag
{
   import mogames.Layers;
   import mogames.gameData.good.bag.base.GameBagVO;
   import mogames.gameData.good.base.ConstBagVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameRole.TargetUtil;
   import mogames.gameRole.base.IHero;
   import mogames.gameUI.role.ConstRole;
   
   public class ShunYiBagVO extends GameBagVO
   {
      
      public function ShunYiBagVO(param1:ConstBagVO)
      {
         super(param1);
      }
      
      override public function handerUse(param1:Function, param2:Function) : void
      {
         var _loc3_:Array = TargetUtil.friendHeros();
         if(_loc3_.length <= 0)
         {
            EffectManager.addPureText("战场里没有我方武将！");
            if(param2 != null)
            {
               param2();
            }
            return;
         }
         var _loc4_:IHero = _loc3_[0];
         var _loc5_:int = 1;
         var _loc6_:int = int(_loc3_.length);
         while(_loc5_ < _loc6_)
         {
            if(_loc4_.roleVO.curHP >= _loc3_[_loc5_].roleVO.curHP)
            {
               _loc4_ = _loc3_[_loc5_];
            }
            _loc5_++;
         }
         EffectManager.addBMC("SEQ_SHUN_YI_CLIP",Layers.frontLayer,_loc4_.x,_loc4_.y - _loc4_.height * 0.5,false,"AUDIO_SHUN_YI");
         _loc4_.changeStatus(ConstRole.AI_STAND_BY);
         _loc4_.view.setLocation(_loc4_.bornP.x,_loc4_.bornP.y);
         super.handerUse(param1,param2);
      }
   }
}

