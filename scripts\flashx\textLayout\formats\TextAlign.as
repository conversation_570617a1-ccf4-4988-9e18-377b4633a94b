package flashx.textLayout.formats
{
   public final class TextAlign
   {
      
      public static const START:String = "start";
      
      public static const END:String = "end";
      
      public static const LEFT:String = "left";
      
      public static const RIGHT:String = "right";
      
      public static const CENTER:String = "center";
      
      public static const JUSTIFY:String = "justify";
      
      public function TextAlign()
      {
         super();
      }
   }
}

