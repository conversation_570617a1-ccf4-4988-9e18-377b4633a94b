package mogames.gameData.mission.extra
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ExtraWave2397
   {
      
      public function ExtraWave2397()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2397);
         _loc1_.limitBR = new WaveLimitVO(99999999,0.8,0.8);
         _loc1_.zhuBoss = new BossArgVO(844,49000000,126000,90000,80,80,300,90,new BossSkillData0(150,{"hurt":250000},5),1011,0);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(772,900000,100000,11000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(772,900000,100000,11000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(772,900000,100000,11000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(772,900000,100000,11000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(771,900000,100000,11000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(16,new RoleArgVO(773,900000,100000,11000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(782,8000000,75000,12000,50,80,150,80,new BossSkillData1(10,{
            "hurt":200000,
            "roleNum":20
         },3),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(771,900000,100000,11000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(773,900000,100000,11000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(772,900000,100000,11000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(17,new RoleArgVO(773,900000,100000,11000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(782,8000000,75000,12000,50,80,150,80,new BossSkillData1(10,{
            "hurt":200000,
            "hurtCount":5
         },3),1003,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(772,900000,100000,11000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(773,900000,100000,11000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(771,900000,100000,11000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(771,900000,100000,11000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(782,8000000,75000,12000,50,80,150,80,new BossSkillData1(10,{
            "hurt":200000,
            "roleNum":20
         },3),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(772,900000,100000,11000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(772,900000,100000,11000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(771,900000,100000,11000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(772,900000,100000,11000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(771,900000,100000,11000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(773,900000,100000,11000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(781,8000000,75000,12000,50,80,150,80,new BossSkillData0(250,{
            "hurt":200000,
            "hurtCount":5
         },3),1003,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

