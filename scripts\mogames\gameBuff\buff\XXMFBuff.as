package mogames.gameBuff.buff
{
   import mogames.gameBuff.base.TimeRoleBuff;
   import mogames.gameData.buff.ConstBuffVO;
   
   public class XXMFBuff extends TimeRoleBuff
   {
      
      public function XXMFBuff(param1:ConstBuffVO)
      {
         super(param1);
      }
      
      override protected function createData() : void
      {
         _owner.signal.add(this.listenATK);
      }
      
      override protected function onCleanRole() : void
      {
         super.onCleanRole();
         _owner.signal.remove(this.listenATK);
      }
      
      private function listenATK(param1:Object) : void
      {
         if(param1.type != "ROLE_ATK")
         {
            return;
         }
         _owner.roleVO.changeHP(_owner.roleVO.totalHP * _buffVO.args.value * 0.01);
      }
   }
}

