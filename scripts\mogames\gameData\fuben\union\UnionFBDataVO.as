package mogames.gameData.fuben.union
{
   import com.mogames.data.Oint;
   import com.mogames.data.Onum;
   import com.mogames.utils.MathUtil;
   import mogames.ConstData;
   import mogames.gameData.flag.FlagProxy;
   
   public class UnionFBDataVO
   {
      
      public var constVO:UnionFBInfor;
      
      public var extra:int;
      
      private var _curHP:Oint = new Oint();
      
      private var _score:Onum = new Onum();
      
      private var _teams:Array;
      
      public function UnionFBDataVO(param1:UnionFBInfor)
      {
         super();
         this.constVO = param1;
         this.startNew();
      }
      
      public function cleanTeamHero(param1:int) : void
      {
         var _loc2_:int = int(this._teams.indexOf(param1));
         if(_loc2_ != -1)
         {
            this._teams.splice(_loc2_,1);
         }
      }
      
      public function setGet() : void
      {
         FlagProxy.instance().openFlag.setValue(this.constVO.flagID);
      }
      
      public function get hasGet() : Boolean
      {
         return FlagProxy.instance().openFlag.isComplete(this.constVO.flagID);
      }
      
      public function get isFinish() : Boolean
      {
         return this.curHP == ConstData.INT0.v;
      }
      
      public function set curHP(param1:int) : void
      {
         if(param1 <= 0)
         {
            param1 = 0;
         }
         MathUtil.saveINT(this._curHP,param1);
      }
      
      public function get curHP() : int
      {
         return MathUtil.loadINT(this._curHP);
      }
      
      public function set score(param1:Number) : void
      {
         MathUtil.saveNUM(this._score,param1);
      }
      
      public function get score() : Number
      {
         return MathUtil.loadNUM(this._score);
      }
      
      public function set teams(param1:Array) : void
      {
         this._teams = param1;
      }
      
      public function get teams() : Array
      {
         return this._teams;
      }
      
      public function get extraStr() : String
      {
         var _loc1_:Array = [];
         _loc1_[0] = "武力+" + this.extra * 5 + "%";
         _loc1_[1] = "生命+" + this.extra * 5 + "%";
         return _loc1_.join("<br>");
      }
      
      public function startNew() : void
      {
         MathUtil.saveINT(this._curHP,this.constVO.totalHP);
         MathUtil.saveNUM(this._score,0);
         this._teams = [];
      }
      
      public function get saveData() : Object
      {
         var _loc1_:Object = new Object();
         _loc1_.score = this.score;
         _loc1_.teams = this.teams;
         return _loc1_;
      }
      
      public function set loadData(param1:Object) : void
      {
         MathUtil.saveNUM(this._score,param1.score);
         this._teams = param1.teams;
      }
   }
}

