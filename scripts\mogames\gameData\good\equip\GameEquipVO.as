package mogames.gameData.good.equip
{
   import com.mogames.data.IntList;
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import com.mogames.utils.TxtUtil;
   import mogames.ConstData;
   import mogames.gameData.good.base.ConstEquipVO;
   import mogames.gameData.good.base.ConstGoodVO;
   import mogames.gameData.good.base.KacaoVO;
   import mogames.gameData.good.vo.GameGoodVO;
   import mogames.gameData.role.hero.HeroGameVO;
   
   public class GameEquipVO extends GameGoodVO
   {
      
      private var _args:IntList;
      
      protected var _level:Oint = new Oint();
      
      protected var _maxLv:Oint = new Oint();
      
      protected var _lock:Oint = new Oint();
      
      protected var _kacaos:Vector.<KacaoVO>;
      
      protected var _constEquip:ConstEquipVO;
      
      protected var _owner:HeroGameVO;
      
      public function GameEquipVO(param1:ConstEquipVO)
      {
         super(param1 as ConstGoodVO);
         this._constEquip = param1;
         MathUtil.saveINT(this._level,0);
         this._args = new IntList([100,120,140,160,180,210,240,270,300,330,380]);
         MathUtil.saveINT(this._maxLv,this._args.length - 1);
      }
      
      public function createEquip(param1:int) : void
      {
         var _loc4_:KacaoVO = null;
         this._kacaos = new Vector.<KacaoVO>();
         var _loc2_:int = 0;
         var _loc3_:int = this.randomCard(param1);
         while(_loc2_ < _loc3_)
         {
            _loc4_ = new KacaoVO(_loc2_);
            this._kacaos[_loc2_] = _loc4_;
            _loc2_++;
         }
      }
      
      public function setForgeLevel(param1:int) : void
      {
         MathUtil.saveINT(this._level,param1);
         if(this._owner)
         {
            this._owner.updateExtra();
         }
      }
      
      public function addEquipATT() : void
      {
         if(!this._owner)
         {
            return;
         }
         if(this._constEquip.equipATK)
         {
            if(this._constEquip.equipATK.isPer)
            {
               this._owner.perATK += this.countATK(this.level);
            }
            else
            {
               this._owner.extraATK += this.countATK(this.level);
            }
         }
         if(this._constEquip.equipWIT)
         {
            if(this._constEquip.equipWIT.isPer)
            {
               this._owner.perWIT += this.countWIT(this.level);
            }
            else
            {
               this._owner.extraWIT += this.countWIT(this.level);
            }
         }
         if(this._constEquip.equipHP)
         {
            if(this._constEquip.equipHP.isPer)
            {
               this._owner.perHP += this.countHP(this.level);
            }
            else
            {
               this._owner.extraHP += this.countHP(this.level);
            }
         }
         if(this._constEquip.equipDEF)
         {
            if(this._constEquip.equipDEF.isPer)
            {
               this._owner.perDEF += this.countDEF(this.level);
            }
            else
            {
               this._owner.extraDEF += this.countDEF(this.level);
            }
         }
         if(this._constEquip.equipMISS)
         {
            this._owner.extraMISS += this._constEquip.equipMISS.value;
         }
         if(this._constEquip.equipCRIT)
         {
            this._owner.extraCRIT += this._constEquip.equipCRIT.value;
         }
         if(this._constEquip.equipBEI)
         {
            this._owner.extraBEI += this._constEquip.equipBEI.value;
         }
         if(this._constEquip.equipSPD)
         {
            this._owner.extraSPD += this.countSPD(this.level);
         }
         this.addCardAtt();
      }
      
      protected function addCardAtt() : void
      {
         var _loc1_:KacaoVO = null;
         if(!this._owner)
         {
            return;
         }
         for each(_loc1_ in this._kacaos)
         {
            if(_loc1_.hasCard)
            {
               _loc1_.attVO.addEquipATT(this._owner);
            }
         }
      }
      
      public function countATK(param1:int) : int
      {
         if(param1 == 0)
         {
            return this._constEquip.equipATK.value;
         }
         if(this._constEquip.equipATK.isPer)
         {
            return this._constEquip.equipATK.value * this._args.findValue(param1) * 0.01;
         }
         return this._constEquip.equipATK.value * this._args.findValue(param1) * 0.01 + param1;
      }
      
      public function countWIT(param1:int) : int
      {
         if(param1 == 0)
         {
            return this._constEquip.equipWIT.value;
         }
         if(this._constEquip.equipWIT.isPer)
         {
            return this._constEquip.equipWIT.value * this._args.findValue(param1) * 0.01;
         }
         return this._constEquip.equipWIT.value * this._args.findValue(param1) * 0.01 + param1;
      }
      
      public function countHP(param1:int) : int
      {
         if(param1 == 0)
         {
            return this._constEquip.equipHP.value;
         }
         if(this._constEquip.equipHP.isPer)
         {
            return this._constEquip.equipHP.value * this._args.findValue(param1) * 0.01;
         }
         return this._constEquip.equipHP.value * this._args.findValue(param1) * 0.01 + param1;
      }
      
      public function countDEF(param1:int) : int
      {
         if(param1 == 0)
         {
            return this._constEquip.equipDEF.value;
         }
         if(this._constEquip.equipDEF.isPer)
         {
            return this._constEquip.equipDEF.value * this._args.findValue(param1) * 0.01;
         }
         return this._constEquip.equipDEF.value * this._args.findValue(param1) * 0.01 + param1;
      }
      
      public function countSPD(param1:int) : int
      {
         return this._constEquip.equipSPD.value * this._args.findValue(param1) * 0.01 + param1;
      }
      
      public function get score() : int
      {
         var _loc1_:int = 0;
         if(this._constEquip.equipATK)
         {
            _loc1_ += this.countATK(this.level) * 19;
         }
         if(this._constEquip.equipWIT)
         {
            _loc1_ += this.countWIT(this.level) * 166;
         }
         if(this._constEquip.equipHP)
         {
            _loc1_ += this.countHP(this.level) * 1.1;
         }
         if(this._constEquip.equipDEF)
         {
            _loc1_ += this.countDEF(this.level) * 36;
         }
         if(this._constEquip.equipMISS)
         {
            _loc1_ += this._constEquip.equipMISS.value * 211;
         }
         if(this._constEquip.equipCRIT)
         {
            _loc1_ += this._constEquip.equipCRIT.value * 213;
         }
         if(this._constEquip.equipBEI)
         {
            _loc1_ += this._constEquip.equipBEI.value * 209;
         }
         if(this._constEquip.equipSPD)
         {
            _loc1_ += this.countSPD(this.level) * 66;
         }
         return _loc1_;
      }
      
      public function set owner(param1:HeroGameVO) : void
      {
         this._owner = param1;
      }
      
      public function get owner() : HeroGameVO
      {
         return this._owner;
      }
      
      public function get level() : int
      {
         return MathUtil.loadINT(this._level);
      }
      
      public function get maxLv() : int
      {
         return MathUtil.loadINT(this._maxLv);
      }
      
      public function get hasKacao() : Boolean
      {
         return this._kacaos != null;
      }
      
      public function get hasCard() : Boolean
      {
         var _loc1_:KacaoVO = null;
         if(!this.hasKacao)
         {
            return false;
         }
         for each(_loc1_ in this._kacaos)
         {
            if(_loc1_.hasCard)
            {
               return true;
            }
         }
         return false;
      }
      
      public function get numHole() : int
      {
         if(this.hasKacao)
         {
            return this._kacaos.length;
         }
         return 0;
      }
      
      public function get kacaoList() : Vector.<KacaoVO>
      {
         return this._kacaos;
      }
      
      public function get isLock() : Boolean
      {
         return MathUtil.loadINT(this._lock) == 1;
      }
      
      public function set isLock(param1:Boolean) : void
      {
         MathUtil.saveINT(this._lock,int(param1));
      }
      
      public function get constEquip() : ConstEquipVO
      {
         return this._constEquip;
      }
      
      public function findKacao(param1:int) : KacaoVO
      {
         var _loc2_:KacaoVO = null;
         for each(_loc2_ in this._kacaos)
         {
            if(_loc2_.index == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      override public function get qualityName() : String
      {
         return TxtUtil.setColor(this._constEquip.prefixName,ConstData.GOOD_COLOR1[quality]);
      }
      
      private function randomCard(param1:int) : int
      {
         if(param1 != 0)
         {
            return param1;
         }
         var _loc2_:int = Math.random() * 100 + 1;
         if(_loc2_ <= 50)
         {
            return 1;
         }
         if(_loc2_ <= 80)
         {
            return 2;
         }
         return 3;
      }
      
      protected function get saveCards() : Array
      {
         var _loc2_:KacaoVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._kacaos)
         {
            _loc1_[_loc1_.length] = _loc2_.saveData;
         }
         return _loc1_;
      }
      
      protected function set loadCards(param1:Array) : void
      {
         var _loc2_:Array = null;
         var _loc3_:KacaoVO = null;
         var _loc4_:String = null;
         var _loc5_:KacaoVO = null;
         this._kacaos = new Vector.<KacaoVO>();
         for each(_loc4_ in param1)
         {
            _loc2_ = _loc4_.split("H");
            _loc5_ = new KacaoVO(int(_loc2_[0]));
            _loc5_.loadData = _loc2_;
            this._kacaos[this._kacaos.length] = _loc5_;
         }
      }
      
      override public function get saveData() : Object
      {
         return {
            "id":this._constEquip.id,
            "base":[this.level,MathUtil.loadINT(this._lock)].join("H"),
            "cards":this.saveCards
         };
      }
      
      public function set loadData(param1:Object) : void
      {
         var _loc2_:Array = param1.base.split("H");
         MathUtil.saveINT(this._level,int(_loc2_[0]));
         MathUtil.saveINT(this._lock,int(_loc2_[1]));
         this.loadCards = param1.cards;
      }
   }
}

