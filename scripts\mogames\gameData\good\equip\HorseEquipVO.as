package mogames.gameData.good.equip
{
   import com.mogames.utils.MathUtil;
   import mogames.gameData.good.base.ConstEquipVO;
   
   public class HorseEquipVO extends GameEquipVO
   {
      
      public function HorseEquipVO(param1:ConstEquipVO)
      {
         super(param1);
      }
      
      override public function createEquip(param1:int) : void
      {
      }
      
      override public function setForgeLevel(param1:int) : void
      {
      }
      
      override protected function addCardAtt() : void
      {
      }
      
      override public function countATK(param1:int) : int
      {
         return _constEquip.equipATK.value;
      }
      
      override public function countWIT(param1:int) : int
      {
         return _constEquip.equipWIT.value;
      }
      
      override public function countHP(param1:int) : int
      {
         return _constEquip.equipHP.value;
      }
      
      override public function countDEF(param1:int) : int
      {
         return _constEquip.equipDEF.value;
      }
      
      override public function countSPD(param1:int) : int
      {
         return _constEquip.equipSPD.value;
      }
      
      override public function get saveData() : Object
      {
         return {
            "id":_constEquip.id,
            "lock":MathUtil.loadINT(_lock)
         };
      }
      
      override public function set loadData(param1:Object) : void
      {
         MathUtil.saveINT(_lock,param1.lock);
      }
   }
}

