package mogames.gameData.good.base
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   
   public class ConstLevelVO extends ConstGoodVO
   {
      
      private var _min:Oint = new Oint();
      
      private var _max:Oint = new Oint();
      
      public function ConstLevelVO(param1:int, param2:int, param3:int, param4:int, param5:int, param6:String, param7:String, param8:String, param9:String)
      {
         super(param1,param4,1,param5,0,param6,param7,param8,param9);
         MathUtil.saveINT(this._min,param2);
         MathUtil.saveINT(this._max,param3);
      }
      
      public function get min() : int
      {
         return MathUtil.loadINT(this._min);
      }
      
      public function get max() : int
      {
         return MathUtil.loadINT(this._max);
      }
   }
}

