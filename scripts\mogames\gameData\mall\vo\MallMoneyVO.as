package mogames.gameData.mall.vo
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import com.mogames.utils.TxtUtil;
   import mogames.ConstData;
   import mogames.gameData.base.RewardProxy;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.game.LackVO;
   import mogames.gameData.good.vo.GameGoodVO;
   import mogames.gameData.master.MasterProxy;
   import mogames.gameNet.MoneyProxy;
   import mogames.gameNet.MulitProxy;
   import mogames.gameNet.PKGProxy;
   import mogames.gameNet.SaveManager;
   import mogames.gameUI.prompt.LockMessage;
   
   public class MallMoneyVO extends MallBaseVO
   {
      
      private var _mallID:Oint = new Oint();
      
      public function MallMoneyVO(param1:int, param2:int, param3:BaseRewardVO)
      {
         super(param2,0,param3);
         MathUtil.saveINT(this._mallID,param1);
      }
      
      override public function checkLack(param1:int) : LackVO
      {
         if(MoneyProxy.instance().money < param1 * price)
         {
            return new LackVO("金票不足！");
         }
         return MasterProxy.instance().checkValue(10010,param1 * price);
      }
      
      override public function handlerBuy(param1:int, param2:Function) : void
      {
         var vo:GameGoodVO = null;
         var sendBuy:Function = null;
         var buyNum:int = param1;
         var okFunc:Function = param2;
         sendBuy = function():void
         {
            var func:Function = null;
            func = function():void
            {
               MasterProxy.instance().changeValue(10010,-price * buyNum);
               RewardProxy.instance().addReward([vo]);
               LockMessage.instance().destroy();
               if(okFunc != null)
               {
                  okFunc();
               }
               SaveManager.instance().saveAuto(false);
            };
            handlerMoney(buyNum,func);
         };
         vo = rewardVO.newGood();
         if(buyNum > 1)
         {
            vo.amount *= buyNum;
         }
         if(RewardProxy.instance().checkLack([vo]))
         {
            return;
         }
         LockMessage.instance().showMsg("正在处理购买数据，请稍等！");
         MulitProxy.instance().checkMulit(sendBuy,LockMessage.instance().destroy);
      }
      
      override public function askBuy(param1:int) : String
      {
         return "是否花费" + TxtUtil.setColor("金票X" + param1 * price,"ffff00") + "购买" + TxtUtil.setColor(rewardVO.constGood.name + "X" + rewardVO.amount * param1,"99ff00") + "？<br>" + ConstData.BUY;
      }
      
      protected function handlerMoney(param1:int, param2:Function) : void
      {
         var func:Function = null;
         var buyNum:int = param1;
         var okFunc:Function = param2;
         func = function(param1:Object):void
         {
            if(!param1 || !param1.propId)
            {
               return;
            }
            if(int(param1.propId) != mallID)
            {
               return;
            }
            MoneyProxy.instance().refreshSpend(okFunc);
         };
         var temp:Object = new Object();
         temp.propId = String(this.mallID);
         temp.count = buyNum;
         temp.price = price;
         temp.idx = PKGProxy.instance().curIndex;
         MoneyProxy.instance().moneySDK.reqMallBuy(temp,func);
      }
      
      private function get mallID() : int
      {
         return MathUtil.loadINT(this._mallID);
      }
   }
}

