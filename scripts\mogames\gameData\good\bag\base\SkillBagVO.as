package mogames.gameData.good.bag.base
{
   import com.mogames.event.GameSignal;
   import com.mogames.event.SignalManager;
   import mogames.ConstData;
   import mogames.gameData.good.base.ConstBagVO;
   import mogames.gameData.skill.SkillFactory;
   import mogames.gameData.skill.base.GameSkillVO;
   
   public class SkillBagVO extends GameBagVO
   {
      
      protected var _skillVO:GameSkillVO;
      
      public function SkillBagVO(param1:ConstBagVO, param2:int, param3:Object = null)
      {
         super(param1);
         this._skillVO = SkillFactory.newBagSkillVO(param2);
         if(param3 != null)
         {
            this._skillVO.baseArg = param3;
         }
      }
      
      override public function handerUse(param1:Function, param2:Function) : void
      {
         var func:Function = null;
         var $okFunc:Function = param1;
         var $denyFunc:Function = param2;
         func = function():void
         {
            if($okFunc != null)
            {
               $okFunc();
            }
            SignalManager.signalReq.dispatchEvent({"signal":GameSignal.USE_BAG});
         };
         SignalManager.signalSkill.dispatchEvent({
            "from":ConstData.FROM_PLAYER,
            "role":null,
            "skill":this._skillVO.constSkill,
            "arg":this._skillVO.skillARG,
            "okFunc":func,
            "denyFunc":$denyFunc
         });
      }
      
      override public function get infor() : String
      {
         return _constVO.infor + "<br><br>" + this._skillVO.infor + "<br><br>" + "冷却时间：" + cd + "秒<br>" + "技能释放：" + this._skillVO.constSkill.typeStr + "<br>" + "作用单位：" + this._skillVO.constSkill.limitStr;
      }
   }
}

