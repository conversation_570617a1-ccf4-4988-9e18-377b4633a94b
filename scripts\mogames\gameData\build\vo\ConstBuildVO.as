package mogames.gameData.build.vo
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   
   public class ConstBuildVO
   {
      
      private var _buildID:Oint = new Oint();
      
      private var _learn:Array;
      
      private var _name:String;
      
      private var _infor:String;
      
      public function ConstBuildVO(param1:int, param2:Array, param3:String, param4:String)
      {
         super();
         MathUtil.saveINT(this._buildID,param1);
         this._learn = param2;
         this._name = param3;
         this._infor = param4;
      }
      
      public function findLearn(param1:int) : Array
      {
         return this._learn[param1];
      }
      
      public function get buildID() : int
      {
         return MathUtil.loadINT(this._buildID);
      }
      
      public function get name() : String
      {
         return this._name;
      }
      
      public function get infor() : String
      {
         return this._infor;
      }
   }
}

