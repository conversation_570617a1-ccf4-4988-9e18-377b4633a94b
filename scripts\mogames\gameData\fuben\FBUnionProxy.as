package mogames.gameData.fuben
{
   import mogames.gameData.base.vo.BaseDropVO;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.fuben.union.UnionFBDataVO;
   import mogames.gameData.fuben.union.UnionFBInfor;
   
   public class FBUnionProxy
   {
      
      private static var _instance:FBUnionProxy;
      
      public var dataDZ:UnionFBDataVO;
      
      public var dataWC:UnionFBDataVO;
      
      public var dataXP:UnionFBDataVO;
      
      public var dataGD:UnionFBDataVO;
      
      public var dataCB:UnionFBDataVO;
      
      public var dataHF:UnionFBDataVO;
      
      public var dataJZ:UnionFBDataVO;
      
      protected var _list:Array;
      
      public function FBUnionProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.dataDZ = new UnionFBDataVO(new UnionFBInfor(1924,245,301,257,50,[3],[new BaseRewardVO(10000,5000),new BaseDropVO(800,new BaseRewardVO(68001,1)),new BaseDropVO(600,new BaseRewardVO(11005,1)),new BaseDropVO(300,new BaseRewardVO(10006,20))]));
         this.dataWC = new UnionFBDataVO(new UnionFBInfor(1932,246,302,251,70,[0,1,2,3],[new BaseRewardVO(10000,6000),new BaseDropVO(800,new BaseRewardVO(68002,1)),new BaseDropVO(600,new BaseRewardVO(11003,1)),new BaseDropVO(300,new BaseRewardVO(10007,20))]));
         this.dataXP = new UnionFBDataVO(new UnionFBInfor(1933,247,303,252,90,[1],[new BaseRewardVO(10000,7000),new BaseDropVO(800,new BaseRewardVO(68001,1)),new BaseDropVO(600,new BaseRewardVO(11157,1)),new BaseDropVO(300,new BaseRewardVO(10006,30))]));
         this.dataGD = new UnionFBDataVO(new UnionFBInfor(1935,248,304,253,100,[1],[new BaseRewardVO(10000,8000),new BaseDropVO(800,new BaseRewardVO(68002,1)),new BaseDropVO(600,new BaseRewardVO(11204,1)),new BaseDropVO(300,new BaseRewardVO(10007,30))]));
         this.dataCB = new UnionFBDataVO(new UnionFBInfor(1937,249,305,254,100,[0,2],[new BaseRewardVO(10000,9000),new BaseDropVO(800,new BaseRewardVO(68001,1)),new BaseDropVO(600,new BaseRewardVO(11501,1)),new BaseDropVO(300,new BaseRewardVO(10006,30))]));
         this.dataHF = new UnionFBDataVO(new UnionFBInfor(1940,250,306,255,100,[2],[new BaseRewardVO(10000,10000),new BaseDropVO(800,new BaseRewardVO(68002,1)),new BaseDropVO(150,new BaseRewardVO(11104,1)),new BaseDropVO(300,new BaseRewardVO(10007,30))]));
         this.dataJZ = new UnionFBDataVO(new UnionFBInfor(1971,251,307,256,100,[0,1,2,3],[new BaseRewardVO(10000,11000),new BaseDropVO(800,new BaseRewardVO(68001,1)),new BaseDropVO(150,new BaseRewardVO(11151,1)),new BaseDropVO(300,new BaseRewardVO(10006,30))]));
         this._list = [this.dataDZ,this.dataWC,this.dataXP,this.dataGD,this.dataCB,this.dataHF,this.dataJZ];
      }
      
      public static function instance() : FBUnionProxy
      {
         if(!_instance)
         {
            _instance = new FBUnionProxy();
         }
         return _instance;
      }
      
      public function startNew() : void
      {
         this.dataDZ.startNew();
         this.dataWC.startNew();
         this.dataXP.startNew();
         this.dataGD.startNew();
         this.dataCB.startNew();
         this.dataHF.startNew();
         this.dataJZ.startNew();
      }
      
      public function set loadData(param1:Object) : void
      {
         this.startNew();
         if(!param1)
         {
            return;
         }
         this.dataDZ.loadData = param1.dataDZ;
         if(param1.dataWC)
         {
            this.dataWC.loadData = param1.dataWC;
         }
         if(param1.dataXP)
         {
            this.dataXP.loadData = param1.dataXP;
         }
         if(param1.dataGD)
         {
            this.dataGD.loadData = param1.dataGD;
         }
         if(param1.dataCB)
         {
            this.dataCB.loadData = param1.dataCB;
         }
         if(param1.dataHF)
         {
            this.dataHF.loadData = param1.dataHF;
         }
         if(param1.dataJZ)
         {
            this.dataJZ.loadData = param1.dataJZ;
         }
      }
      
      public function get saveData() : Object
      {
         var _loc1_:Object = new Object();
         _loc1_.dataDZ = this.dataDZ.saveData;
         _loc1_.dataWC = this.dataWC.saveData;
         _loc1_.dataXP = this.dataXP.saveData;
         _loc1_.dataGD = this.dataGD.saveData;
         _loc1_.dataCB = this.dataCB.saveData;
         _loc1_.dataHF = this.dataHF.saveData;
         _loc1_.dataJZ = this.dataJZ.saveData;
         return _loc1_;
      }
      
      public function cleanTeamHero(param1:int) : void
      {
         var _loc2_:UnionFBDataVO = null;
         for each(_loc2_ in this._list)
         {
            _loc2_.cleanTeamHero(param1);
         }
      }
      
      public function findData(param1:int) : UnionFBDataVO
      {
         var _loc2_:UnionFBDataVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.constVO.hpID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function get idList() : Array
      {
         var _loc2_:UnionFBDataVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._list)
         {
            _loc1_[_loc1_.length] = _loc2_.constVO.hpID;
         }
         return _loc1_;
      }
      
      public function get fubenWins() : Array
      {
         var _loc2_:UnionFBDataVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._list)
         {
            _loc1_[_loc1_.length] = _loc2_.score;
         }
         return _loc1_;
      }
   }
}

