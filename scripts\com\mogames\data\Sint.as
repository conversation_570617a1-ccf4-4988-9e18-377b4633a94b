package com.mogames.data
{
   import com.mogames.event.SignalManager;
   import flash.system.System;
   
   public class Sint
   {
      
      private var real:String;
      
      private var salt:int;
      
      private var hash:String;
      
      public function Sint(param1:int = 0)
      {
         super();
         this.v = param1;
      }
      
      public function set v(param1:int) : void
      {
         this.real = String(param1);
         this.salt = int(Math.random() * System.freeMemory);
         this.hash = String(int(this.real) + this.salt);
      }
      
      public function get v() : int
      {
         if(this.hash != String(int(this.real) + this.salt))
         {
            SignalManager.signalCheat.dispatchEvent(101);
            return -1;
         }
         return int(this.real);
      }
   }
}

