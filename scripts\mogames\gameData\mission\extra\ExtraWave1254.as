package mogames.gameData.mission.extra
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ExtraWave1254
   {
      
      public function ExtraWave1254()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1254);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(639,375000,2770,600,80,80,300,90,new BossSkillData0(150,{
            "hurt":3850,
            "hurtCount":3
         },5),0,1);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(109,7400,940,130,50,80,200,120,{
            "rate":100,
            "hurtPer":60,
            "spdPer":70,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(109,7400,940,130,50,80,200,120,{
            "rate":100,
            "hurtPer":60,
            "spdPer":70,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(109,7400,940,130,50,80,200,120,{
            "rate":100,
            "hurtPer":60,
            "spdPer":70,
            "keepTime":3
         })));
         _loc2_.addFu(new BossArgVO(204,85000,3300,400,50,80,150,80,new BossSkillData1(10,{"hurt":1650},2),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(110,6400,940,130,50,80,200,120,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(110,6400,940,130,50,80,200,120,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(110,6400,940,130,50,80,200,120,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc2_.addFu(new BossArgVO(205,85000,3300,400,50,80,150,80,new BossSkillData1(10,{"hurt":1650},2),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(111,6400,940,130,50,80,200,120,{
            "rate":160,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(111,6400,940,130,50,80,200,120,{
            "rate":160,
            "keepTime":2
         })));
         _loc2_.addFu(new BossArgVO(206,85000,3300,400,50,80,150,80,new BossSkillData0(150,{"hurt":1670},2),1010,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(111,6400,940,130,50,80,200,120,{
            "rate":160,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(112,6400,940,130,50,80,200,120,{
            "rate":150,
            "hurtBei":1.8
         })));
         _loc2_.addFu(new BossArgVO(207,85000,3300,400,50,80,150,80,new BossSkillData0(150,{"hurt":1250},2),1008,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

