package mogames.gameData.good.zhibao
{
   import com.mogames.sound.SoundManager;
   import com.mogames.utils.MathUtil;
   import mogames.Layers;
   import mogames.gameBuff.BuffProxy;
   import mogames.gameData.buff.base.BuffVO;
   import mogames.gameData.good.base.ConstEquipVO;
   import mogames.gameData.good.equip.GameZhiBaoVO;
   import mogames.gameData.role.battle.BattleHeroVO;
   import mogames.gameData.role.hero.HeroGameVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameRole.TargetUtil;
   import mogames.gameRole.base.IRole;
   
   public class GameSSBZVO extends GameZhiBaoVO
   {
      
      private var arr:Array = [30003,30103,30203];
      
      public function GameSSBZVO(param1:ConstEquipVO)
      {
         super(param1);
      }
      
      override public function handlerATK(param1:IRole) : void
      {
         var _loc7_:BattleHeroVO = null;
         var _loc8_:IRole = null;
         if(!MathUtil.checkOdds(30) || param1.isDead)
         {
            return;
         }
         var _loc2_:BuffVO = new BuffVO(1001,5,{
            "value":15,
            "isPer":true
         });
         var _loc3_:BuffVO = new BuffVO(1006,5,{
            "value":25,
            "isPer":true
         });
         var _loc4_:BuffVO = new BuffVO(1001,5,{
            "value":30,
            "isPer":true
         });
         var _loc5_:BuffVO = new BuffVO(1006,5,{
            "value":200,
            "isPer":true
         });
         BuffProxy.instance().addRoleBuff(_loc2_,param1);
         BuffProxy.instance().addRoleBuff(_loc3_,param1);
         var _loc6_:Array = TargetUtil.friendHeros();
         for each(_loc8_ in _loc6_)
         {
            _loc7_ = _loc8_.roleVO as BattleHeroVO;
            if(this.hasRenWangDun(_loc7_.heroVO))
            {
               BuffProxy.instance().addRoleBuff(_loc4_,_loc8_);
               BuffProxy.instance().addRoleBuff(_loc5_,_loc8_);
            }
         }
         EffectManager.addSkillName("三色宝珠！",Layers.frontLayer,param1.x,param1.y - param1.height);
         SoundManager.instance().playAudio("AUDIO_BUFF");
      }
      
      private function hasRenWangDun(param1:HeroGameVO) : Boolean
      {
         var _loc2_:int = 0;
         for each(_loc2_ in this.arr)
         {
            if(param1.hasEquip(_loc2_))
            {
               return true;
            }
         }
         return false;
      }
   }
}

