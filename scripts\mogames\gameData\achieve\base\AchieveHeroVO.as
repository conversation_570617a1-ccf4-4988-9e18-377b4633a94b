package mogames.gameData.achieve.base
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import mogames.gameData.role.HeroProxy;
   import mogames.gameData.role.hero.HeroGameVO;
   
   public class AchieveHeroVO extends AchieveLockVO
   {
      
      private var _num:Oint = new Oint();
      
      private var _star:Oint = new Oint();
      
      public function AchieveHeroVO(param1:int, param2:int, param3:int, param4:Array, param5:String, param6:String)
      {
         super(param1,param4,param5,param6);
         MathUtil.saveINT(this._num,param2);
         MathUtil.saveINT(this._star,param3);
      }
      
      override public function get isOpen() : Boolean
      {
         var _loc1_:int = 0;
         var _loc4_:HeroGameVO = null;
         var _loc2_:int = MathUtil.loadINT(this._star);
         var _loc3_:Array = HeroProxy.instance().ownerHeros;
         for each(_loc4_ in _loc3_)
         {
            if(_loc4_.quality == _loc2_)
            {
               _loc1_++;
            }
         }
         return _loc1_ >= MathUtil.loadINT(this._num);
      }
      
      override public function get saveData() : String
      {
         return [id,MathUtil.loadINT(_open),MathUtil.loadINT(_get),MathUtil.loadINT(_fail)].join("H");
      }
      
      override public function set loadData(param1:Array) : void
      {
         MathUtil.saveINT(_open,int(param1[1]));
         MathUtil.saveINT(_get,int(param1[2]));
         MathUtil.saveINT(_fail,int(param1[2]));
      }
   }
}

