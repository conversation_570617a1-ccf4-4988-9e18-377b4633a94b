package mogames.gameData.good.base
{
   import file.DazaoConfig;
   import mogames.gameData.base.UseProxy;
   import mogames.gameData.base.func.DazaoVO;
   
   public class ConstPaperVO extends ConstGoodVO
   {
      
      private var _paperInfor:String;
      
      public function ConstPaperVO(param1:int, param2:int, param3:int, param4:String, param5:String, param6:String, param7:String)
      {
         super(param1,param2,1,param3,0,param4,param5,param6,param7);
      }
      
      override public function get infor() : String
      {
         var _loc2_:Array = null;
         var _loc1_:DazaoVO = DazaoConfig.instance().findDazao(id);
         if(!_loc1_)
         {
            return _infor;
         }
         if(!this._paperInfor)
         {
            _loc2_ = [];
            _loc2_[_loc2_.length] = _loc1_.needEquip.name;
            if(_loc1_.gold > 0)
            {
               _loc2_[_loc2_.length] = "银票X" + _loc1_.gold;
            }
            if(_loc1_.yin > 0)
            {
               _loc2_[_loc2_.length] = "白银X" + _loc1_.yin;
            }
            _loc2_ = _loc2_.concat(UseProxy.instance().nameList(_loc1_.needList));
            this._paperInfor = _infor + "<br><br>打造需求：<br>" + _loc2_.join("+");
         }
         return this._paperInfor;
      }
   }
}

