package mogames.gameBuff
{
   import file.BuffConfig;
   import mogames.gameBuff.base.RoleBuff;
   import mogames.gameData.buff.base.BuffVO;
   import mogames.gameRole.base.IRole;
   
   public class BuffProxy
   {
      
      private static var _instance:BuffProxy;
      
      private var _overList:Array = [];
      
      public function BuffProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
      }
      
      public static function instance() : BuffProxy
      {
         if(!_instance)
         {
            _instance = new BuffProxy();
         }
         return _instance;
      }
      
      public function addRoleBuff(param1:BuffVO, param2:IRole) : void
      {
         if(!param2 || param2.isDead)
         {
            return;
         }
         this.checkOverBuff(param1.buffID,param2);
         if(param1.constVO.isOver)
         {
            this.destroyRoleBuff(param1.buffID,param2);
         }
         var _loc3_:RoleBuff = BuffConfig.instance().newRoleBuff(param1.buffID);
         param2.setBuff(_loc3_);
         _loc3_.start(param2,param1);
      }
      
      public function hasBuff(param1:int, param2:IRole) : Boolean
      {
         var _loc3_:RoleBuff = null;
         for each(_loc3_ in param2.buffList)
         {
            if(_loc3_.constVO.buffID == param1)
            {
               return true;
            }
         }
         return false;
      }
      
      public function hasDebuff(param1:IRole) : Boolean
      {
         var _loc2_:RoleBuff = null;
         for each(_loc2_ in param1.buffList)
         {
            if(_loc2_.buffVO.isDebuff)
            {
               return true;
            }
         }
         return false;
      }
      
      private function checkOverBuff(param1:int, param2:IRole) : void
      {
         var _loc3_:int = 0;
         if(this._overList.indexOf(param1) == -1)
         {
            return;
         }
         for each(_loc3_ in this._overList)
         {
            this.destroyRoleBuff(_loc3_,param2);
         }
      }
      
      public function destroyRoleDeff(param1:IRole) : void
      {
         var _loc2_:RoleBuff = null;
         var _loc3_:int = 0;
         while(_loc3_ < param1.buffList.length)
         {
            _loc2_ = param1.buffList[_loc3_];
            if(_loc2_.buffVO.isDebuff)
            {
               param1.buffList.splice(_loc3_,1);
               _loc2_.destroy();
               _loc3_--;
            }
            _loc3_++;
         }
      }
      
      public function destroyRoleBuff(param1:int, param2:IRole) : void
      {
         var _loc3_:RoleBuff = null;
         var _loc4_:int = 0;
         while(_loc4_ < param2.buffList.length)
         {
            _loc3_ = param2.buffList[_loc4_];
            if(_loc3_.constVO.buffID == param1)
            {
               param2.buffList.splice(_loc4_,1);
               _loc3_.destroy();
               _loc4_--;
            }
            _loc4_++;
         }
      }
   }
}

