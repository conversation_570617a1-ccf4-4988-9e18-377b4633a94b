package mogames.gameData.mache
{
   import com.mogames.utils.TxtUtil;
   import mogames.gameData.base.RewardProxy;
   import mogames.gameData.base.vo.BaseRewardVO;
   
   public class MachePropVO extends MacheEventVO
   {
      
      public function MachePropVO(param1:String, param2:Array)
      {
         super(true,param1,param2);
      }
      
      override protected function handlerAdd() : String
      {
         var _loc1_:BaseRewardVO = _list[int(Math.random() * _list.length)];
         var _loc2_:Boolean = RewardProxy.instance().checkGiftLack([_loc1_],false);
         if(!_loc2_)
         {
            RewardProxy.instance().addGiftReward([_loc1_],null,false,false);
         }
         var _loc3_:String = _infor + TxtUtil.setColor(_loc1_.baseInfor,"99ff00");
         if(_loc2_)
         {
            _loc3_ += "<br>" + TxtUtil.setColor("（国库已满，未能获得）");
         }
         return _loc3_;
      }
   }
}

