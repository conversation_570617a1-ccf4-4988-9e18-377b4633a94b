package mogames.gameData.cell
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import mogames.ConstData;
   import mogames.gameData.build.BuildProxy;
   import mogames.gameData.build.vo.GameBuildVO;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.vip.VipProxy;
   
   public class CellConstVO
   {
      
      private var _index:Oint = new Oint();
      
      private var _learns:Array;
      
      public function CellConstVO(param1:int, param2:Array)
      {
         super();
         MathUtil.saveINT(this._index,param1);
         this._learns = param2;
      }
      
      public function get index() : int
      {
         return MathUtil.loadINT(this._index);
      }
      
      public function findNeeds(param1:int) : Array
      {
         return this._learns[param1];
      }
      
      public function setOpen() : void
      {
         if(this.index == ConstData.INT5.v)
         {
            FlagProxy.instance().openFlag.setValue(150);
         }
         else if(this.index == ConstData.INT6.v)
         {
            FlagProxy.instance().openFlag.setValue(151);
         }
      }
      
      public function get isVIP() : Boolean
      {
         return this.index == ConstData.INT5.v || this.index == ConstData.INT6.v;
      }
      
      public function get bgFrame() : int
      {
         if(this.isVIP && VipProxy.instance().hasFunc(110))
         {
            return 2;
         }
         return 1;
      }
      
      public function get isOpen() : Boolean
      {
         var _loc1_:GameBuildVO = BuildProxy.instance().findBuild(101);
         switch(this.index)
         {
            case 2:
               return _loc1_.level >= ConstData.INT1.v;
            case 3:
               return _loc1_.level >= ConstData.INT2.v;
            case 4:
               return _loc1_.level >= ConstData.INT3.v;
            case 5:
               return FlagProxy.instance().openFlag.isComplete(150);
            case 6:
               return FlagProxy.instance().openFlag.isComplete(151);
            default:
               return true;
         }
      }
      
      public function get openInfor() : String
      {
         switch(this.index)
         {
            case 2:
               return "地牢建筑升级到1级后解锁。";
            case 3:
               return "地牢建筑升级到2级后解锁。";
            case 4:
               return "地牢建筑升级到3级后解锁。";
            case 5:
               return "点击消耗100金票后解锁。";
            case 6:
               return "点击消耗100金票后解锁。";
            default:
               return "";
         }
      }
   }
}

