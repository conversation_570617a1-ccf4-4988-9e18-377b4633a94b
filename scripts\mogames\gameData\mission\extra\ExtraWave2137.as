package mogames.gameData.mission.extra
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ExtraWave2137
   {
      
      public function ExtraWave2137()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2137);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(823,1000000,5400,650,80,80,300,90,new BossSkillData0(150,{"hurt":20000},5),1006,0);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(275,25500,2400,110,20,50,190,80,0)));
         _loc2_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(275,25500,2400,110,20,50,190,80,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(275,25500,2400,110,20,50,190,80,0)));
         _loc2_.addFu(new BossArgVO(277,350000,4700,450,50,80,150,80,new BossSkillData1(8,{
            "hurt":6750,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(272,25500,2400,110,20,50,190,80,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(273,25500,2400,110,20,50,190,80,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(273,25500,2400,110,20,50,190,80,0)));
         _loc2_.addFu(new BossArgVO(277,350000,4700,450,50,80,150,80,new BossSkillData1(8,{
            "hurt":6650,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(272,25500,2400,110,20,50,190,80,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(273,25500,2400,110,20,50,190,80,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(272,25500,2400,110,20,50,190,80,0)));
         _loc2_.addFu(new BossArgVO(277,350000,4700,450,50,80,150,80,new BossSkillData0(250,{"hurt":6000},2),1010,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(273,25500,2400,110,20,50,190,80,0)));
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(272,25500,2400,110,20,50,190,80,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(273,25500,2400,110,20,50,190,80,0)));
         _loc2_.addFu(new BossArgVO(278,350000,4700,450,50,80,150,80,new BossSkillData0(250,{
            "hurt":6600,
            "keepTime":3
         },1),1014,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(272,25500,2400,110,20,50,190,80,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(272,25500,2400,110,20,50,190,80,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(273,25500,2400,110,20,50,190,80,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(273,25500,2400,110,20,50,190,80,0)));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

