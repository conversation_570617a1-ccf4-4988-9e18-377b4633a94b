package mogames.gameData.boon
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import file.SevenConfig;
   import mogames.gameData.flag.FlagProxy;
   
   public class SevenDayVO
   {
      
      private var _index:Oint = new Oint();
      
      private var _flagID:Oint = new Oint();
      
      private var _list:Array;
      
      public function SevenDayVO(param1:int, param2:int, param3:Array)
      {
         super();
         MathUtil.saveINT(this._index,param1);
         MathUtil.saveINT(this._flagID,param2);
         this._list = param3;
      }
      
      public function get index() : int
      {
         return MathUtil.loadINT(this._index);
      }
      
      public function get rewardList() : Array
      {
         return this._list;
      }
      
      public function get hasGet() : Boolean
      {
         return FlagProxy.instance().openFlag.isComplete(this.flagID);
      }
      
      public function setGet() : void
      {
         FlagProxy.instance().openFlag.setValue(this.flagID);
      }
      
      public function get isDay() : Bo<PERSON>an
      {
         return SevenConfig.instance().loginIndex >= this.index;
      }
      
      private function get flagID() : int
      {
         return MathUtil.loadINT(this._flagID);
      }
   }
}

