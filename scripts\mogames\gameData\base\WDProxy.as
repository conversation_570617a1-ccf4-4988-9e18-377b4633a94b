package mogames.gameData.base
{
   import com.hexagonstar.util.debug.Debug;
   import com.mogames.utils.MathUtil;
   import flash.display.Loader;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   import mogames.gameData.ServerProxy;
   import mogames.gameData.depot.DepotProxy;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.master.MasterProxy;
   import mogames.gameData.mission.MissionProxy;
   import mogames.gameData.mission.base.MissionFlagVO;
   import mogames.gameData.role.HeroProxy;
   import mogames.gameData.role.hero.HeroGameVO;
   import mogames.gameNet.PKGProxy;
   import unit4399.Open4399Func;
   
   public class WDProxy
   {
      
      private static var _instance:WDProxy;
      
      public function WDProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
      }
      
      public static function instance() : WDProxy
      {
         if(!_instance)
         {
            _instance = new WDProxy();
         }
         return _instance;
      }
      
      public function submit() : void
      {
         var loader:Loader;
         var game_id:String;
         var uid:String;
         var index:String;
         var a:String;
         var b:String;
         var c:String;
         var d:String;
         var e:String;
         var f:String;
         var g:String;
         var h:String;
         var temp:Array;
         var ioErrorHandler:Function = null;
         var submitComplete:Function = null;
         var url:URLRequest = null;
         ioErrorHandler = function(param1:IOErrorEvent):void
         {
            Debug.trace("维度提交错误。");
         };
         submitComplete = function(param1:Event):void
         {
            Debug.trace("维度提交成功。");
            FlagProxy.instance().openFlag.setValue(10);
         };
         if(FlagProxy.instance().openFlag.isComplete(10) || Sanx.isLocal)
         {
            return;
         }
         loader = new Loader();
         game_id = "game_id=100049278";
         uid = "uid=" + Open4399Func.userData.uid;
         index = "index=" + PKGProxy.instance().curIndex;
         a = "a=" + this.checkQXBZ();
         b = "b=" + this.checkGold();
         c = "c=" + this.checkMission();
         d = "d=" + this.checkRWD();
         e = "e=" + this.checkNanHua();
         f = "f=" + this.checkTime();
         g = "g=" + this.hasHero(482);
         h = "h=" + this.hasHero(526);
         temp = [game_id,uid,index,a,b,c,d,e,f,g,h];
         try
         {
            url = new URLRequest("https://stat.api.4399.com/archive_statistics/log.js?" + temp.join("&"));
            url.method = URLRequestMethod.POST;
            loader.contentLoaderInfo.addEventListener(IOErrorEvent.IO_ERROR,ioErrorHandler);
            loader.contentLoaderInfo.addEventListener(Event.COMPLETE,submitComplete);
            loader.load(url);
         }
         catch(err:IOErrorEvent)
         {
            Debug.trace("维度提交错误。");
         }
      }
      
      private function checkQXBZ() : int
      {
         if(HeroProxy.instance().hasEquip(30002) || DepotProxy.instance().findNum(30002) > 0)
         {
            return 1;
         }
         return 0;
      }
      
      private function checkRWD() : int
      {
         if(HeroProxy.instance().hasEquip(30003) || DepotProxy.instance().findNum(30003) > 0)
         {
            return 1;
         }
         return 0;
      }
      
      private function checkNanHua() : int
      {
         if(HeroProxy.instance().hasHero(482))
         {
            return 1;
         }
         return 0;
      }
      
      private function checkQuality() : int
      {
         var _loc3_:int = 0;
         var _loc4_:HeroGameVO = null;
         var _loc1_:Array = [0,1,3,6,10,15,25,35,45,999];
         var _loc2_:Array = HeroProxy.instance().ownerHeros;
         for each(_loc4_ in _loc2_)
         {
            if(_loc4_.quality >= 3)
            {
               _loc3_++;
            }
         }
         return MathUtil.checkWD(_loc3_,_loc1_);
      }
      
      private function checkGold() : int
      {
         var _loc1_:Array = [10000,50000,100000,200000,500000,1000000,2000000,3000000,4000000,5000000,99999999];
         var _loc2_:int = MasterProxy.instance().findValue(10000);
         return MathUtil.checkWD(_loc2_,_loc1_);
      }
      
      private function checkMission() : int
      {
         var _loc3_:int = 0;
         var _loc4_:MissionFlagVO = null;
         var _loc1_:Array = [5,8,12,16,20,24,28,32,36,41,45,49,55,60,65,70,75,80,85];
         var _loc2_:Array = MissionProxy.instance().allMission;
         for each(_loc4_ in _loc2_)
         {
            if(_loc4_.isFinish)
            {
               _loc3_++;
            }
         }
         return MathUtil.checkWD(_loc3_,_loc1_);
      }
      
      private function checkLevel() : int
      {
         var _loc1_:Array = [3,6,9,12,15,18,20,22,25,28,30,33,35];
         var _loc2_:int = MasterProxy.instance().masterVO.level;
         return MathUtil.checkWD(_loc2_,_loc1_);
      }
      
      private function checkStar() : int
      {
         var _loc3_:int = 0;
         var _loc4_:MissionFlagVO = null;
         var _loc1_:Array = [2,12,31,56,57,9999];
         var _loc2_:Array = MissionProxy.instance().allMission;
         for each(_loc4_ in _loc2_)
         {
            _loc3_ += _loc4_.starNum;
         }
         return MathUtil.checkWD(_loc3_,_loc1_);
      }
      
      private function checkZi() : int
      {
         var _loc3_:int = 0;
         var _loc4_:HeroGameVO = null;
         var _loc1_:Array = [0,1,3,5,6,999];
         var _loc2_:Array = HeroProxy.instance().ownerHeros;
         for each(_loc4_ in _loc2_)
         {
            if(_loc4_.quality == 2)
            {
               _loc3_++;
            }
         }
         return MathUtil.checkWD(_loc3_,_loc1_);
      }
      
      private function hasHero(param1:int) : int
      {
         var _loc3_:HeroGameVO = null;
         var _loc2_:Array = HeroProxy.instance().ownerHeros;
         for each(_loc3_ in _loc2_)
         {
            if(_loc3_.heroID == param1)
            {
               return 1;
            }
         }
         return 0;
      }
      
      private function checkTime() : int
      {
         var _loc1_:Array = [10,30,60,90,120,150,210,240,300,420,520,620,720,820,920,1020,1320,1620,1920,2220,3000,4000,5000,7000,999999];
         return MathUtil.checkWD(ServerProxy.instance().playTime,_loc1_);
      }
   }
}

