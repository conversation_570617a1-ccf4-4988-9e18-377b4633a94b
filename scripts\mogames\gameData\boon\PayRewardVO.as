package mogames.gameData.boon
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import mogames.gameData.ServerProxy;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameNet.MoneyProxy;
   
   public class PayRewardVO
   {
      
      private var _flagID:Oint = new Oint();
      
      private var _need:Oint = new Oint();
      
      private var _rewards:Array;
      
      public function PayRewardVO(param1:int, param2:int, param3:Array)
      {
         super();
         MathUtil.saveINT(this._flagID,param1);
         MathUtil.saveINT(this._need,param2);
         this._rewards = param3;
      }
      
      public function get needPay() : int
      {
         return MathUtil.loadINT(this._need);
      }
      
      public function get canGet() : Boolean
      {
         return MoneyProxy.instance().payNum >= this.needPay;
      }
      
      public function get hasGet() : Boolean
      {
         return FlagProxy.instance().openFlag.isComplete(MathUtil.loadINT(this._flagID));
      }
      
      public function setGet() : void
      {
         FlagProxy.instance().openFlag.changeValue(MathUtil.loadINT(this._flagID));
         if(MoneyProxy.instance().payNum < this.needPay)
         {
            ServerProxy.instance().checkCode = 209;
         }
      }
      
      public function get rewards() : Array
      {
         return this._rewards;
      }
   }
}

