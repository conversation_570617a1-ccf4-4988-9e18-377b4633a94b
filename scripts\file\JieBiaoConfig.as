package file
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import mogames.ConstData;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.fuben.vo.FubenVO;
   import mogames.gameData.master.MasterProxy;
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameFuben.yougu.jiebiao.enemy.TDArgVO;
   
   public class JieBiaoConfig
   {
      
      private static var _instance:JieBiaoConfig;
      
      private var _maxHero:Oint = new Oint();
      
      private var _escape:Oint = new Oint();
      
      private var _food:Oint = new Oint();
      
      private var _wood:Oint = new Oint();
      
      public function JieBiaoConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : JieBiaoConfig
      {
         if(!_instance)
         {
            _instance = new JieBiaoConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         MathUtil.saveINT(this._maxHero,10);
         MathUtil.saveINT(this._food,600);
         MathUtil.saveINT(this._wood,800);
         MathUtil.saveINT(this._escape,10);
      }
      
      public function newWaveData() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO();
         _loc2_ = new OneWaveVO(50);
         _loc2_.addEnemy(new WaveEnemyVO(2,this.newArgVO(35)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(3,this.newArgVO(35)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(32);
         _loc2_.addEnemy(new WaveEnemyVO(5,this.newArgVO(36)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(6,this.newArgVO(38)));
         _loc2_.addEnemy(new WaveEnemyVO(2,this.newArgVO(130)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(32);
         _loc2_.addEnemy(new WaveEnemyVO(7,this.newArgVO(41)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(32);
         _loc2_.addEnemy(new WaveEnemyVO(8,this.newArgVO(44)));
         _loc2_.addEnemy(new WaveEnemyVO(1,this.newArgVO(130)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(32);
         _loc2_.addEnemy(new WaveEnemyVO(9,this.newArgVO(47)));
         _loc2_.addEnemy(new WaveEnemyVO(1,this.newArgVO(130)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,this.newArgVO(51)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(32);
         _loc2_.addEnemy(new WaveEnemyVO(11,this.newArgVO(55)));
         _loc2_.addEnemy(new WaveEnemyVO(2,this.newArgVO(130)));
         _loc2_.addEnemy(new WaveEnemyVO(1,this.newArgVO(100)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(16,this.newArgVO(82)));
         _loc2_.addEnemy(new WaveEnemyVO(2,this.newArgVO(130)));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
      
      public function newReward(param1:Boolean) : Array
      {
         if(!param1)
         {
            return this.loseReward;
         }
         return this.winReward;
      }
      
      private function get winReward() : Array
      {
         var _loc6_:int = 0;
         var _loc1_:Array = [];
         var _loc2_:int = MasterProxy.instance().masterVO.level * MathUtil.randomNum(350,450);
         _loc1_[0] = new BaseRewardVO(10000,_loc2_);
         var _loc3_:int = ConstData.INT1.v;
         if(MathUtil.checkOdds(300))
         {
            _loc3_ = ConstData.INT2.v;
         }
         var _loc4_:FubenVO = FubenConfig.instance().findFuben(101);
         var _loc5_:Array = _loc4_.drops.slice(1);
         var _loc7_:int = 0;
         while(_loc7_ < _loc3_)
         {
            _loc6_ = Math.random() * _loc5_.length;
            _loc1_[_loc1_.length] = _loc5_[_loc6_];
            _loc5_.splice(_loc6_,1);
            _loc7_++;
         }
         return _loc1_;
      }
      
      private function get loseReward() : Array
      {
         var _loc1_:int = MasterProxy.instance().masterVO.level * MathUtil.randomNum(300,600);
         return [new BaseRewardVO(10000,_loc1_)];
      }
      
      private function newArgVO(param1:int) : TDArgVO
      {
         var _loc2_:int = MasterProxy.instance().masterVO.level * 85 + 100;
         var _loc3_:int = MasterProxy.instance().masterVO.level;
         var _loc4_:int = MasterProxy.instance().masterVO.level;
         return new TDArgVO(false,801,_loc2_,_loc3_,_loc4_,param1);
      }
      
      public function get maxHero() : int
      {
         return MathUtil.loadINT(this._maxHero);
      }
      
      public function get needWood() : int
      {
         return MathUtil.loadINT(this._wood);
      }
      
      public function get needFood() : int
      {
         return MathUtil.loadINT(this._food);
      }
      
      public function get failNeed() : int
      {
         return MathUtil.loadINT(this._escape);
      }
   }
}

