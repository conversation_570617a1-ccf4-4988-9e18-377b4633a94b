package mogames.gameData.mission.extra
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ExtraWave1255
   {
      
      public function ExtraWave1255()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1255);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(640,375000,2770,600,80,80,300,90,new BossSkillData0(150,{"hurt":3405},5),0,1);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(105,7400,940,130,50,80,200,120,{
            "rate":150,
            "curePer":100
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(101,7400,940,130,50,80,200,120,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(105,7400,940,130,50,80,200,120,{
            "rate":150,
            "curePer":100
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(101,7400,940,130,50,80,200,120,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(101,7400,940,130,50,80,200,120,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(101,7400,940,130,50,80,200,120,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(209,85000,3300,400,50,80,150,80,new BossSkillData1(10,{"hurt":1600},2),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(108,5050,900,130,50,80,200,120,{
            "rate":120,
            "hurtPer":30,
            "keepTime":3
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(101,7400,940,130,50,80,200,120,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(108,5050,900,130,50,80,200,120,{
            "rate":120,
            "hurtPer":30,
            "keepTime":3
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(101,7400,940,130,50,80,200,120,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(208,85000,3300,400,50,80,150,80,new BossSkillData1(8,{
            "hurt":1490,
            "keepTime":3
         },2),1007,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(101,7400,940,130,50,80,200,120,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(108,5050,900,130,50,80,200,120,{
            "rate":120,
            "hurtPer":30,
            "keepTime":3
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(101,7400,940,130,50,80,200,120,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(105,7400,940,130,50,80,200,120,{
            "rate":150,
            "curePer":100
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(101,7400,940,130,50,80,200,120,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(210,85000,3300,400,50,80,150,80,new BossSkillData0(150,{"hurt":1800},2),1009,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(101,7400,940,130,50,80,200,120,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(101,7400,940,130,50,80,200,120,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(108,5050,900,130,50,80,200,120,{
            "rate":120,
            "hurtPer":30,
            "keepTime":3
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(101,7400,940,130,50,80,200,120,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(211,85000,3300,400,50,80,150,80,new BossSkillData0(150,{"hurt":1800},2),1009,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

