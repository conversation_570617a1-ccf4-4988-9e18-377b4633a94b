package file
{
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.main.vo.WalkerVO;
   
   public class WalkerConfig0
   {
      
      private static var _instance:WalkerConfig0;
      
      private var _walkers:Array;
      
      private var _temp:Array;
      
      private var _rewards:Array;
      
      public function WalkerConfig0()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : WalkerConfig0
      {
         if(!_instance)
         {
            _instance = new WalkerConfig0();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._walkers = [];
         this._walkers[this._walkers.length] = new WalkerVO(101,["我这辫子是不是看着有点像女的？","知道吗，我力气可大着呢！","今天天气真不错呢！"]);
         this._walkers[this._walkers.length] = new WalkerVO(102,["我娘老让我读书写字，好烦。","看见那个骑木马的小孩没？整天炫耀！","穿粉色衣服的小女孩是我妹妹！"]);
         this._walkers[this._walkers.length] = new WalkerVO(103,["那些臭男人老瞅我，真是！","我该置办点新衣服了，身上的都不艳了。","沉鱼落雁，闭月羞花，说的是我吗？"]);
         this._walkers[this._walkers.length] = new WalkerVO(104,["看出来了么，我是个郎中。","病从口入，吃东西一定要干净。","每逢佳节胖三斤，陈胖子就是例子！"]);
         this._walkers[this._walkers.length] = new WalkerVO(105,["年纪大了，走会路都喘的厉害。","哎？我这是要去哪里来着？","我儿子可孝顺了，经常给我做好吃的。"]);
         this._walkers[this._walkers.length] = new WalkerVO(106,["这大都以前是个小村子。","我从小就生活在这，从来没离开过。","我有个双胞胎弟弟也住在大都！"]);
         this._walkers[this._walkers.length] = new WalkerVO(107,["我和我双胞胎大哥都住在这里。","在外头漂久了，回到大都感觉变化真大！","我是不是看着不像本地人？"]);
         this._walkers[this._walkers.length] = new WalkerVO(108,["好学近乎知，力行近乎仁，知耻近乎勇乎。","学而时习之，不亦说乎?","这乱世啊，读书很难出人头地！"]);
         this._walkers[this._walkers.length] = new WalkerVO(109,["大都虽是个小县城，但是感觉好热闹！","这边离豫州应该不远了吧?","在这边歇歇脚，明天再赶路！"]);
         this._walkers[this._walkers.length] = new WalkerVO(110,["骑木马的那个小孩好讨厌！","好想吃糖啊，可是妈妈不给我买。","男女授受不亲，你不知道吗！"]);
         this._walkers[this._walkers.length] = new WalkerVO(111,["我那女儿啊，整天瞎跑！","没给女儿买糖吃她就跑了，唉！","我丈夫出去当兵好多年了！","有个叫马甲的小子，为了1块钱躲了我3年。"]);
         this._walkers[this._walkers.length] = new WalkerVO(112,["这世道兵荒马乱的，我都不敢出去！","我这辈子估计是没什么出息了。","酒馆的酒挺便宜的。"]);
         this._walkers[this._walkers.length] = new WalkerVO(113,["城里的乱党被一波不明来历的人给灭了。","大都原来的主公其实也不怎么样。","年纪大了，多走动走动有好处。"]);
         this._walkers[this._walkers.length] = new WalkerVO(114,["今晚炖鱼汤给我家老头子喝。","别看我年纪大，身体好着呢！","城里的陈胖子以前可瘦了！"]);
         this._walkers[this._walkers.length] = new WalkerVO(115,["啦啦啦，我有木马哦！","驾！驾！驾！这木马好看不？","其他小孩老说我坏话。"]);
         this._walkers[this._walkers.length] = new WalkerVO(116,["出门在外，带把雨伞防患于未然！","外头势头这么乱，大都却挺祥和。","听说大都出了乱党，都哪里去了？"]);
         this._walkers[this._walkers.length] = new WalkerVO(117,["不错，我就是陈胖子！","不知道为啥，越来越爱吃。","欧阳燕要许配给吴掌柜了，唉......","谁知道马甲躲哪了？"]);
         this._walkers[this._walkers.length] = new WalkerVO(118,["我的厨艺很好，什么菜都会！","过阵子我准备出去闯闯。","你听说过【传说的厨具】吗？有童年的人都知道哦！"]);
         this._walkers[this._walkers.length] = new WalkerVO(119,["我娘说女孩子家不要老出去乱跑。","陈胖子很可爱啊，嘻嘻，他是个大笨蛋！","娘要把我许配给吴掌柜，可是我心里有人了。"]);
         this._walkers[this._walkers.length] = new WalkerVO(120,["听说以前有个叫囧爷的可厉害了！","我的理想是当一名惩奸除恶的女侠！","我可是会武功的哦！","老苏做的大饼可好吃了！人更帅，嘻嘻！"]);
         this._walkers[this._walkers.length] = new WalkerVO(121,["我是酒馆的伙计阿呆！","酒馆里有很多过往的旅客，有些看着挺威武。","吴掌柜真烦，这不又让我拉客人！"]);
         this._walkers[this._walkers.length] = new WalkerVO(122,["这里的人都叫我刘大婶。","今天想去市集里看看，置办点家用。","这人到中年啊，脾气越来越差！"]);
         this._walkers[this._walkers.length] = new WalkerVO(123,["我经常去演武场看擂台呢。","别看我年纪轻轻，见过的场面多着呢！","今天挣了不少，准备去买点酒喝。"]);
         this._walkers[this._walkers.length] = new WalkerVO(124,["真是的，娘又让我去找哥哥！","肚子好饿，赶紧回家吃饭。","城东的二蛋可调皮了。"]);
         this._rewards = [new BaseRewardVO(10021,1),new BaseRewardVO(10022,1),new BaseRewardVO(10023,1),new BaseRewardVO(10000,111),new BaseRewardVO(10000,222),new BaseRewardVO(10000,333),new BaseRewardVO(10000,178),new BaseRewardVO(10000,231),new BaseRewardVO(10000,1000),new BaseRewardVO(10000,366),new BaseRewardVO(10000,320),new BaseRewardVO(10000,130),new BaseRewardVO(10000,188),new BaseRewardVO(10000,267),new BaseRewardVO(10000,368),new BaseRewardVO(10000,288),new BaseRewardVO(10000,388),new BaseRewardVO(10000,888)];
      }
      
      public function get rewardVO() : BaseRewardVO
      {
         return this._rewards[int(Math.random() * this._rewards.length)];
      }
      
      public function get randomVO() : WalkerVO
      {
         if(!this._temp || this._temp.length <= 0)
         {
            this._temp = this._walkers.slice();
         }
         var _loc1_:int = int(Math.random() * this._temp.length);
         var _loc2_:WalkerVO = this._temp[_loc1_];
         this._temp.splice(_loc1_,1);
         return _loc2_;
      }
   }
}

