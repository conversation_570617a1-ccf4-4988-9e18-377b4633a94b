package com.mogames.system
{
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.FocusEvent;
   import flash.events.KeyboardEvent;
   import flash.net.SharedObject;
   import flash.text.TextField;
   import flash.text.TextFieldType;
   import flash.text.TextFormat;
   import flash.ui.Keyboard;
   import flash.utils.Dictionary;
   import org.osflash.signals.Signal;
   
   public class Console extends Sprite
   {
      
      public var openKey:uint = 9;
      
      private var _inputField:TextField;
      
      private var _executeKey:int;
      
      private var _prevHistoryKey:int;
      
      private var _nextHistoryKey:int;
      
      private var _commandHistory:Array;
      
      private var _historyMax:Number;
      
      private var _showing:Boolean;
      
      private var _currHistoryIndex:int;
      
      private var _numCommandsInHistory:Number;
      
      private var _commandDelegates:Dictionary;
      
      private var _shared:SharedObject;
      
      private var _enabled:Boolean = true;
      
      private var _onShowConsole:Signal;
      
      private var _onHideConsole:Signal;
      
      public function Console(param1:int = 9)
      {
         super();
         addEventListener(Event.ADDED_TO_STAGE,this.onAddedToStage);
         this._shared = SharedObject.getLocal("history");
         this.openKey = param1;
         this._executeKey = Keyboard.ENTER;
         this._prevHistoryKey = Keyboard.UP;
         this._nextHistoryKey = Keyboard.DOWN;
         this._historyMax = 25;
         this._showing = false;
         this._currHistoryIndex = 0;
         this._numCommandsInHistory = 0;
         if(this._shared.data.history)
         {
            this._commandHistory = this._shared.data.history as Array;
            this._numCommandsInHistory = this._commandHistory.length;
         }
         else
         {
            this._commandHistory = new Array();
            this._shared.data.history = this._commandHistory;
         }
         this._commandDelegates = new Dictionary();
         this._inputField = addChild(new TextField()) as TextField;
         this._inputField.type = TextFieldType.INPUT;
         this._inputField.addEventListener(FocusEvent.FOCUS_OUT,this.onConsoleFocusOut);
         this._inputField.defaultTextFormat = new TextFormat("_sans",14,16777215,false,false,false);
         visible = false;
         this._onShowConsole = new Signal();
         this._onHideConsole = new Signal();
      }
      
      public function destroy() : void
      {
         stage.removeEventListener(KeyboardEvent.KEY_UP,this.onToggleKeyPress);
         this._onShowConsole.removeAll();
         this._onHideConsole.removeAll();
      }
      
      public function get onShowConsole() : Signal
      {
         return this._onShowConsole;
      }
      
      public function get onHideConsole() : Signal
      {
         return this._onHideConsole;
      }
      
      public function get enabled() : Boolean
      {
         return this._enabled;
      }
      
      public function set enabled(param1:Boolean) : void
      {
         if(this._enabled == param1)
         {
            return;
         }
         this._enabled = param1;
         if(this._enabled)
         {
            stage.addEventListener(KeyboardEvent.KEY_UP,this.onToggleKeyPress);
         }
         else
         {
            stage.removeEventListener(KeyboardEvent.KEY_UP,this.onToggleKeyPress);
            this.hideConsole();
         }
      }
      
      public function clearStoredHistory() : void
      {
         this._shared.clear();
      }
      
      public function addCommand(param1:String, param2:Function) : void
      {
         this._commandDelegates[param1] = param2;
      }
      
      public function addCommandToHistory(param1:String) : void
      {
         var _loc2_:int = int(this._commandHistory.indexOf(param1));
         if(_loc2_ != -1)
         {
            this._commandHistory.splice(_loc2_,1);
            --this._numCommandsInHistory;
         }
         this._commandHistory.push(param1);
         ++this._numCommandsInHistory;
         if(this._commandHistory.length > this._historyMax)
         {
            this._commandHistory.shift();
            --this._numCommandsInHistory;
         }
         this._shared.flush();
      }
      
      public function getPreviousHistoryCommand() : String
      {
         if(this._currHistoryIndex > 0)
         {
            --this._currHistoryIndex;
         }
         return this.getCurrentCommand();
      }
      
      public function getNextHistoryCommand() : String
      {
         if(this._currHistoryIndex < this._numCommandsInHistory)
         {
            ++this._currHistoryIndex;
         }
         return this.getCurrentCommand();
      }
      
      public function getCurrentCommand() : String
      {
         var _loc1_:String = this._commandHistory[this._currHistoryIndex];
         if(!_loc1_)
         {
            return "";
         }
         return _loc1_;
      }
      
      public function toggleConsole() : void
      {
         if(this._showing)
         {
            this.hideConsole();
         }
         else
         {
            this.showConsole();
         }
      }
      
      public function showConsole() : void
      {
         if(!this._showing)
         {
            this._showing = true;
            visible = true;
            stage.focus = this._inputField;
            stage.addEventListener(KeyboardEvent.KEY_UP,this.onKeyPressInConsole);
            this._currHistoryIndex = this._numCommandsInHistory;
            this._onShowConsole.dispatch();
         }
      }
      
      public function hideConsole() : void
      {
         if(this._showing)
         {
            this._showing = false;
            visible = false;
            stage.removeEventListener(KeyboardEvent.KEY_UP,this.onKeyPressInConsole);
            this._onHideConsole.dispatch();
         }
      }
      
      public function clearConsole() : void
      {
         this._inputField.text = "";
      }
      
      private function onAddedToStage(param1:Event) : void
      {
         graphics.beginFill(0,0.8);
         graphics.drawRect(0,0,stage.stageWidth,30);
         graphics.endFill();
         this._inputField.width = stage.stageWidth;
         this._inputField.y = 4;
         this._inputField.x = 4;
         if(this._enabled)
         {
            stage.addEventListener(KeyboardEvent.KEY_UP,this.onToggleKeyPress);
         }
      }
      
      private function onConsoleFocusOut(param1:FocusEvent) : void
      {
         this.hideConsole();
      }
      
      private function onToggleKeyPress(param1:KeyboardEvent) : void
      {
         if(param1.keyCode == this.openKey)
         {
            this.toggleConsole();
         }
      }
      
      private function onKeyPressInConsole(param1:KeyboardEvent) : void
      {
         var args:Array = null;
         var command:String = null;
         var func:Function = null;
         var expected:Number = NaN;
         var lessArgs:Array = null;
         var event:KeyboardEvent = param1;
         if(event.keyCode == this._executeKey)
         {
            if(this._inputField.text == "" || this._inputField.text == " ")
            {
               return;
            }
            this.addCommandToHistory(this._inputField.text);
            args = this._inputField.text.split(",");
            command = args.shift();
            this.clearConsole();
            this.hideConsole();
            func = this._commandDelegates[command];
            if(func != null)
            {
               try
               {
                  func.apply(this,args);
               }
               catch(e:ArgumentError)
               {
                  if(e.errorID == 1063)
                  {
                     expected = Number(e.message.slice(e.message.indexOf("Expected ") + 9,e.message.lastIndexOf(",")));
                     lessArgs = args.slice(0,expected);
                     func.apply(this,lessArgs);
                  }
               }
            }
         }
         else if(event.keyCode == this._prevHistoryKey)
         {
            this._inputField.text = this.getPreviousHistoryCommand();
            event.preventDefault();
            this._inputField.setSelection(this._inputField.text.length,this._inputField.text.length);
         }
         else if(event.keyCode == this._nextHistoryKey)
         {
            this._inputField.text = this.getNextHistoryCommand();
            event.preventDefault();
            this._inputField.setSelection(this._inputField.text.length,this._inputField.text.length);
         }
      }
   }
}

