package file
{
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class TZBZConfig
   {
      
      private static var _instance:TZBZConfig;
      
      public var activeIndex:int;
      
      public var waveData:WaveDataVO;
      
      private var _rewards:Array;
      
      private var _shows:Array;
      
      public function TZBZConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : TZBZConfig
      {
         if(!_instance)
         {
            _instance = new TZBZConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         var _loc1_:OneWaveVO = null;
         this._rewards = [];
         this._rewards[this._rewards.length] = [new BaseRewardVO(10000,66666),new BaseRewardVO(10512,1),new BaseRewardVO(10512,1),new BaseRewardVO(10512,1),new BaseRewardVO(10513,1),new BaseRewardVO(10513,1),new BaseRewardVO(10513,1),new BaseRewardVO(10512,2),new BaseRewardVO(10513,2)];
         this._rewards[this._rewards.length] = [new BaseRewardVO(10000,77777),new BaseRewardVO(10514,1),new BaseRewardVO(10514,1),new BaseRewardVO(10514,1),new BaseRewardVO(10514,1),new BaseRewardVO(10514,1),new BaseRewardVO(10515,1),new BaseRewardVO(10515,1),new BaseRewardVO(10515,1),new BaseRewardVO(10515,1),new BaseRewardVO(10515,1),new BaseRewardVO(10514,2),new BaseRewardVO(10515,2)];
         this._rewards[this._rewards.length] = [new BaseRewardVO(10000,88888),new BaseRewardVO(10516,1),new BaseRewardVO(10516,1),new BaseRewardVO(10516,1),new BaseRewardVO(10516,1),new BaseRewardVO(10516,1),new BaseRewardVO(10517,1),new BaseRewardVO(10517,1),new BaseRewardVO(10517,1),new BaseRewardVO(10517,1),new BaseRewardVO(10517,1),new BaseRewardVO(10516,2),new BaseRewardVO(10517,2)];
         this._rewards[this._rewards.length] = [new BaseRewardVO(10000,99999),new BaseRewardVO(10518,1),new BaseRewardVO(10518,1),new BaseRewardVO(10518,1),new BaseRewardVO(10518,1),new BaseRewardVO(10518,1),new BaseRewardVO(10519,1),new BaseRewardVO(10519,1),new BaseRewardVO(10519,1),new BaseRewardVO(10519,1),new BaseRewardVO(10519,1),new BaseRewardVO(10518,2),new BaseRewardVO(10519,2)];
         this._rewards[this._rewards.length] = [new BaseRewardVO(10000,111111),new BaseRewardVO(10656,1),new BaseRewardVO(10656,1),new BaseRewardVO(10656,1),new BaseRewardVO(10656,1),new BaseRewardVO(10656,1),new BaseRewardVO(10657,1),new BaseRewardVO(10657,1),new BaseRewardVO(10657,1),new BaseRewardVO(10657,1),new BaseRewardVO(10657,1),new BaseRewardVO(10656,2),new BaseRewardVO(10657,2)];
         this._rewards[this._rewards.length] = [new BaseRewardVO(10000,122222),new BaseRewardVO(10660,1),new BaseRewardVO(10660,1),new BaseRewardVO(10660,1),new BaseRewardVO(10660,1),new BaseRewardVO(10660,1),new BaseRewardVO(10661,1),new BaseRewardVO(10661,1),new BaseRewardVO(10661,1),new BaseRewardVO(10661,1),new BaseRewardVO(10661,1),new BaseRewardVO(10660,2),new BaseRewardVO(10661,2)];
         this._shows = [];
         this._shows[this._shows.length] = [new BaseRewardVO(10000,1),new BaseRewardVO(10512,1),new BaseRewardVO(10513,1)];
         this._shows[this._shows.length] = [new BaseRewardVO(10000,1),new BaseRewardVO(10514,1),new BaseRewardVO(10515,1)];
         this._shows[this._shows.length] = [new BaseRewardVO(10000,1),new BaseRewardVO(10516,1),new BaseRewardVO(10517,1)];
         this._shows[this._shows.length] = [new BaseRewardVO(10000,1),new BaseRewardVO(10518,1),new BaseRewardVO(10519,1)];
         this._shows[this._shows.length] = [new BaseRewardVO(10000,1),new BaseRewardVO(10656,1),new BaseRewardVO(10657,1)];
         this._shows[this._shows.length] = [new BaseRewardVO(10000,1),new BaseRewardVO(10660,1),new BaseRewardVO(10661,1)];
         this.waveData = new WaveDataVO(0);
         this.waveData.limitBR = new WaveLimitVO(0,1.2,1.3);
         this.waveData.zhuBoss = new BossArgVO(1005,99999999,13000,5600,150,250,1000,150,new BossSkillData1(10,{"hurt":60000},999),4006,0);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(266,75000,4000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(268,75000,4000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(270,75000,4000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(271,75000,4000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(266,100000,5000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(268,100000,5000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(270,100000,5000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(271,100000,5000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(266,125000,6000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(268,125000,6000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(270,125000,6000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(271,125000,6000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(266,150000,7000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(268,150000,7000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(270,150000,7000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(271,150000,7000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
      }
      
      public function get showList() : Array
      {
         return this._shows;
      }
      
      public function findList(param1:int) : Array
      {
         return this._rewards[param1];
      }
      
      public function newReward(param1:int) : Array
      {
         var _loc2_:Array = this.findList(this.activeIndex).slice();
         if(param1 <= 150)
         {
            return _loc2_.slice(0,1);
         }
         if(param1 <= 900)
         {
            return this.randomRewards(1,_loc2_);
         }
         if(param1 <= 3000)
         {
            return this.randomRewards(2,_loc2_);
         }
         if(param1 <= 7000)
         {
            return this.randomRewards(3,_loc2_);
         }
         return this.randomRewards(4,_loc2_);
      }
      
      private function randomRewards(param1:int, param2:Array) : Array
      {
         var _loc4_:int = 0;
         var _loc3_:Array = [];
         var _loc5_:int = 0;
         while(_loc5_ < param1)
         {
            _loc4_ = Math.random() * param2.length;
            _loc3_[_loc3_.length] = param2[_loc4_];
            param2.splice(_loc4_,1);
            _loc5_++;
         }
         return _loc3_;
      }
   }
}

