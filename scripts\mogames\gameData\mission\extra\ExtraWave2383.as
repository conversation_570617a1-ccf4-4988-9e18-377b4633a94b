package mogames.gameData.mission.extra
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ExtraWave2383
   {
      
      public function ExtraWave2383()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2383);
         _loc1_.limitBR = new WaveLimitVO(99999999,0.8,0.8);
         _loc1_.zhuBoss = new BossArgVO(782,35000000,112000,60000,80,80,300,90,new BossSkillData0(150,{
            "hurt":250000,
            "atkPer":5
         },5),1023,0);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(771,750000,65000,8000,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(771,750000,65000,8000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(772,750000,65000,8000,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(772,750000,65000,8000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(781,6000000,55000,7000,50,80,150,80,new BossSkillData1(10,{"hurt":130000},3),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(772,750000,65000,8000,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(772,750000,65000,8000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(772,750000,65000,8000,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(14,new RoleArgVO(771,750000,65000,8000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(782,6000000,55000,7000,50,80,150,80,new BossSkillData1(10,{"hurt":130000},3),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(772,750000,65000,8000,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(772,750000,65000,8000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(772,750000,65000,8000,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(14,new RoleArgVO(772,750000,65000,8000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(783,6000000,55000,7000,50,80,150,80,new BossSkillData0(250,{
            "hurt":130000,
            "hurtCount":5
         },3),1003,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(772,750000,65000,8000,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(771,750000,65000,8000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(772,750000,65000,8000,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(772,750000,65000,8000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(782,6000000,55000,7000,50,80,150,80,new BossSkillData0(250,{"hurt":130000},3),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(772,750000,65000,8000,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(772,750000,65000,8000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(772,750000,65000,8000,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(771,750000,65000,8000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(772,750000,65000,8000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(781,6000000,55000,7000,50,80,150,80,new BossSkillData0(250,{"hurt":130000},3),1008,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

