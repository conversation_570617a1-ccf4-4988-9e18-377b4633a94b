package file
{
   import mogames.gameData.box.base.BoxSkinVO;
   
   public class BoxConfig
   {
      
      private static var _instance:BoxConfig;
      
      private var _list:Array;
      
      public function BoxConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : BoxConfig
      {
         if(!_instance)
         {
            _instance = new BoxConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = [];
         this._list[this._list.length] = new BoxSkinVO(0,44,40,"SEQ_RED_BOX");
         this._list[this._list.length] = new BoxSkinVO(1,38,32,"SEQ_YELLOW_BOX");
         this._list[this._list.length] = new BoxSkinVO(2,30,28,"SEQ_SLIVERY_BOX");
         this._list[this._list.length] = new BoxSkinVO(3,30,26,"SEQ_WHITE_BOX");
      }
      
      public function findSkin(param1:int) : BoxSkinVO
      {
         var _loc2_:BoxSkinVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.id == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

