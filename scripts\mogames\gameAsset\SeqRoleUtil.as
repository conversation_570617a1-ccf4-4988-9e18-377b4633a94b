package mogames.gameAsset
{
   import com.mogames.utils.ConvertUtil;
   import file.HeroConfig;
   import file.RoleConfig;
   import file.TuJianConfig;
   import flash.display.MovieClip;
   import mogames.gameData.role.base.RoleInfoVO;
   import mogames.gameData.role.hero.HeroInfoVO;
   import mogames.gameData.role.hero.HeroOtherVO;
   
   public class SeqRoleUtil
   {
      
      private static var _skins:Array = ["_STAND","_MOVE","_ATTACK","_DEAD","_WIN","_STONE","_STUN","_PALSY","_ICE","_STUCK"];
      
      public function SeqRoleUtil()
      {
         super();
      }
      
      public static function newSeq(param1:String) : Array
      {
         var _loc4_:String = null;
         var _loc5_:MovieClip = null;
         var _loc2_:Array = [];
         var _loc3_:Array = newSkins(param1);
         for each(_loc4_ in _loc3_)
         {
            _loc5_ = AssetManager.newMCRes(_loc4_);
            if(_loc5_)
            {
               _loc2_.push(ConvertUtil.convertMC(_loc5_));
            }
         }
         return _loc2_;
      }
      
      public static function newSkins(param1:String) : Array
      {
         var _loc3_:String = null;
         var _loc2_:Array = [];
         for each(_loc3_ in _skins)
         {
            _loc2_.push(param1 + _loc3_);
         }
         return _loc2_;
      }
      
      public static function convertHeroAsset() : void
      {
         var _loc2_:HeroInfoVO = null;
         var _loc3_:HeroOtherVO = null;
         var _loc4_:Array = null;
         var _loc5_:int = 0;
         var _loc1_:Vector.<HeroOtherVO> = HeroConfig.instance().otherList;
         for each(_loc3_ in _loc1_)
         {
            _loc2_ = RoleConfig.instance().findInfo(_loc3_.heroID) as HeroInfoVO;
            AssetManager.findRoleSeq(_loc2_.ownSkinID);
         }
         _loc4_ = [40101,40201,40301,40401,40501,40601,40701];
         for each(_loc5_ in _loc4_)
         {
            AssetManager.findRoleSeq(_loc5_);
         }
      }
      
      public static function convertRoleAsset() : void
      {
         var _loc2_:RoleInfoVO = null;
         var _loc3_:int = 0;
         var _loc1_:Array = TuJianConfig.instance().tuRoles;
         for each(_loc3_ in _loc1_)
         {
            _loc2_ = RoleConfig.instance().findInfo(_loc3_);
            AssetManager.findRoleSeq(_loc2_.enemySkinID);
         }
      }
   }
}

