package mogames.gameData.huodong.vo
{
   import com.adobe.crypto.MD5;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   import flash.net.URLVariables;
   import mogames.gameData.huodong.BaseExchange;
   import mogames.gameEffect.EffectManager;
   
   public class JifenExchange0 extends BaseExchange
   {
      
      private var KEY:String;
      
      public function JifenExchange0()
      {
         super();
         _req = new URLRequest("https://my.4399.com/jifen/api-apply");
         this.KEY = "6fe5caf1ddf082c5ae69d5cbbcdad3ce";
      }
      
      override public function startExchange(... rest) : void
      {
         var _loc2_:URLVariables = new URLVariables();
         _loc2_.activation = String(rest[1]);
         _loc2_.product_id = String(rest[0]);
         _loc2_.uid = Number(uid);
         _loc2_.type = "1";
         _loc2_.token = this.countKey([_loc2_.activation,_loc2_.product_id,"1",_loc2_.uid,this.KEY]);
         _req.data = _loc2_;
         _req.method = URLRequestMethod.POST;
         _loader.load(_req);
      }
      
      override protected function countKey(param1:Array) : String
      {
         var _loc3_:String = null;
         var _loc2_:String = "";
         for each(_loc3_ in param1)
         {
            _loc2_ += _loc3_;
         }
         return MD5.hash(_loc2_);
      }
      
      override protected function handlerLoaded(param1:Object) : void
      {
         super.handlerLoaded(param1);
         param1 = JSON.parse(String(param1));
         if(param1.code != 100)
         {
            if(param1.code == 107)
            {
               EffectManager.addPureText("激活码不存在！");
            }
            else
            {
               EffectManager.addPureText(param1.msg);
            }
            return;
         }
         _okFunc();
      }
   }
}

