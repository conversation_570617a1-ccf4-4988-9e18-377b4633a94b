package mogames.gameData.mission.extra
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ExtraWave2511
   {
      
      public function ExtraWave2511()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2511);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(849,552000000,2480000,960000,80,80,300,90,new BossSkillData0(150,{
            "hurt":5000000,
            "keepTime":5,
            "hurtCount":5
         },5),1001,0);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new <PERSON><PERSON>rgVO(774,9350000,575000,520000,100,100,200,120,0)));
         _loc2_.addFu(new BossArgVO(779,92000000,1050000,550000,100,120,150,80,new BossSkillData0(250,{
            "hurt":800000,
            "roleNum":20
         },3),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(775,9350000,575000,520000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(774,9350000,575000,520000,100,100,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(775,9350000,575000,520000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(774,9350000,575000,520000,100,100,200,120,0)));
         _loc2_.addFu(new BossArgVO(779,92000000,1050000,550000,100,120,150,80,new BossSkillData1(8,{"hurt":800000},3),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(775,9350000,575000,520000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(775,9350000,575000,520000,100,100,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(775,9350000,575000,520000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(775,9350000,575000,520000,100,100,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(775,9350000,575000,520000,100,100,200,120,0)));
         _loc2_.addFu(new BossArgVO(779,92000000,1050000,550000,100,120,150,80,new BossSkillData1(8,{"hurt":800000},3),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(775,9350000,575000,520000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(775,9350000,575000,520000,100,100,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(775,9350000,575000,520000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(775,9350000,575000,520000,100,100,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(775,9350000,575000,520000,100,100,200,120,0)));
         _loc2_.addFu(new BossArgVO(779,92000000,1050000,550000,100,120,150,80,new BossSkillData1(8,{
            "hurt":800000,
            "roleNum":20
         },3),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(775,9350000,575000,520000,100,100,200,120,0)));
         _loc2_.addFu(new BossArgVO(779,92000000,1050000,550000,100,120,150,80,new BossSkillData0(250,{"hurt":800000},3),1008,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

