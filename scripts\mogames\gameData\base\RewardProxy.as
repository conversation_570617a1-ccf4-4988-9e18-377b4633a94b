package mogames.gameData.base
{
   import com.mogames.sound.SoundManager;
   import com.mogames.utils.TxtUtil;
   import file.GoodConfig;
   import mogames.ConstData;
   import mogames.gameData.base.vo.BaseDropVO;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.depot.DepotProxy;
   import mogames.gameData.game.LackVO;
   import mogames.gameData.good.base.ConstVirtualVO;
   import mogames.gameData.good.vo.GameGoodVO;
   import mogames.gameData.master.MasterProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameUI.prompt.MiniMessage;
   
   public class RewardProxy
   {
      
      private static var _instance:RewardProxy;
      
      public function RewardProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
      }
      
      public static function instance() : RewardProxy
      {
         if(!_instance)
         {
            _instance = new RewardProxy();
         }
         return _instance;
      }
      
      public function newDropReward(param1:Array) : Array
      {
         var _loc3_:BaseDropVO = null;
         if(!param1)
         {
            return [];
         }
         var _loc2_:Array = [];
         for each(_loc3_ in param1)
         {
            if(_loc3_)
            {
               _loc2_[_loc2_.length] = _loc3_.rewardVO.newGood();
            }
         }
         return _loc2_;
      }
      
      public function newGiftReward(param1:Array) : Array
      {
         var _loc3_:BaseRewardVO = null;
         if(!param1)
         {
            return [];
         }
         var _loc2_:Array = [];
         for each(_loc3_ in param1)
         {
            if(_loc3_)
            {
               _loc2_[_loc2_.length] = _loc3_.newGood();
            }
         }
         return _loc2_;
      }
      
      public function checkGiftLack(param1:Array, param2:Boolean = false) : Boolean
      {
         var _loc3_:Array = this.newGiftReward(param1);
         return this.checkLack(_loc3_,param2);
      }
      
      public function checkLack(param1:Array, param2:Boolean = false) : Boolean
      {
         var _loc3_:Array = this.splitByVirtual(param1);
         var _loc4_:Array = this.splitByLabel(_loc3_[1]);
         var _loc5_:LackVO = DepotProxy.instance().checkLack(_loc4_);
         if(_loc5_)
         {
            if(param2)
            {
               EffectManager.addPureText(TxtUtil.setColor(_loc5_.str,"ffff00"));
            }
            return true;
         }
         return false;
      }
      
      public function addGiftReward(param1:Array, param2:Function = null, param3:Boolean = true, param4:Boolean = true) : void
      {
         var _loc5_:Array = this.newGiftReward(param1);
         this.addReward(_loc5_,param2,param3,param4);
      }
      
      public function addReward(param1:Array, param2:Function = null, param3:Boolean = true, param4:Boolean = true) : void
      {
         var _loc8_:GameGoodVO = null;
         var _loc9_:Array = null;
         var _loc5_:Array = this.splitByVirtual(param1);
         var _loc6_:Array = this.splitByLabel(_loc5_[1]);
         var _loc7_:LackVO = DepotProxy.instance().checkLack(_loc6_);
         if(_loc7_)
         {
            EffectManager.addPureText(TxtUtil.setColor(_loc7_.str,"ffff00"));
            return;
         }
         for each(_loc8_ in _loc5_[0])
         {
            MasterProxy.instance().changeValue(_loc8_.constGood.id,_loc8_.amount);
         }
         for each(_loc9_ in _loc6_)
         {
            DepotProxy.instance().addItems(_loc9_);
         }
         if(param3)
         {
            MiniMessage.instance().showMsg("获得：" + this.parseName0(param1),200,false,"");
         }
         if(param4)
         {
            SoundManager.instance().playAudio("AUDIO_REWARD");
         }
         if(param2 != null)
         {
            param2();
         }
      }
      
      public function parseName0(param1:Array) : String
      {
         var _loc3_:GameGoodVO = null;
         var _loc2_:Array = [];
         for each(_loc3_ in param1)
         {
            _loc2_[_loc2_.length] = _loc3_.qualityName + TxtUtil.setColor("X" + _loc3_.amount,ConstData.GOOD_COLOR1[_loc3_.quality]);
         }
         return _loc2_.join("，");
      }
      
      public function parseName1(param1:Array, param2:Boolean) : String
      {
         var _loc4_:BaseRewardVO = null;
         var _loc3_:Array = [];
         for each(_loc4_ in param1)
         {
            if(param2)
            {
               _loc3_[_loc3_.length] = _loc4_.colorInfor;
            }
            else
            {
               _loc3_[_loc3_.length] = _loc4_.baseInfor;
            }
         }
         return _loc3_.join("，");
      }
      
      public function parseName2(param1:Array, param2:String) : String
      {
         var _loc4_:BaseRewardVO = null;
         var _loc3_:Array = [];
         for each(_loc4_ in param1)
         {
            _loc3_[_loc3_.length] = _loc4_.constGood.name + param2 + _loc4_.amount;
         }
         return _loc3_.join("，");
      }
      
      private function splitByVirtual(param1:Array) : Array
      {
         var _loc3_:GameGoodVO = null;
         var _loc2_:Array = [[],[]];
         for each(_loc3_ in param1)
         {
            if(_loc3_.constGood is ConstVirtualVO)
            {
               _loc2_[0].push(_loc3_);
            }
            else
            {
               _loc2_[1].push(_loc3_);
            }
         }
         return _loc2_;
      }
      
      private function splitByLabel(param1:Array) : Array
      {
         var _loc5_:int = 0;
         var _loc2_:Array = [[],[],[],[],[],[],[],[]];
         var _loc3_:int = 0;
         var _loc4_:int = int(param1.length);
         while(_loc3_ < _loc4_)
         {
            _loc5_ = GoodConfig.instance().findGoodLabel(param1[_loc3_].constGood.id);
            _loc2_[_loc5_ - 1].push(param1[_loc3_]);
            _loc3_++;
         }
         return _loc2_;
      }
   }
}

