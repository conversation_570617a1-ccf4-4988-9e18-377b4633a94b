package mogames.gameData.mission.extra
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ExtraWave2098
   {
      
      public function ExtraWave2098()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2098);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(821,800000,4400,650,80,80,300,90,new BossSkillData0(150,{
            "hurt":6800,
            "roleNum":30
         },5),1005,0);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(272,15400,1750,100,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(274,15400,1750,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(272,15400,1750,100,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(275,225000,4500,450,50,80,150,80,new BossSkillData0(150,{
            "hurt":5055,
            "keepTime":3
         },1),1014,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(272,15400,1750,100,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(274,15400,1750,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(272,15400,1750,100,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(275,225000,4500,450,50,80,150,80,new BossSkillData1(12,{
            "hurt":5050,
            "hurtCount":5
         },1),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(272,15400,1750,100,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(274,15400,1750,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(273,15400,1750,100,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(275,225000,4500,450,50,80,150,80,new BossSkillData0(150,{"hurt":15500},2),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(273,15400,1750,100,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(275,15400,1750,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(273,15400,1750,100,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(275,15400,1750,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(273,15400,1750,100,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(275,15400,1750,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(274,15400,1750,100,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(275,225000,4500,450,50,80,150,80,new BossSkillData0(150,{
            "hurt":5055,
            "keepTime":3
         },1),1014,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

