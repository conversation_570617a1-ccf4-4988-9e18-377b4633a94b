package mogames.gameData.cheat
{
   import com.mogames.system.SysTimer;
   import flash.utils.getTimer;
   import mogames.Layers;
   import mogames.gameUI.prompt.ConfirmMessage;
   
   public class SpeedChecker
   {
      
      private var cheatNum:int;
      
      private var cheatTime:int;
      
      private var timerNum:int;
      
      private var _timer:SysTimer;
      
      private var isActive:Boolean;
      
      public function SpeedChecker()
      {
         super();
         this._timer = new SysTimer();
      }
      
      public function init() : void
      {
         this._timer.setLoop(1,this.checkCheat);
      }
      
      private function checkCheat() : void
      {
         var _loc1_:Date = new Date();
         var _loc2_:int = _loc1_.time - this.cheatTime;
         var _loc3_:int = getTimer() - this.timerNum;
         var _loc4_:int = _loc3_ - _loc2_;
         if(_loc4_ > 10)
         {
            ++this.cheatNum;
            if(this.cheatNum > 5)
            {
               Layers.setLock(true);
               ConfirmMessage.instance().showMsg("请勿使用其他程序加速游戏！",false);
               this._timer.stop();
            }
         }
         else
         {
            this.cheatNum = 0;
         }
         this.timerNum = getTimer();
         this.cheatTime = _loc1_.time;
      }
   }
}

