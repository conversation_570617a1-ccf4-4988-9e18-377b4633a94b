package mogames.gameData.good.zhibao
{
   import mogames.gameData.good.base.ConstEquipVO;
   import mogames.gameData.good.equip.GameZhiBaoVO;
   import mogames.gameData.role.battle.HurtData;
   import mogames.gameRole.base.IRole;
   import mogames.gameRole.view.HeroBaseView;
   
   public class GameYuXiVO extends GameZhiBaoVO
   {
      
      public function GameYuXiVO(param1:ConstEquipVO)
      {
         super(param1);
      }
      
      override public function hurtBefore(param1:IRole, param2:HurtData) : void
      {
         param2.hurtValue *= 0.9;
         (param1.view as HeroBaseView).startZBEffect();
      }
   }
}

