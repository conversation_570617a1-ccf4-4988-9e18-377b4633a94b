package file
{
   import mogames.ConstData;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.fuben.vo.FubenVO;
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.RoleArgVO;
   
   public class MoBeiConfig
   {
      
      private static var _instance:MoBeiConfig;
      
      public var waveData:WaveDataVO;
      
      public var argVO:RoleArgVO;
      
      public var peoples:Array;
      
      public function MoBeiConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : MoBeiConfig
      {
         if(!_instance)
         {
            _instance = new MoBeiConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         var _loc1_:OneWaveVO = null;
         this.argVO = new RoleArgVO(166,10000,13000,1995,30,10,250,130,null);
         this.waveData = new WaveDataVO(0);
         this.waveData.limitBR = new WaveLimitVO(5000,1.2,1.3);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(247,10000,2500,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(248,10000,2500,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(249,10000,2500,0,30,25,150,100,null)));
         _loc1_.addEnemy(new WaveEnemyVO(1,new RoleArgVO(240,80000,2600,0,30,25,150,150,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(247,10000,2500,0,30,25,150,120,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(248,10000,2500,0,30,25,150,120,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(249,10000,2500,0,30,25,150,120,null)));
         _loc1_.addEnemy(new WaveEnemyVO(1,new RoleArgVO(241,80000,2600,0,30,25,150,150,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(247,10000,2500,0,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(248,10000,2500,0,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(249,10000,2500,0,30,25,150,130,null)));
         _loc1_.addEnemy(new WaveEnemyVO(1,new RoleArgVO(242,80000,2600,0,30,25,150,150,null)));
         this.waveData.addWave(_loc1_);
         this.peoples = [];
         this.peoples[this.peoples.length] = new RoleArgVO(800,2000,0,2975,10,0,0,7,null);
         this.peoples[this.peoples.length] = new RoleArgVO(800,2000,0,2975,10,0,0,3,null);
         this.peoples[this.peoples.length] = new RoleArgVO(800,2000,0,2975,10,0,0,9,null);
         this.peoples[this.peoples.length] = new RoleArgVO(800,2000,0,2975,10,0,0,8,null);
         this.peoples[this.peoples.length] = new RoleArgVO(800,2000,0,2975,10,0,0,4,null);
         this.peoples[this.peoples.length] = new RoleArgVO(800,2000,0,2975,10,0,0,5,null);
         this.peoples[this.peoples.length] = new RoleArgVO(800,2000,0,2975,10,0,0,6,null);
         this.peoples[this.peoples.length] = new RoleArgVO(800,2000,0,2975,10,0,0,7,null);
         this.peoples[this.peoples.length] = new RoleArgVO(800,2000,0,2975,10,0,0,8,null);
         this.peoples[this.peoples.length] = new RoleArgVO(800,2000,0,2975,10,0,0,10,null);
      }
      
      public function newReward(param1:Boolean) : Array
      {
         if(!param1)
         {
            return this.loseReward;
         }
         return this.winReward;
      }
      
      private function get winReward() : Array
      {
         var _loc2_:int = 0;
         var _loc6_:int = 0;
         var _loc1_:Array = [];
         var _loc3_:int = Math.random() * 100 + 1;
         if(_loc3_ <= 20)
         {
            _loc2_ = ConstData.INT1.v;
         }
         else if(_loc3_ <= 80)
         {
            _loc2_ = ConstData.INT2.v;
         }
         else
         {
            _loc2_ = ConstData.INT3.v;
         }
         var _loc4_:FubenVO = FubenConfig.instance().findFuben(604);
         var _loc5_:Array = _loc4_.drops.slice(1);
         var _loc7_:int = 0;
         while(_loc7_ < _loc2_)
         {
            _loc6_ = Math.random() * _loc5_.length;
            _loc1_[_loc1_.length] = _loc5_[_loc6_];
            _loc5_.splice(_loc6_,1);
            _loc7_++;
         }
         return _loc1_;
      }
      
      private function get loseReward() : Array
      {
         return [new BaseRewardVO(10000,35000)];
      }
   }
}

