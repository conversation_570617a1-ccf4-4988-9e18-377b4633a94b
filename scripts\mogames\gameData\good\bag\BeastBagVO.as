package mogames.gameData.good.bag
{
   import com.mogames.data.Oint;
   import com.mogames.event.GameSignal;
   import com.mogames.event.SignalManager;
   import com.mogames.sound.SoundManager;
   import com.mogames.utils.MathUtil;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.good.bag.base.GameBagVO;
   import mogames.gameData.good.base.ConstBagVO;
   import mogames.gameData.mission.BattleProxy;
   import mogames.gameData.mission.res.JSONResVO;
   import mogames.gameEffect.EffectManager;
   
   public class BeastBagVO extends GameBagVO
   {
      
      private var _num:Oint = new Oint();
      
      public function BeastBagVO(param1:ConstBagVO, param2:int)
      {
         super(param1);
         MathUtil.saveINT(this._num,param2);
      }
      
      override public function handerUse(param1:Function, param2:Function) : void
      {
         var _loc6_:JSONResVO = null;
         var _loc3_:Rectangle = BattleProxy.instance().dataMove.rectNear;
         var _loc4_:int = MathUtil.loadINT(this._num);
         var _loc5_:int = 0;
         while(_loc5_ < _loc4_)
         {
            _loc6_ = new JSONResVO();
            _loc6_.id = MathUtil.randomNum(301,303);
            _loc6_.posX = _loc3_.x + Math.random() * _loc3_.width;
            _loc6_.posY = _loc3_.y + Math.random() * _loc3_.height;
            if(_loc6_.posY >= 450)
            {
               _loc6_.posY -= 200;
            }
            EffectManager.addBMC("SEQ_COME_UP_CLIP",Layers.frontLayer,_loc6_.posX,_loc6_.posY);
            SignalManager.signalRes.dispatchEvent({
               "signal":GameSignal.ADD_RES,
               "resVO":_loc6_
            });
            _loc5_++;
         }
         SoundManager.instance().playAudio("AUDIO_BING_FU");
         super.handerUse(param1,param2);
      }
   }
}

