package mogames.gameData.good.bag
{
   import com.mogames.event.GameSignal;
   import com.mogames.event.SignalManager;
   import com.mogames.sound.SoundManager;
   import mogames.gameData.good.bag.base.GameBagVO;
   import mogames.gameData.good.base.ConstBagVO;
   import mogames.gameData.mission.BattleProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.MissionManager;
   import mogames.gameMission.base.BaseMission;
   
   public class ChaoFengBagVO extends GameBagVO
   {
      
      public function ChaoFengBagVO(param1:ConstBagVO)
      {
         super(param1);
      }
      
      override public function handerUse(param1:Function, param2:Function) : void
      {
         if(BattleProxy.instance().dataWave.isPause)
         {
            EffectManager.addPureText("倒计时暂停中，无法嘲讽！");
            if(param2 != null)
            {
               param2();
            }
            return;
         }
         if(BattleProxy.instance().dataWave.isTrain)
         {
            EffectManager.addPureText("敌方正在出战，无法嘲讽！");
            if(param2 != null)
            {
               param2();
            }
            return;
         }
         if(BattleProxy.instance().dataWave.lastWave)
         {
            EffectManager.addPureText("已是最后一波！");
            if(param2 != null)
            {
               param2();
            }
            return;
         }
         SoundManager.instance().playAudio("AUDIO_CHAO_FENG");
         SignalManager.signalBag.dispatchEvent({"signal":GameSignal.BAG_CHAOFENG});
         super.handerUse(param1,param2);
      }
      
      override public function get cd() : Number
      {
         var _loc1_:BaseMission = null;
         if(BattleProxy.instance().battleType == 9)
         {
            return 0;
         }
         if(BattleProxy.instance().battleType != 1 || !MissionManager.instance().curMission)
         {
            return _constBag.constCD;
         }
         if(MissionManager.instance().curMission is BaseMission)
         {
            _loc1_ = MissionManager.instance().curMission as BaseMission;
            if(_loc1_.flagVO.starNum >= 2)
            {
               return 0;
            }
         }
         return _constBag.constCD;
      }
   }
}

