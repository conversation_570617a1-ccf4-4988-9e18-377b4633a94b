package mogames.gameData.mission.data
{
   import com.mogames.data.Oint;
   import com.mogames.event.GameSignal;
   import com.mogames.event.SignalManager;
   import com.mogames.utils.MathUtil;
   import flash.geom.Rectangle;
   import mogames.gameData.game.LackVO;
   
   public class BattleSkillData
   {
      
      private var _totalMP:Oint = new Oint();
      
      private var _curMP:Oint = new Oint();
      
      public var skillRect:Rectangle;
      
      public function BattleSkillData()
      {
         super();
      }
      
      public function init() : void
      {
         this.initMaxMP(100);
      }
      
      public function initMaxMP(param1:int) : void
      {
         MathUtil.saveINT(this._totalMP,param1);
         MathUtil.saveINT(this._curMP,param1);
      }
      
      public function changeMP(param1:int) : void
      {
         var _loc2_:int = MathUtil.loadINT(this._curMP) + param1;
         if(_loc2_ <= 0)
         {
            _loc2_ = 0;
         }
         else if(_loc2_ >= MathUtil.loadINT(this._totalMP))
         {
            _loc2_ = MathUtil.loadINT(this._totalMP);
         }
         MathUtil.saveINT(this._curMP,_loc2_);
         SignalManager.signalUI.dispatchEvent({"signal":GameSignal.REFRESH_MP});
      }
      
      public function checkLack(param1:int) : LackVO
      {
         if(MathUtil.loadINT(this._curMP) < param1)
         {
            return new LackVO("法力不足！");
         }
         return null;
      }
      
      public function get mpPer() : Number
      {
         return MathUtil.loadINT(this._curMP) / MathUtil.loadINT(this._totalMP);
      }
      
      public function get mpStatus() : String
      {
         return MathUtil.loadINT(this._curMP) + "/" + MathUtil.loadINT(this._totalMP);
      }
   }
}

