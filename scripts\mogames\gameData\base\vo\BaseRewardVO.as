package mogames.gameData.base.vo
{
   import com.mogames.utils.TxtUtil;
   import file.GoodConfig;
   import mogames.ConstData;
   import mogames.gameData.good.base.ConstEquipVO;
   import mogames.gameData.good.base.ConstGoodVO;
   import mogames.gameData.good.vo.GameGoodVO;
   
   public class BaseRewardVO
   {
      
      protected var _goodVO:GameGoodVO;
      
      public function BaseRewardVO(param1:int, param2:int)
      {
         super();
         this.createGood(param1,param2);
      }
      
      protected function createGood(param1:int, param2:int) : void
      {
         this._goodVO = GoodConfig.instance().newGameGood(param1);
         if(this._goodVO)
         {
            this._goodVO.amount = param2;
         }
      }
      
      public function newGood(param1:int = 1) : GameGoodVO
      {
         var _loc2_:GameGoodVO = GoodConfig.instance().newGameGood(this._goodVO.constGood.id);
         _loc2_.amount = this._goodVO.amount * param1;
         return _loc2_;
      }
      
      public function get goodVO() : GameGoodVO
      {
         return this._goodVO;
      }
      
      public function get constGood() : ConstGoodVO
      {
         return this._goodVO.constGood;
      }
      
      public function get amount() : int
      {
         return this._goodVO.amount;
      }
      
      public function get tipID() : int
      {
         if(this.constGood is ConstEquipVO)
         {
            return 106;
         }
         return 102;
      }
      
      public function get tipGood() : *
      {
         return this._goodVO;
      }
      
      public function get colorInfor() : String
      {
         return TxtUtil.setColor(this.baseInfor,ConstData.GOOD_COLOR1[this._goodVO.quality]);
      }
      
      public function get baseInfor() : String
      {
         return this._goodVO.constGood.name + "X" + this._goodVO.amount;
      }
   }
}

