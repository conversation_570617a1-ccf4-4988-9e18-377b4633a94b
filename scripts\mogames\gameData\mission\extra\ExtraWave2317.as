package mogames.gameData.mission.extra
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ExtraWave2317
   {
      
      public function ExtraWave2317()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2317);
         _loc1_.limitBR = new WaveLimitVO(99999999,0.9,0.9);
         _loc1_.zhuBoss = new BossArgVO(833,7500000,55000,10000,80,80,300,90,new BossSkillData0(150,{
            "hurt":96500,
            "keepTime":5,
            "hurtCount":5
         },10),1001,0);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new <PERSON>ArgVO(754,300000,10000,100,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(765,1500000,25000,450,50,80,150,80,new BossSkillData0(250,{
            "hurt":68450,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(751,300000,10000,100,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(754,300000,10000,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(750,300000,10000,100,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(754,300000,10000,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(750,300000,10000,100,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(753,300000,10000,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(750,300000,10000,100,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(753,300000,10000,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(750,300000,10000,100,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(765,1500000,25000,450,50,80,150,80,new BossSkillData1(8,{"hurt":68450},2),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(750,300000,10000,100,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(753,300000,10000,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(750,300000,10000,100,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(753,300000,10000,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(750,300000,10000,100,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(765,1500000,25000,450,50,80,150,80,new BossSkillData1(8,{
            "hurt":68450,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(750,300000,10000,100,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(765,1500000,25000,450,50,80,150,80,new BossSkillData0(250,{"hurt":68450},2),1008,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

