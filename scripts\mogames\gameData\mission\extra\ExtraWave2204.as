package mogames.gameData.mission.extra
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ExtraWave2204
   {
      
      public function ExtraWave2204()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2204);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(816,2000000,10000,650,80,80,300,90,new BossSkillData0(200,{
            "hurt":25000,
            "keepTime":8,
            "hurtCount":6
         },7),1015,0);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(273,35000,3070,130,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(274,35000,3070,130,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(274,35000,3070,130,50,80,200,120,0)));
         _loc2_.addFu(new BossArgVO(276,550000,5300,450,50,80,150,80,new BossSkillData1(10,{
            "hurt":6000,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(272,35000,3070,130,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(275,35000,3070,130,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(273,35000,3070,130,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(274,35000,3070,130,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(272,35000,3070,130,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(275,35000,3070,130,50,80,200,120,0)));
         _loc2_.addFu(new BossArgVO(277,550000,5300,450,50,80,150,80,new BossSkillData1(10,{
            "hurt":7000,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(273,35000,3070,130,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(274,35000,3070,130,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(272,35000,3070,130,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(275,35000,3070,130,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(272,35000,3070,130,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(275,35000,3070,130,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(273,35000,3070,130,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(274,35000,3070,130,50,80,200,120,0)));
         _loc2_.addFu(new BossArgVO(278,300000,4800,450,50,80,150,80,new BossSkillData0(250,{"hurt":7000},3),1006,0));
         _loc2_.addFu(new BossArgVO(279,300000,4800,450,50,80,150,80,new BossSkillData0(250,{
            "hurt":7000,
            "keepTime":3
         },2),1014,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(273,35000,3070,130,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(275,35000,3070,130,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

