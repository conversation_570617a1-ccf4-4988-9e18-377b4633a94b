package mogames.gameData.good.bag
{
   import com.mogames.utils.TxtUtil;
   import mogames.gameData.good.bag.base.GameBagVO;
   import mogames.gameData.good.base.ConstBagVO;
   import mogames.gameData.mission.BattleProxy;
   import mogames.gameEffect.EffectManager;
   
   public class LGJBag<PERSON> extends GameBagVO
   {
      
      public function LGJBagVO(param1:ConstBagVO)
      {
         super(param1);
      }
      
      override public function handerUse(param1:Function, param2:Function) : void
      {
         if(BattleProxy.instance().dataSkill.mpPer >= 1)
         {
            EffectManager.addPureText("当前无需回复法力！");
            if(param2 != null)
            {
               param2();
            }
            return;
         }
         super.handerUse(param1,param2);
         BattleProxy.instance().dataSkill.changeMP(50);
         EffectManager.addPureText(TxtUtil.setColor("法力回复50点！","33CCFF"),20,480,300,"AUDIO_STAR");
      }
   }
}

