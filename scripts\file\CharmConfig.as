package file
{
   import mogames.gameData.base.func.CharmVO;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.base.vo.NeedVO;
   
   public class CharmConfig
   {
      
      private static var _instance:CharmConfig;
      
      private var _jineng:Array;
      
      private var _fuzhu:Array;
      
      private var _zhaohuan:Array;
      
      public function CharmConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : CharmConfig
      {
         if(!_instance)
         {
            _instance = new CharmConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._jineng = [];
         this._jineng[this._jineng.length] = new CharmVO(new BaseRewardVO(11301,1),[new NeedVO(10401,1),new NeedVO(10410,1),new NeedVO(10407,2)]);
         this._jineng[this._jineng.length] = new CharmVO(new BaseRewardVO(11302,1),[new NeedVO(11301,2),new NeedVO(10411,2)]);
         this._fuzhu = [];
         this._fuzhu[this._fuzhu.length] = new CharmVO(new BaseRewardVO(11071,1),[new NeedVO(10401,1),new NeedVO(10406,1),new NeedVO(10407,2)]);
         this._fuzhu[this._fuzhu.length] = new CharmVO(new BaseRewardVO(11002,1),[new NeedVO(10401,1),new NeedVO(10406,1),new NeedVO(10404,1),new NeedVO(10405,2)]);
         this._fuzhu[this._fuzhu.length] = new CharmVO(new BaseRewardVO(11003,1),[new NeedVO(11002,1),new NeedVO(10412,2)]);
         this._fuzhu[this._fuzhu.length] = new CharmVO(new BaseRewardVO(11008,1),[new NeedVO(10401,1),new NeedVO(10406,1),new NeedVO(10408,1),new NeedVO(10407,1)]);
         this._fuzhu[this._fuzhu.length] = new CharmVO(new BaseRewardVO(11601,1),[new NeedVO(10401,1),new NeedVO(10418,2),new NeedVO(10419,2)]);
         this._fuzhu[this._fuzhu.length] = new CharmVO(new BaseRewardVO(11602,1),[new NeedVO(11601,1),new NeedVO(10420,1),new NeedVO(10421,1)]);
         this._zhaohuan = [];
         this._zhaohuan[this._zhaohuan.length] = new CharmVO(new BaseRewardVO(11101,1),[new NeedVO(10401,1),new NeedVO(10405,1),new NeedVO(10406,2)]);
         this._zhaohuan[this._zhaohuan.length] = new CharmVO(new BaseRewardVO(11204,1),[new NeedVO(10401,1),new NeedVO(10402,1),new NeedVO(10409,2)]);
         this._zhaohuan[this._zhaohuan.length] = new CharmVO(new BaseRewardVO(11205,1),[new NeedVO(11204,1),new NeedVO(10402,1),new NeedVO(10413,2)]);
         this._zhaohuan[this._zhaohuan.length] = new CharmVO(new BaseRewardVO(11157,1),[new NeedVO(10401,1),new NeedVO(10403,1),new NeedVO(10409,2)]);
         this._zhaohuan[this._zhaohuan.length] = new CharmVO(new BaseRewardVO(11158,1),[new NeedVO(11157,1),new NeedVO(10416,2),new NeedVO(10417,2)]);
         this._zhaohuan[this._zhaohuan.length] = new CharmVO(new BaseRewardVO(11151,1),[new NeedVO(10401,1),new NeedVO(10415,1),new NeedVO(10408,2)]);
      }
      
      public function findCharms(param1:int) : Array
      {
         switch(param1)
         {
            case 0:
               return this._jineng;
            case 1:
               return this._fuzhu;
            case 2:
               return this._zhaohuan;
            default:
               return [];
         }
      }
   }
}

