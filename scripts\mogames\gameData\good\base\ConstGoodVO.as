package mogames.gameData.good.base
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import com.mogames.utils.TxtUtil;
   import mogames.ConstData;
   
   public class ConstGoodVO extends IconVO
   {
      
      protected var _id:Oint = new Oint();
      
      protected var _price:Oint = new Oint();
      
      protected var _quality:Oint = new Oint();
      
      protected var _pile:Oint = new Oint();
      
      protected var _use:int;
      
      protected var _getInfor:String;
      
      public function ConstGoodVO(param1:int, param2:int, param3:int, param4:int, param5:int, param6:String, param7:String, param8:String, param9:String)
      {
         super(param6,param7,param8);
         MathUtil.saveINT(this._id,param1);
         MathUtil.saveINT(this._price,param4);
         MathUtil.saveINT(this._quality,param2);
         MathUtil.saveINT(this._pile,param3);
         this._use = param5;
         this._getInfor = param9;
      }
      
      public function get label() : int
      {
         return int(String(this.id).charAt(0));
      }
      
      public function get type() : String
      {
         return ConstData.GOOD_TYPE[this.label - 1];
      }
      
      public function get id() : int
      {
         return MathUtil.loadINT(this._id);
      }
      
      public function get quality() : int
      {
         return MathUtil.loadINT(this._quality);
      }
      
      public function get price() : int
      {
         return MathUtil.loadINT(this._price);
      }
      
      public function get isPile() : Boolean
      {
         return MathUtil.loadINT(this._pile) == 1;
      }
      
      public function get isUse() : Boolean
      {
         return this._use != 0;
      }
      
      public function get priceStr() : String
      {
         if(this.price == 0)
         {
            return "无法出售";
         }
         return "" + this.price + "银票";
      }
      
      public function get getStr() : String
      {
         return this._getInfor;
      }
      
      public function get qualityName() : String
      {
         return TxtUtil.setColor(name,ConstData.GOOD_COLOR1[this.quality]);
      }
   }
}

