package mogames.gameData.box.vo
{
   import com.mogames.sound.SoundManager;
   import com.mogames.utils.MathUtil;
   import com.mogames.utils.TxtUtil;
   import mogames.gameData.box.base.GameBoxVO;
   import mogames.gameData.mission.BattleProxy;
   
   public class FoodBoxVO extends GameBoxVO
   {
      
      public function FoodBoxVO(param1:Number, param2:int, param3:Object)
      {
         super(param1,param2,param3);
      }
      
      override public function handlerOpen() : String
      {
         var _loc1_:int = MathUtil.randomNum(findArg("min"),findArg("max"));
         BattleProxy.instance().dataRes.changeFood(_loc1_);
         SoundManager.instance().playAudio("AUDIO_REWARD");
         return TxtUtil.setColor("获得食物X" + _loc1_,"99FF00");
      }
   }
}

