package mogames.gameData.bag.base
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import file.BagConfig;
   import mogames.gameData.master.BookProxy;
   import mogames.gameData.skill.base.BookSkillVO;
   
   public class BookBagVO
   {
      
      private var _bagID:Oint = new Oint();
      
      private var _skillID:Oint = new Oint();
      
      private var _lockVO:BagLockVO;
      
      public function BookBagVO(param1:int)
      {
         super();
         MathUtil.saveINT(this._bagID,param1);
         MathUtil.saveINT(this._skillID,0);
         this._lockVO = BagConfig.instance().findLock(param1);
      }
      
      public function setSkillID(param1:int) : void
      {
         MathUtil.saveINT(this._skillID,param1);
      }
      
      public function get bagID() : int
      {
         return MathUtil.loadINT(this._bagID);
      }
      
      public function get isOpen() : Boolean
      {
         return this._lockVO.isOpen;
      }
      
      public function get lockVO() : BagLockVO
      {
         return this._lockVO;
      }
      
      public function get skillID() : int
      {
         return MathUtil.loadINT(this._skillID);
      }
      
      public function get skillVO() : BookSkillVO
      {
         return BookProxy.instance().findSkill(this.skillID);
      }
   }
}

