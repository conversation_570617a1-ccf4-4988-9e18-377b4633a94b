package mogames.gameData.achieve.vo
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import file.AreaConfig;
   import mogames.gameData.achieve.base.AchieveLockVO;
   
   public class AreaAchieveVO extends AchieveLockVO
   {
      
      private var _area:Oint = new Oint();
      
      public function AreaAchieveVO(param1:int, param2:int, param3:Array, param4:String, param5:String)
      {
         super(param1,param3,param4,param5);
         MathUtil.saveINT(this._area,param2);
      }
      
      override public function checkOpen(param1:int = 1) : void
      {
         if(isOpen)
         {
            return;
         }
         MathUtil.saveINT(_open,int(AreaConfig.instance().findArea(MathUtil.loadINT(this._area)).isFinish));
         dispatchTip();
      }
   }
}

