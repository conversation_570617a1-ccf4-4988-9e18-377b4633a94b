package mogames.gameData.mission.extra
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ExtraWave1256
   {
      
      public function ExtraWave1256()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1256);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(641,375000,2800,600,80,80,300,90,new Boss<PERSON>killData0(150,{
            "hurt":3500,
            "hurtCount":5
         },5),0,1);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(108,5400,940,130,50,80,200,120,{
            "rate":100,
            "hurtPer":20,
            "keepTime":5
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(104,7400,940,95,50,80,200,120,{
            "rate":100,
            "curePer":40
         })));
         _loc2_.addFu(new BossArgVO(212,85000,3300,400,50,80,150,80,new BossSkillData1(150,{
            "hurt":1153,
            "keepTime":8,
            "hurtCount":6
         },2),1015,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(102,7400,940,95,50,80,200,120,{
            "rate":100,
            "hurtBei":1.6,
            "atkPer":50,
            "keepTime":5
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(104,7400,940,95,50,80,200,120,{
            "rate":100,
            "curePer":40
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(102,7400,940,95,50,80,200,120,{
            "rate":100,
            "hurtBei":1.6,
            "atkPer":50,
            "keepTime":5
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(104,7400,940,95,50,80,200,120,{
            "rate":100,
            "curePer":40
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(12,new RoleArgVO(104,7400,940,95,50,80,200,120,{
            "rate":100,
            "curePer":40
         })));
         _loc2_.addFu(new BossArgVO(201,85000,3300,400,50,80,150,80,new BossSkillData1(8,{
            "hurt":1550,
            "keepTime":3
         },2),1007,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(109,7400,940,95,50,80,200,120,{
            "rate":100,
            "hurtPer":60,
            "spdPer":50,
            "keepTime":3
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(104,7400,940,95,50,80,200,120,{
            "rate":100,
            "curePer":40
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(106,7400,940,95,50,80,200,120,{"atkPer":15})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(106,7400,940,95,50,80,200,120,{"atkPer":15})));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(104,7400,940,95,50,80,200,120,{
            "rate":100,
            "curePer":40
         })));
         _loc2_.addFu(new BossArgVO(207,85000,3300,400,50,80,150,80,new BossSkillData1(8,{
            "hurt":1650,
            "keepTime":3
         },2),1007,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(108,5400,940,130,50,80,200,120,{
            "rate":100,
            "hurtPer":20,
            "keepTime":5
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(104,7400,940,95,50,80,200,120,{
            "rate":100,
            "curePer":40
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(104,7100,910,95,50,80,200,120,{
            "rate":100,
            "curePer":40
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(104,7400,940,95,50,80,200,120,{
            "rate":100,
            "curePer":40
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(109,7100,910,95,50,80,200,120,{
            "rate":100,
            "hurtPer":60,
            "spdPer":50,
            "keepTime":3
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(104,7400,940,95,50,80,200,120,{
            "rate":100,
            "curePer":40
         })));
         _loc2_.addFu(new BossArgVO(204,85000,3300,400,50,80,150,80,new BossSkillData0(150,{
            "hurt":1560,
            "roleNum":4
         },2),1004,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

