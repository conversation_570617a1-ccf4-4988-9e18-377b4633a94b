package mogames.gameBuff.buff
{
   import mogames.gameBuff.base.TimeRoleBuff;
   import mogames.gameData.buff.ConstBuffVO;
   
   public class HLZTBuff extends TimeRoleBuff
   {
      
      private var _miss:int;
      
      private var _crit:int;
      
      public function HLZTBuff(param1:ConstBuffVO)
      {
         super(param1);
      }
      
      override protected function createData() : void
      {
         this._miss = _buffVO.args.missValue;
         this._crit = _buffVO.args.critValue;
         _owner.roleVO.skillMISS += this._miss;
         _owner.roleVO.skillCRIT += this._crit;
         _owner.roleVO.updateMISS();
         _owner.roleVO.updateCRIT();
      }
      
      override protected function onCleanRole() : void
      {
         _owner.roleVO.skillMISS -= this._miss;
         _owner.roleVO.skillCRIT -= this._crit;
         _owner.roleVO.updateMISS();
         _owner.roleVO.updateCRIT();
      }
   }
}

