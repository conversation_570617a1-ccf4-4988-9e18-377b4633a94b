package file
{
   import com.mogames.utils.MathUtil;
   import mogames.ConstData;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.fuben.vo.FubenVO;
   import mogames.gameData.master.MasterProxy;
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.RoleArgVO;
   
   public class HuLunConfig
   {
      
      private static var _instance:HuLunConfig;
      
      public var argVO:RoleArgVO;
      
      public function HuLunConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : HuLunConfig
      {
         if(!_instance)
         {
            _instance = new HuLunConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this.argVO = new RoleArgVO(166,800,2000,99999,30,10,250,130,null);
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2700);
         _loc1_.limitBR = new WaveLimitVO(9000,5,5);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(248,19000,650,0,30,25,150,100,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(248,19000,650,0,30,25,150,100,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(249,19000,650,0,30,25,150,100,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(247,19000,650,0,30,25,150,100,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(247,19000,650,0,30,25,150,100,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(249,19000,650,0,30,25,150,100,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(250,19000,650,0,30,25,150,100,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(250,19000,650,0,30,25,150,100,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(247,19000,650,0,30,25,150,100,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(248,19000,650,0,30,25,150,100,null)));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
      
      public function newReward(param1:Boolean) : Array
      {
         if(!param1)
         {
            return this.loseReward;
         }
         return this.winReward;
      }
      
      private function get winReward() : Array
      {
         var _loc6_:int = 0;
         var _loc1_:Array = [];
         var _loc2_:int = MasterProxy.instance().masterVO.level * MathUtil.randomNum(350,550);
         _loc1_[0] = new BaseRewardVO(10000,_loc2_);
         var _loc3_:int = ConstData.INT1.v;
         if(MathUtil.checkOdds(100))
         {
            _loc3_ = ConstData.INT2.v;
         }
         var _loc4_:FubenVO = FubenConfig.instance().findFuben(603);
         var _loc5_:Array = _loc4_.drops.slice(1);
         var _loc7_:int = 0;
         while(_loc7_ < _loc3_)
         {
            _loc6_ = Math.random() * _loc5_.length;
            _loc1_[_loc1_.length] = _loc5_[_loc6_];
            _loc5_.splice(_loc6_,1);
            _loc7_++;
         }
         return _loc1_;
      }
      
      private function get loseReward() : Array
      {
         return [new BaseRewardVO(10000,30000)];
      }
   }
}

