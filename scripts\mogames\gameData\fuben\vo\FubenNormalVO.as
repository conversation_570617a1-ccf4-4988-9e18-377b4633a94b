package mogames.gameData.fuben.vo
{
   import com.mogames.utils.TxtUtil;
   import file.MoneyFuncConfig;
   import mogames.gameData.mall.vo.MallFuncVO;
   import mogames.gameData.master.MasterProxy;
   import mogames.gameData.role.HeroProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameNet.SaveManager;
   import mogames.gameUI.quick.QuickBuyFrame;
   
   public class FubenNormalVO extends FubenVO
   {
      
      protected var _require:Object;
      
      public function FubenNormalVO(param1:int, param2:int, param3:Object, param4:Array, param5:String)
      {
         super(param1,param1 * 10,param1 * 10 + 1,param2,param4,param5 + "<br>每天默认挑战2次；<br>可消耗10金票购买次数（无购买限制）。");
         this._require = param3;
      }
      
      override public function handlerBuy(param1:Function) : void
      {
         var confirmBuy:Function = null;
         var okFunc:Function = param1;
         confirmBuy = function(param1:int):void
         {
            numFlag.changeValue(param1);
            okFunc();
         };
         if(!numFlag.isComplete)
         {
            EffectManager.addPureText("挑战次数未用完，无需购买！");
            return;
         }
         QuickBuyFrame.instance().init(this.buyVO,confirmBuy);
      }
      
      override public function handlerEnter(param1:Boolean = true) : void
      {
         numFlag.changeValue(-1);
         if(param1)
         {
            SaveManager.instance().saveAuto();
         }
      }
      
      override public function get isOpen() : Boolean
      {
         if(Sanx.isLocal || !this._require)
         {
            return true;
         }
         var _loc1_:int = int(this._require.level);
         var _loc2_:int = int(this._require.num);
         return MasterProxy.instance().masterVO.level >= _loc1_ && HeroProxy.instance().ownerHeros.length >= _loc2_;
      }
      
      override public function get enterTip() : String
      {
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         var _loc3_:String = null;
         if(!this.isOpen)
         {
            _loc1_ = int(this._require.level);
            _loc2_ = int(this._require.num);
            _loc3_ = "开启条件：";
            if(_loc1_ != 0)
            {
               _loc3_ += "主公等级达到" + _loc1_ + "级<br>";
            }
            if(_loc2_ != 0)
            {
               _loc3_ += "拥有" + _loc2_ + "名以上武将";
            }
            return _loc3_;
         }
         return leftInfor;
      }
      
      override public function get buyTip() : String
      {
         return "花费" + this.buyVO.price + "金票购买1次挑战次数。<br>";
      }
      
      protected function get buyVO() : MallFuncVO
      {
         return MoneyFuncConfig.instance().buyFuben;
      }
      
      public function get sdTip() : String
      {
         var _loc1_:String = "解锁条件：<br>";
         _loc1_ += TxtUtil.setColor("一、通关一次该副本；<br>","ffff00");
         return _loc1_ + TxtUtil.setColor("二、主公等级达到" + this.sdLevel + "级。","ffff00");
      }
      
      public function get canSD() : Boolean
      {
         if(MasterProxy.instance().masterVO.level < this.sdLevel)
         {
            return false;
         }
         if(!winFlag.isComplete)
         {
            return false;
         }
         return true;
      }
      
      protected function get sdLevel() : int
      {
         return Math.min(MasterProxy.instance().masterVO.maxLevel,this._require.level + 5);
      }
   }
}

