package mogames.gameData.good.base
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import com.mogames.utils.TxtUtil;
   import mogames.gameData.master.MasterProxy;
   
   public class ConstLvPKGVO extends ConstPKGVO
   {
      
      protected var _level:Oint = new Oint();
      
      public function ConstLvPKGVO(param1:int, param2:int, param3:int, param4:int, param5:String, param6:String, param7:String, param8:String)
      {
         super(param1,param2,param4,param5,param6,param7,param8);
         MathUtil.saveINT(this._level,param3);
      }
      
      public function get level() : int
      {
         return MathUtil.loadINT(this._level);
      }
      
      override public function get canOpen() : Boolean
      {
         return MasterProxy.instance().masterVO.level >= this.level;
      }
      
      override public function get infor() : String
      {
         var _loc1_:String = this.canOpen ? "99FF00" : "FF0000";
         return _infor + "<br>" + TxtUtil.setColor(TxtUtil.addKuoHao("打开条件：主公达到" + this.level + "级"),_loc1_);
      }
   }
}

