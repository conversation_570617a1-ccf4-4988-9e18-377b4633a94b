package mogames.gameData.achieve.base
{
   import com.mogames.utils.MathUtil;
   import mogames.gameData.role.HeroProxy;
   import mogames.gameData.role.hero.HeroGameVO;
   
   public class Achieve417VO extends AchieveLockVO
   {
      
      public function Achieve417VO(param1:int, param2:Array, param3:String, param4:String)
      {
         super(param1,param2,param3,param4);
      }
      
      override public function get isOpen() : Bo<PERSON>an
      {
         var _loc2_:HeroGameVO = null;
         var _loc1_:Array = HeroProxy.instance().ownerHeros;
         for each(_loc2_ in _loc1_)
         {
            if(_loc2_.heroInfo.sex == 0)
            {
               return true;
            }
         }
         return Boolean(MathUtil.loadINT(_open));
      }
   }
}

