package file
{
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class TZQLConfig
   {
      
      private static var _instance:TZQLConfig;
      
      public var activeIndex:int;
      
      public var waveData:WaveDataVO;
      
      private var _rewards:Array;
      
      private var _shows:Array;
      
      public function TZQLConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : TZQLConfig
      {
         if(!_instance)
         {
            _instance = new TZQLConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         var _loc1_:OneWaveVO = null;
         this._rewards = [];
         this._rewards[this._rewards.length] = [new BaseRewardVO(10000,50000),new BaseRewardVO(10450,1),new BaseRewardVO(10450,2),new BaseRewardVO(10451,1),new BaseRewardVO(10451,1),new BaseRewardVO(10451,2),new BaseRewardVO(10452,1),new BaseRewardVO(10452,1),new BaseRewardVO(10452,2)];
         this._rewards[this._rewards.length] = [new BaseRewardVO(10000,50000),new BaseRewardVO(10450,1),new BaseRewardVO(10450,2),new BaseRewardVO(10454,1),new BaseRewardVO(10454,1),new BaseRewardVO(10454,2),new BaseRewardVO(10453,1),new BaseRewardVO(10453,1),new BaseRewardVO(10453,2)];
         this._rewards[this._rewards.length] = [new BaseRewardVO(10000,50000),new BaseRewardVO(10934,1),new BaseRewardVO(10935,1),new BaseRewardVO(10936,1),new BaseRewardVO(10937,1),new BaseRewardVO(10931,1),new BaseRewardVO(10932,1),new BaseRewardVO(10933,1),new BaseRewardVO(10934,1),new BaseRewardVO(10935,1),new BaseRewardVO(10936,1),new BaseRewardVO(10937,1),new BaseRewardVO(10931,1),new BaseRewardVO(10932,1),new BaseRewardVO(10933,1),new BaseRewardVO(10931,2),new BaseRewardVO(10932,2),new BaseRewardVO(10933,2),new BaseRewardVO(10934,2),new BaseRewardVO(10935,2),new BaseRewardVO(10936,2),new BaseRewardVO(10937,2)];
         this._rewards[this._rewards.length] = [new BaseRewardVO(10000,50000),new BaseRewardVO(10461,1),new BaseRewardVO(10462,1),new BaseRewardVO(10463,1),new BaseRewardVO(10464,1),new BaseRewardVO(10465,1),new BaseRewardVO(10461,1),new BaseRewardVO(10462,1),new BaseRewardVO(10463,1),new BaseRewardVO(10464,1),new BaseRewardVO(10465,1),new BaseRewardVO(10461,2),new BaseRewardVO(10462,2),new BaseRewardVO(10463,2),new BaseRewardVO(10464,2),new BaseRewardVO(10465,2)];
         this._rewards[this._rewards.length] = [new BaseRewardVO(10000,50000),new BaseRewardVO(10450,1),new BaseRewardVO(10450,2),new BaseRewardVO(10457,1),new BaseRewardVO(10457,1),new BaseRewardVO(10457,2),new BaseRewardVO(10458,1),new BaseRewardVO(10458,1),new BaseRewardVO(10458,2)];
         this._rewards[this._rewards.length] = [new BaseRewardVO(10000,50000),new BaseRewardVO(10450,1),new BaseRewardVO(10450,2),new BaseRewardVO(10459,1),new BaseRewardVO(10459,1),new BaseRewardVO(10459,2),new BaseRewardVO(10460,1),new BaseRewardVO(10460,1),new BaseRewardVO(10460,2)];
         this._shows = [];
         this._shows[this._shows.length] = [new BaseRewardVO(10000,1),new BaseRewardVO(10450,1),new BaseRewardVO(10451,1),new BaseRewardVO(10452,1)];
         this._shows[this._shows.length] = [new BaseRewardVO(10000,1),new BaseRewardVO(10450,1),new BaseRewardVO(10453,1),new BaseRewardVO(10454,1)];
         this._shows[this._shows.length] = [new BaseRewardVO(10000,1),new BaseRewardVO(10931,1),new BaseRewardVO(10932,1),new BaseRewardVO(10933,1),new BaseRewardVO(10934,1),new BaseRewardVO(10935,1),new BaseRewardVO(10936,1),new BaseRewardVO(10937,1)];
         this._shows[this._shows.length] = [new BaseRewardVO(10000,1),new BaseRewardVO(10461,1),new BaseRewardVO(10462,1),new BaseRewardVO(10463,1),new BaseRewardVO(10464,1),new BaseRewardVO(10465,1)];
         this._shows[this._shows.length] = [new BaseRewardVO(10000,1),new BaseRewardVO(10450,1),new BaseRewardVO(10457,1),new BaseRewardVO(10458,1)];
         this._shows[this._shows.length] = [new BaseRewardVO(10000,1),new BaseRewardVO(10450,1),new BaseRewardVO(10459,1),new BaseRewardVO(10460,1)];
         this.waveData = new WaveDataVO(0);
         this.waveData.limitBR = new WaveLimitVO(0,1.2,1.3);
         this.waveData.zhuBoss = new BossArgVO(1004,99999999,7000,1200,150,150,800,150,new BossSkillData1(15,{
            "hurt":15000,
            "keepHurt":50
         },999),4005,0);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(266,50000,5000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(268,50000,5000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(270,50000,5000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(271,50000,5000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(266,75000,4000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(268,75000,4000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(270,75000,4000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(271,75000,4000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(266,100000,5000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(268,100000,5000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(270,100000,5000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(271,100000,5000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(266,125000,6000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(268,125000,6000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(270,125000,6000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(271,125000,6000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(266,150000,7000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(268,150000,7000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(270,150000,7000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(271,150000,7000,500,30,25,150,130,null)));
         this.waveData.addWave(_loc1_);
      }
      
      public function get showList() : Array
      {
         return this._shows;
      }
      
      public function findList(param1:int) : Array
      {
         return this._rewards[param1];
      }
      
      public function newReward(param1:int) : Array
      {
         var _loc2_:Array = this.findList(this.activeIndex).slice();
         if(param1 <= 50)
         {
            return _loc2_.slice(0,1);
         }
         if(param1 <= 800)
         {
            return this.randomRewards(1,_loc2_);
         }
         if(param1 <= 3000)
         {
            return this.randomRewards(2,_loc2_);
         }
         if(param1 <= 8000)
         {
            return this.randomRewards(3,_loc2_);
         }
         return this.randomRewards(4,_loc2_);
      }
      
      private function randomRewards(param1:int, param2:Array) : Array
      {
         var _loc4_:int = 0;
         var _loc3_:Array = [];
         var _loc5_:int = 0;
         while(_loc5_ < param1)
         {
            _loc4_ = Math.random() * param2.length;
            _loc3_[_loc3_.length] = param2[_loc4_];
            param2.splice(_loc4_,1);
            _loc5_++;
         }
         return _loc3_;
      }
   }
}

