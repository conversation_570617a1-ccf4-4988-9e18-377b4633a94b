package mogames.gameAsset
{
   import com.mogames.data.ImageInfo;
   import com.mogames.display.BitmapBtn;
   import com.mogames.utils.ConvertUtil;
   import com.mogames.utils.MethodUtil;
   import file.AssetConfig;
   import file.RoleConfig;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.text.TextField;
   import flash.utils.Dictionary;
   import mogames.gameAsset.co.AssetTempDic;
   import mogames.gameData.asset.URLAssetVO;
   import mogames.gameData.mission.res.ConstBeastVO;
   import mogames.gameData.role.base.SkinVO;
   import mogames.gameUI.item.RectTip;
   import mogames.gameUI.load.CacheLoader;
   
   public class AssetManager
   {
      
      private static var _classRes:Dictionary;
      
      private static var _imageRes:Dictionary;
      
      private static var _aniRes:Dictionary;
      
      private static var _seqRoles:Dictionary;
      
      private static var _seqBeasts:Dictionary;
      
      private static var _seqTaverns:Dictionary;
      
      public static var assetTemp:AssetTempDic;
      
      public function AssetManager()
      {
         super();
      }
      
      public static function init() : void
      {
         _classRes = new Dictionary();
         _imageRes = new Dictionary();
         _aniRes = new Dictionary();
         _seqRoles = new Dictionary();
         _seqBeasts = new Dictionary();
         _seqTaverns = new Dictionary();
         assetTemp = new AssetTempDic();
      }
      
      public static function saveClass(param1:String, param2:Object) : void
      {
         if(_classRes[param1])
         {
            return;
         }
         _classRes[param1] = param2;
      }
      
      public static function findClass(param1:String) : Object
      {
         return _classRes[param1];
      }
      
      public static function checkAsset(param1:String, param2:Function, param3:int = 1) : void
      {
         var vo:URLAssetVO = null;
         var func:Function = null;
         var name:String = param1;
         var okFunc:Function = param2;
         var $uiType:int = param3;
         func = function():void
         {
            vo.loaded = true;
            okFunc();
         };
         vo = AssetConfig.instance().findURL(name);
         if(vo.loaded)
         {
            okFunc();
         }
         else
         {
            CacheLoader.instance().load(vo.urls,func,$uiType);
         }
      }
      
      public static function newMCRes(param1:String, param2:Number = 0, param3:Number = 0) : MovieClip
      {
         var _loc4_:Class = findClass(param1) as Class;
         var _loc5_:MovieClip = new _loc4_() as MovieClip;
         _loc5_.x = param2;
         _loc5_.y = param3;
         _loc5_.gotoAndStop(1);
         return _loc5_;
      }
      
      public static function newBtnRes(param1:String, param2:Number = 0, param3:Number = 0) : SimpleButton
      {
         var _loc4_:Class = findClass(param1) as Class;
         var _loc5_:SimpleButton = new _loc4_() as SimpleButton;
         _loc5_.x = param2;
         _loc5_.y = param3;
         return _loc5_;
      }
      
      public static function newBitmapBtn(param1:String, param2:Number = 0, param3:Number = 0) : BitmapBtn
      {
         var _loc4_:Vector.<ImageInfo> = findAniRes(param1);
         var _loc5_:BitmapBtn = new BitmapBtn(_loc4_);
         _loc5_.x = param2;
         _loc5_.y = param3;
         return _loc5_;
      }
      
      public static function newBitmap(param1:String = "", param2:Number = 0, param3:Number = 0) : Bitmap
      {
         var _loc4_:Bitmap = new Bitmap();
         _loc4_.x = param2;
         _loc4_.y = param3;
         if(param1)
         {
            _loc4_.bitmapData = findPicRes(param1);
         }
         return _loc4_;
      }
      
      public static function newText(param1:Number = 0, param2:Number = 0) : TextField
      {
         var _loc3_:TextField = new TextField();
         _loc3_.x = param1;
         _loc3_.y = param2;
         return _loc3_;
      }
      
      public static function newEmptyTip(param1:int, param2:int, param3:int, param4:int, param5:int = 0) : RectTip
      {
         var _loc6_:RectTip = new RectTip(param1,param2);
         _loc6_.x = param3;
         _loc6_.y = param4;
         _loc6_.setTipID(param5);
         return _loc6_;
      }
      
      public static function findAniRes(param1:String) : Vector.<ImageInfo>
      {
         if(!_aniRes[param1])
         {
            _aniRes[param1] = ConvertUtil.convertMC(newMCRes(param1));
         }
         return _aniRes[param1];
      }
      
      public static function findPicRes(param1:String) : BitmapData
      {
         var _loc2_:Class = null;
         if(!_imageRes[param1])
         {
            _loc2_ = findClass(param1) as Class;
            if(_loc2_ == null)
            {
            }
            _imageRes[param1] = _loc2_ == null ? null : new _loc2_();
         }
         return _imageRes[param1];
      }
      
      public static function findUI(param1:String, param2:Sprite) : Object
      {
         return MethodUtil.transMC(newMCRes(param1),param2);
      }
      
      public static function findRoleSeq(param1:int) : Array
      {
         var _loc2_:SkinVO = null;
         if(_seqRoles[param1] == null)
         {
            _loc2_ = RoleConfig.instance().findSkin(param1);
            _seqRoles[param1] = SeqRoleUtil.newSeq(_loc2_.bodySkin);
         }
         return _seqRoles[param1];
      }
      
      public static function findPMSeq(param1:SkinVO) : Array
      {
         if(_seqRoles[param1.skinID] == null)
         {
            _seqRoles[param1.skinID] = SeqPingMinUtil.newSeq(param1.bodySkin);
         }
         return _seqRoles[param1.skinID];
      }
      
      public static function findBeastSeq(param1:ConstBeastVO) : Array
      {
         if(!_seqBeasts[param1.id])
         {
            _seqBeasts[param1.id] = SeqBeastUtil.newSeq(param1.skin);
         }
         return _seqBeasts[param1.id];
      }
      
      public static function findTavernSeq(param1:int) : Array
      {
         if(!_seqTaverns[param1])
         {
            _seqTaverns[param1] = SeqTavernUtil.newSeq(param1);
         }
         return _seqTaverns[param1];
      }
   }
}

