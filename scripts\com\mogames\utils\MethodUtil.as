package com.mogames.utils
{
   import com.mogames.display.BMCSprite;
   import com.mogames.display.BaseSprite;
   import flash.display.DisplayObject;
   import flash.display.DisplayObjectContainer;
   import flash.display.InteractiveObject;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   
   public class MethodUtil
   {
      
      public function MethodUtil()
      {
         super();
      }
      
      public static function sortYIndex(param1:Sprite) : void
      {
         var _loc5_:DisplayObject = null;
         var _loc2_:int = param1.numChildren;
         var _loc3_:Array = [];
         var _loc4_:int = 0;
         while(_loc4_ < _loc2_)
         {
            _loc5_ = param1.getChildAt(_loc4_) as DisplayObject;
            if(_loc5_)
            {
               _loc3_[_loc3_.length] = {
                  "mc":_loc5_,
                  "mcy":_loc5_.y
               };
            }
            _loc4_++;
         }
         _loc3_.sortOn("mcy",Array.NUMERIC);
         _loc2_ = int(_loc3_.length);
         _loc4_ = 0;
         while(_loc4_ < _loc2_)
         {
            param1.setChildIndex(_loc3_[_loc4_].mc,_loc4_);
            _loc4_++;
         }
      }
      
      public static function drawRect(param1:int, param2:int, param3:Number = 1, param4:uint = 0) : BaseSprite
      {
         var _loc5_:BaseSprite = new BaseSprite();
         _loc5_.graphics.beginFill(param4,1);
         _loc5_.graphics.drawRect(0,0,param1,param2);
         _loc5_.graphics.endFill();
         _loc5_.alpha = param3;
         return _loc5_;
      }
      
      public static function drawLineRect(param1:int, param2:int, param3:Number = 1, param4:uint = 0) : BaseSprite
      {
         var _loc5_:BaseSprite = new BaseSprite();
         _loc5_.graphics.lineStyle(param3,param4);
         _loc5_.graphics.drawRect(0,0,param1,param2);
         return _loc5_;
      }
      
      public static function toggle(param1:SimpleButton) : void
      {
         var _loc2_:DisplayObject = param1.upState;
         param1.upState = param1.downState;
         param1.downState = _loc2_;
      }
      
      public static function setMousable(param1:DisplayObjectContainer, param2:Boolean) : void
      {
         param1.mouseChildren = param2;
         param1.mouseEnabled = param2;
      }
      
      public static function addChildArr(param1:Array, param2:DisplayObjectContainer) : void
      {
         var _loc3_:int = 0;
         var _loc4_:int = int(param1.length);
         while(_loc3_ < _loc4_)
         {
            param2.addChild(param1[_loc3_]);
            _loc3_++;
         }
      }
      
      public static function removeMe(param1:DisplayObject) : void
      {
         if(param1 != null && param1.stage != null && param1.parent != null)
         {
            if(param1 is MovieClip)
            {
               (param1 as MovieClip).stop();
            }
            param1.parent.removeChild(param1);
         }
      }
      
      public static function removeAllChildren(param1:DisplayObjectContainer) : void
      {
         var _loc2_:DisplayObject = null;
         while(Boolean(param1) && Boolean(param1.numChildren))
         {
            _loc2_ = param1.getChildAt(0);
            if(_loc2_ is MovieClip)
            {
               (_loc2_ as MovieClip).stop();
            }
            param1.removeChild(_loc2_);
         }
      }
      
      public static function enableBtn(param1:InteractiveObject, param2:Boolean, param3:Boolean = true, param4:Number = 0.3) : void
      {
         if(!param1)
         {
            return;
         }
         if(param2)
         {
            param1.mouseEnabled = true;
            param1.filters = [];
            return;
         }
         param1.mouseEnabled = false;
         if(param3)
         {
            param1.filters = [FilterUtil.getGrayFilter(param4)];
         }
      }
      
      public static function mcPlay(param1:MovieClip, param2:Boolean) : void
      {
         if(param2)
         {
            param1.gotoAndPlay(1);
         }
         else
         {
            param1.gotoAndStop(1);
         }
      }
      
      public static function mcShow(param1:MovieClip, param2:int) : void
      {
         param1.gotoAndStop(param2);
         param1.visible = param2 != 0;
      }
      
      public static function setBmcTip(param1:BMCSprite, param2:Boolean) : void
      {
         param1.visible = param2;
         if(param2)
         {
            param1.play();
         }
         else
         {
            param1.stop();
         }
      }
      
      public static function setMCTip(param1:MovieClip, param2:Boolean) : void
      {
         param1.visible = param2;
         if(param2)
         {
            param1.play();
         }
         else
         {
            param1.stop();
         }
      }
      
      public static function gray(param1:DisplayObject, param2:Boolean, param3:Number = 0.3) : void
      {
         if(param2)
         {
            param1.filters = [FilterUtil.getGrayFilter(param3)];
         }
         else
         {
            param1.filters = [];
         }
      }
      
      public static function transMC(param1:MovieClip, param2:Sprite) : Object
      {
         var _loc4_:DisplayObject = null;
         var _loc3_:Object = new Object();
         while(param1.numChildren > 0)
         {
            _loc4_ = param1.getChildAt(0);
            _loc3_[_loc4_.name] = _loc4_;
            param2.addChild(_loc4_);
         }
         return _loc3_;
      }
   }
}

