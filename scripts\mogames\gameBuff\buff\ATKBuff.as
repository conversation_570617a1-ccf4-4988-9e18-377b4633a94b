package mogames.gameBuff.buff
{
   import mogames.gameBuff.base.TimeRoleBuff;
   import mogames.gameData.buff.ConstBuffVO;
   
   public class ATKBuff extends TimeRoleBuff
   {
      
      private var _add:int;
      
      public function ATKBuff(param1:ConstBuffVO)
      {
         super(param1);
      }
      
      override protected function createData() : void
      {
         this._add = _buffVO.args.isPer ? int(_owner.roleVO.totalATK * _buffVO.args.value * 0.01) : int(_buffVO.args.value);
         _owner.roleVO.skillATK += this._add;
         _owner.roleVO.updateATK();
      }
      
      override protected function onCleanRole() : void
      {
         _owner.roleVO.skillATK -= this._add;
         _owner.roleVO.updateATK();
      }
   }
}

