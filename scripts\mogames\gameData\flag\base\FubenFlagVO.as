package mogames.gameData.flag.base
{
   import com.mogames.utils.MathUtil;
   
   public class FubenFlagVO extends FlagVO
   {
      
      public function FubenFlagVO(param1:int)
      {
         super(param1,2,true,true,true);
         MathUtil.saveINT(_flag,total);
      }
      
      override public function dailyRefresh() : void
      {
         if(!_daily || cur > total)
         {
            return;
         }
         this.setValue(total);
      }
      
      override public function changeValue(param1:int) : void
      {
         this.setValue(MathUtil.loadINT(_flag) + param1);
      }
      
      override public function setValue(param1:int) : void
      {
         if(param1 <= 0)
         {
            param1 = 0;
         }
         MathUtil.saveINT(_flag,param1);
      }
      
      override public function get isComplete() : Bo<PERSON>an
      {
         return cur <= 0;
      }
      
      override public function get left() : int
      {
         return cur;
      }
   }
}

