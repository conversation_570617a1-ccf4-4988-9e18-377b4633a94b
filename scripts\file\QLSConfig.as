package file
{
   import com.mogames.utils.MathUtil;
   import mogames.ConstData;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.fuben.vo.FubenVO;
   import mogames.gameData.master.MasterProxy;
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class QLSConfig
   {
      
      private static var _instance:QLSConfig;
      
      public var waveData:WaveDataVO;
      
      public var danTime:Number;
      
      public var danArg:Object;
      
      public var danRoleArg:RoleArgVO;
      
      public function QLSConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : QLSConfig
      {
         if(!_instance)
         {
            _instance = new QLSConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         var _loc1_:OneWaveVO = null;
         this.danTime = 22;
         this.danArg = {
            "hp":3000,
            "def":200,
            "miss":0
         };
         this.danRoleArg = new RoleArgVO(267,5000,400,300,30,25,150,150,null);
         this.waveData = new WaveDataVO(0);
         this.waveData.limitBR = new WaveLimitVO(8000,1.2,1.3);
         this.waveData.zhuBoss = new BossArgVO(1001,300000,1100,500,50,100,300,190,new BossSkillData1(11,{
            "hurt":5550,
            "atkPer":30,
            "keepTime":5
         },8),4002,0);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(16,new RoleArgVO(268,1000,200,50,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
      }
      
      public function newReward(param1:Boolean) : Array
      {
         if(!param1)
         {
            return this.loseReward;
         }
         return this.winReward;
      }
      
      private function get winReward() : Array
      {
         var _loc5_:int = 0;
         var _loc1_:Array = [];
         var _loc2_:int = ConstData.INT2.v;
         if(MathUtil.checkOdds(300))
         {
            _loc2_ = ConstData.INT3.v;
         }
         var _loc3_:FubenVO = FubenConfig.instance().findFuben(403);
         var _loc4_:Array = _loc3_.drops.slice(1);
         var _loc6_:int = 0;
         while(_loc6_ < _loc2_)
         {
            _loc5_ = Math.random() * _loc4_.length;
            _loc1_[_loc1_.length] = _loc4_[_loc5_];
            _loc4_.splice(_loc5_,1);
            _loc6_++;
         }
         return _loc1_;
      }
      
      private function get loseReward() : Array
      {
         var _loc1_:int = MasterProxy.instance().masterVO.level * MathUtil.randomNum(300,450);
         return [new BaseRewardVO(10000,_loc1_)];
      }
   }
}

