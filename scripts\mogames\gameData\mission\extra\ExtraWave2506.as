package mogames.gameData.mission.extra
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ExtraWave2506
   {
      
      public function ExtraWave2506()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2506);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(778,220000000,800000,370000,120,150,300,90,new BossSkillData0(150,{
            "hurt":1200000,
            "roleNum":5
         },5),1016,0);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(776,4950000,295000,260000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(776,4950000,295000,260000,100,100,200,120,0)));
         _loc2_.addFu(new BossArgVO(777,36000000,400000,300000,100,120,150,80,new BossSkillData1(10,{
            "hurt":800000,
            "hurtCount":5
         },3),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(774,4950000,295000,260000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(776,4950000,295000,260000,100,100,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(774,4950000,295000,260000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(776,4950000,295000,260000,100,100,200,120,0)));
         _loc2_.addFu(new BossArgVO(777,36000000,400000,300000,100,120,150,80,new BossSkillData1(10,{
            "hurt":800000,
            "hurtCount":5
         },3),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(774,4950000,295000,260000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(776,4950000,295000,260000,100,100,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(774,4950000,295000,260000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(776,4950000,295000,260000,100,100,200,120,0)));
         _loc2_.addFu(new BossArgVO(778,36000000,400000,300000,100,120,150,80,new BossSkillData0(250,{"hurt":800000},3),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(776,4950000,295000,260000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(776,4950000,295000,260000,100,100,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(774,4950000,295000,260000,100,100,200,120,0)));
         _loc2_.addFu(new BossArgVO(778,36000000,400000,300000,100,120,150,80,new BossSkillData1(10,{
            "hurt":800000,
            "hurtCount":5
         },3),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(774,4950000,295000,260000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(774,4950000,295000,260000,100,100,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(776,4950000,295000,260000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(776,4950000,295000,260000,100,100,200,120,0)));
         _loc2_.addFu(new BossArgVO(779,36000000,400000,300000,100,120,150,80,new BossSkillData0(250,{
            "hurt":800000,
            "keepTime":3
         },3),1014,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(774,4950000,295000,260000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(776,4950000,295000,260000,100,100,200,120,0)));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

