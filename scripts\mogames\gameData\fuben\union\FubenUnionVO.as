package mogames.gameData.fuben.union
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import com.mogames.utils.TxtUtil;
   import file.MoneyFuncConfig;
   import mogames.ConstData;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.flag.base.FlagVO;
   import mogames.gameData.game.LackVO;
   import mogames.gameData.mall.vo.MoneyFuncVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameNet.SaveManager;
   import mogames.gameUI.prompt.DecideModule;
   
   public class FubenUnionVO
   {
      
      private var _fid:Oint = new Oint();
      
      private var _numID:Oint = new Oint();
      
      private var _rewards:Array;
      
      public function FubenUnionVO(param1:int, param2:Array)
      {
         super();
         MathUtil.saveINT(this._fid,param1);
         MathUtil.saveINT(this._numID,param1 * 10);
         this._rewards = param2;
      }
      
      public function handlerBuy(param1:Function) : void
      {
         var lackVO:LackVO;
         var moneyVO:MoneyFuncVO = null;
         var func:Function = null;
         var confirmBuy:Function = null;
         var okFunc:Function = param1;
         func = function():void
         {
            moneyVO.handlerBuy(confirmBuy,true);
         };
         confirmBuy = function():void
         {
            numFlag.changeValue(-1);
            okFunc();
         };
         if(!this.numFlag.isComplete)
         {
            EffectManager.addPureText("挑战次数未用完，无需购买！");
            return;
         }
         moneyVO = MoneyFuncConfig.instance().buyUnion;
         lackVO = moneyVO.checkLack();
         if(lackVO)
         {
            EffectManager.addPureText(lackVO.str);
            return;
         }
         DecideModule.instance().showDecide("是否花费" + TxtUtil.setColor(moneyVO.price + "金票","ffff00") + "购买1次挑战次数？<br>" + ConstData.BUY,true,func,null,"是的，我要购买。","再等等！");
      }
      
      public function handlerEnter() : void
      {
         this.numFlag.changeValue(1);
         SaveManager.instance().saveAuto();
      }
      
      public function get fubenID() : int
      {
         return MathUtil.loadINT(this._fid);
      }
      
      public function get numFlag() : FlagVO
      {
         return FlagProxy.instance().fubenFlag.findFlag(MathUtil.loadINT(this._numID));
      }
      
      public function get rewards() : Array
      {
         return this._rewards;
      }
      
      public function get titleFrame() : int
      {
         return this.fubenID - 300;
      }
      
      public function get winIndex() : int
      {
         return this.fubenID % 301;
      }
   }
}

