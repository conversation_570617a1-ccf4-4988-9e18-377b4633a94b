package file
{
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.mission.base.SecretConstVO;
   import mogames.gameData.mission.base.SecretFlagVO;
   
   public class SecretConfig
   {
      
      private static var _instance:SecretConfig;
      
      private var _secrets:Vector.<SecretConstVO>;
      
      public function SecretConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : SecretConfig
      {
         if(!_instance)
         {
            _instance = new SecretConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._secrets = new Vector.<SecretConstVO>();
         this._secrets[this._secrets.length] = new SecretConstVO(201,2001,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(201,2002,2001,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(201,2003,2002,[new BaseRewardVO(10404,6),new BaseRewardVO(10301,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(201,2004,2003,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(201,2005,2004,null,4);
         this._secrets[this._secrets.length] = new SecretConstVO(201,2006,2005,[new BaseRewardVO(10404,6),new BaseRewardVO(10301,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(201,2007,2006,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(201,2008,2007,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(201,2009,2008,[new BaseRewardVO(10402,3),new BaseRewardVO(10404,6),new BaseRewardVO(10301,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(201,2010,2009,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(201,2011,2010,null,8);
         this._secrets[this._secrets.length] = new SecretConstVO(201,2012,2011,[new BaseRewardVO(10403,3),new BaseRewardVO(10404,6),new BaseRewardVO(10301,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(201,2013,2012,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(201,2014,2013,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(201,2015,2014,[new BaseRewardVO(50002,3),new BaseRewardVO(10404,6),new BaseRewardVO(10301,1),new BaseRewardVO(10271,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(202,2016,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(202,2017,2016,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(202,2018,2017,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(202,2019,2018,null,4);
         this._secrets[this._secrets.length] = new SecretConstVO(202,2020,2019,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(202,2021,2020,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(202,2022,2021,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(202,2023,2022,[new BaseRewardVO(10101,1),new BaseRewardVO(11011,3),new BaseRewardVO(11073,3)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(202,2024,2023,null,8);
         this._secrets[this._secrets.length] = new SecretConstVO(202,2025,2024,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(202,2026,2025,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(202,2027,2026,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(202,2028,2027,null,12);
         this._secrets[this._secrets.length] = new SecretConstVO(202,2029,2028,null,13);
         this._secrets[this._secrets.length] = new SecretConstVO(202,2030,2029,[new BaseRewardVO(50003,3),new BaseRewardVO(11154,2),new BaseRewardVO(11201,2),new BaseRewardVO(10272,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(203,2031,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(203,2032,2031,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(203,2033,2032,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(203,2034,2033,null,4);
         this._secrets[this._secrets.length] = new SecretConstVO(203,2035,2034,[new BaseRewardVO(10102,1),new BaseRewardVO(10412,3)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(203,2036,2035,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(203,2037,2036,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(203,2038,2037,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(203,2039,2038,null,8);
         this._secrets[this._secrets.length] = new SecretConstVO(203,2040,2039,[new BaseRewardVO(10411,3),new BaseRewardVO(10412,3)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(203,2041,2040,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(203,2042,2041,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(203,2043,2042,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(203,2044,2043,null,12);
         this._secrets[this._secrets.length] = new SecretConstVO(203,2045,2044,null,13);
         this._secrets[this._secrets.length] = new SecretConstVO(203,2046,2045,[new BaseRewardVO(10411,7),new BaseRewardVO(10412,4),new BaseRewardVO(68001,1),new BaseRewardVO(68002,1),new BaseRewardVO(10273,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(204,2047,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(204,2048,2047,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(204,2049,2048,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(204,2050,2049,null,4);
         this._secrets[this._secrets.length] = new SecretConstVO(204,2051,2050,[new BaseRewardVO(10202,6),new BaseRewardVO(10030,3)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(204,2052,2051,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(204,2053,2052,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(204,2054,2053,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(204,2055,2054,null,8);
         this._secrets[this._secrets.length] = new SecretConstVO(204,2056,2055,[new BaseRewardVO(10030,3),new BaseRewardVO(10412,3)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(204,2057,2056,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(204,2058,2057,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(204,2059,2058,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(204,2060,2059,null,12);
         this._secrets[this._secrets.length] = new SecretConstVO(204,2061,2060,[new BaseRewardVO(10415,2),new BaseRewardVO(10412,4),new BaseRewardVO(68001,1),new BaseRewardVO(68002,1),new BaseRewardVO(10274,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(205,2062,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(205,2063,2062,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(205,2064,2063,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(205,2065,2064,null,4);
         this._secrets[this._secrets.length] = new SecretConstVO(205,2066,2065,[new BaseRewardVO(68001,1),new BaseRewardVO(10304,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(205,2067,2066,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(205,2068,2067,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(205,2069,2068,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(205,2070,2069,null,8);
         this._secrets[this._secrets.length] = new SecretConstVO(205,2071,2070,[new BaseRewardVO(68002,1),new BaseRewardVO(50010,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(205,2072,2071,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(205,2073,2072,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(205,2074,2073,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(205,2075,2074,null,12);
         this._secrets[this._secrets.length] = new SecretConstVO(205,2076,2075,[new BaseRewardVO(10415,1),new BaseRewardVO(50010,2),new BaseRewardVO(10304,2),new BaseRewardVO(10801,1),new BaseRewardVO(10275,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(206,2077,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(206,2078,2077,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(206,2079,2078,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(206,2080,2079,null,4);
         this._secrets[this._secrets.length] = new SecretConstVO(206,2081,2080,[new BaseRewardVO(10202,6),new BaseRewardVO(10030,3)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(206,2082,2081,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(206,2083,2082,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(206,2084,2083,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(206,2085,2084,null,8);
         this._secrets[this._secrets.length] = new SecretConstVO(206,2086,2085,[new BaseRewardVO(10030,3),new BaseRewardVO(10412,3)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(206,2087,2086,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(206,2088,2087,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(206,2089,2088,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(206,2090,2089,null,12);
         this._secrets[this._secrets.length] = new SecretConstVO(206,2091,2090,[new BaseRewardVO(50015,2),new BaseRewardVO(10418,3),new BaseRewardVO(10419,3)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(207,2092,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(207,2093,2092,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(207,2094,2093,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(207,2095,2094,null,4);
         this._secrets[this._secrets.length] = new SecretConstVO(207,2096,2095,[new BaseRewardVO(68001,1),new BaseRewardVO(10404,3)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(207,2097,2096,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(207,2098,2097,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(207,2099,2098,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(207,2100,2099,null,8);
         this._secrets[this._secrets.length] = new SecretConstVO(207,2101,2100,[new BaseRewardVO(68002,1),new BaseRewardVO(11005,3)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(207,2102,2101,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(207,2103,2102,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(207,2104,2103,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(207,2105,2104,null,12);
         this._secrets[this._secrets.length] = new SecretConstVO(207,2106,2105,[new BaseRewardVO(50016,2),new BaseRewardVO(11104,1),new BaseRewardVO(11003,1),new BaseRewardVO(10276,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(208,2107,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(208,2108,2107,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(208,2109,2108,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(208,2110,2109,null,4);
         this._secrets[this._secrets.length] = new SecretConstVO(208,2111,2110,[new BaseRewardVO(68001,1),new BaseRewardVO(10416,5)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(208,2112,2111,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(208,2113,2112,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(208,2114,2113,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(208,2115,2114,null,8);
         this._secrets[this._secrets.length] = new SecretConstVO(208,2116,2115,[new BaseRewardVO(68002,1),new BaseRewardVO(10417,5)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(208,2117,2116,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(208,2118,2117,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(208,2119,2118,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(208,2120,2119,null,12);
         this._secrets[this._secrets.length] = new SecretConstVO(208,2121,2120,[new BaseRewardVO(50018,2),new BaseRewardVO(11005,1),new BaseRewardVO(11011,1),new BaseRewardVO(10811,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(209,2122,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(209,2123,2122,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(209,2124,2123,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(209,2125,2124,null,4);
         this._secrets[this._secrets.length] = new SecretConstVO(209,2126,2125,[new BaseRewardVO(68001,1),new BaseRewardVO(11071,5)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(209,2127,2126,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(209,2128,2127,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(209,2129,2128,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(209,2130,2129,null,8);
         this._secrets[this._secrets.length] = new SecretConstVO(209,2131,2130,[new BaseRewardVO(68002,1),new BaseRewardVO(10413,3)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(209,2132,2131,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(209,2133,2132,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(209,2134,2133,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(209,2135,2134,null,12);
         this._secrets[this._secrets.length] = new SecretConstVO(209,2136,2135,[new BaseRewardVO(10980,2),new BaseRewardVO(10306,2),new BaseRewardVO(10413,3),new BaseRewardVO(10277,1),new BaseRewardVO(10806,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(210,2137,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(210,2138,2137,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(210,2139,2138,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(210,2140,2139,null,4);
         this._secrets[this._secrets.length] = new SecretConstVO(210,2141,2140,[new BaseRewardVO(68001,1),new BaseRewardVO(11002,3)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(210,2142,2141,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(210,2143,2142,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(210,2144,2143,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(210,2145,2144,null,8);
         this._secrets[this._secrets.length] = new SecretConstVO(210,2146,2145,[new BaseRewardVO(68002,1),new BaseRewardVO(10306,3)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(210,2147,2146,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(210,2148,2147,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(210,2149,2148,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(210,2150,2149,null,12);
         this._secrets[this._secrets.length] = new SecretConstVO(210,2151,2150,[new BaseRewardVO(11501,3),new BaseRewardVO(10304,3),new BaseRewardVO(50025,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(211,2152,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(211,2153,2152,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(211,2154,2153,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(211,2155,2154,null,4);
         this._secrets[this._secrets.length] = new SecretConstVO(211,2156,2155,[new BaseRewardVO(68001,2),new BaseRewardVO(11501,3)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(211,2157,2156,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(211,2158,2157,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(211,2159,2158,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(211,2160,2159,null,8);
         this._secrets[this._secrets.length] = new SecretConstVO(211,2161,2160,[new BaseRewardVO(68002,2),new BaseRewardVO(11012,3)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(211,2162,2161,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(211,2163,2162,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(211,2164,2163,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(211,2165,2164,null,12);
         this._secrets[this._secrets.length] = new SecretConstVO(211,2166,2165,[new BaseRewardVO(10980,2),new BaseRewardVO(10304,3),new BaseRewardVO(50028,2),new BaseRewardVO(10278,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(212,2167,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(212,2168,2167,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(212,2169,2168,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(212,2170,2169,null,4);
         this._secrets[this._secrets.length] = new SecretConstVO(212,2171,2170,[new BaseRewardVO(68001,2),new BaseRewardVO(10303,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(212,2172,2171,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(212,2173,2172,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(212,2174,2173,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(212,2175,2174,null,8);
         this._secrets[this._secrets.length] = new SecretConstVO(212,2176,2175,[new BaseRewardVO(68002,2),new BaseRewardVO(10304,3)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(212,2177,2176,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(212,2178,2177,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(212,2179,2178,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(212,2180,2179,null,12);
         this._secrets[this._secrets.length] = new SecretConstVO(212,2181,2180,[new BaseRewardVO(11157,1),new BaseRewardVO(11071,3),new BaseRewardVO(50032,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(213,2182,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(213,2183,2182,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(213,2184,2183,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(213,2185,2184,null,4);
         this._secrets[this._secrets.length] = new SecretConstVO(213,2186,2185,[new BaseRewardVO(68001,2),new BaseRewardVO(10300,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(213,2187,2186,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(213,2188,2187,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(213,2189,2188,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(213,2190,2189,null,8);
         this._secrets[this._secrets.length] = new SecretConstVO(213,2191,2190,[new BaseRewardVO(68002,2),new BaseRewardVO(10304,3)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(213,2192,2191,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(213,2193,2192,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(213,2194,2193,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(213,2195,2194,null,12);
         this._secrets[this._secrets.length] = new SecretConstVO(213,2196,2195,[new BaseRewardVO(11204,1),new BaseRewardVO(11071,3),new BaseRewardVO(50036,2),new BaseRewardVO(10279,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(214,2197,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(214,2198,2197,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(214,2199,2198,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(214,2200,2199,null,4);
         this._secrets[this._secrets.length] = new SecretConstVO(214,2201,2200,[new BaseRewardVO(10204,1),new BaseRewardVO(10034,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(214,2202,2201,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(214,2203,2202,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(214,2204,2203,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(214,2205,2204,null,8);
         this._secrets[this._secrets.length] = new SecretConstVO(214,2206,2205,[new BaseRewardVO(10204,2),new BaseRewardVO(10034,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(214,2207,2206,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(214,2208,2207,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(214,2209,2208,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(214,2210,2209,null,12);
         this._secrets[this._secrets.length] = new SecretConstVO(214,2211,2210,[new BaseRewardVO(10204,2),new BaseRewardVO(10034,3),new BaseRewardVO(11601,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(215,2212,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(215,2213,2212,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(215,2214,2213,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(215,2215,2214,null,4);
         this._secrets[this._secrets.length] = new SecretConstVO(215,2216,2215,[new BaseRewardVO(68001,2),new BaseRewardVO(11101,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(215,2217,2216,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(215,2218,2217,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(215,2219,2218,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(215,2220,2219,null,8);
         this._secrets[this._secrets.length] = new SecretConstVO(215,2221,2220,[new BaseRewardVO(68002,2),new BaseRewardVO(11201,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(215,2222,2221,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(215,2223,2222,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(215,2224,2223,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(215,2225,2224,null,12);
         this._secrets[this._secrets.length] = new SecretConstVO(215,2226,2225,[new BaseRewardVO(11157,1),new BaseRewardVO(10560,2),new BaseRewardVO(10559,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(216,2227,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(216,2228,2227,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(216,2229,2228,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(216,2230,2229,null,4);
         this._secrets[this._secrets.length] = new SecretConstVO(216,2231,2230,[new BaseRewardVO(68001,2),new BaseRewardVO(10034,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(216,2232,2231,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(216,2233,2232,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(216,2234,2233,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(216,2235,2234,null,8);
         this._secrets[this._secrets.length] = new SecretConstVO(216,2236,2235,[new BaseRewardVO(68002,2),new BaseRewardVO(10034,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(216,2237,2236,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(216,2238,2237,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(216,2239,2238,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(216,2240,2239,null,12);
         this._secrets[this._secrets.length] = new SecretConstVO(216,2241,2240,[new BaseRewardVO(10204,2),new BaseRewardVO(10034,3),new BaseRewardVO(10561,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(217,2242,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(217,2243,2242,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(217,2244,2243,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(217,2245,2244,null,4);
         this._secrets[this._secrets.length] = new SecretConstVO(217,2246,2245,[new BaseRewardVO(68001,2),new BaseRewardVO(11601,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(217,2247,2246,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(217,2248,2247,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(217,2249,2248,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(217,2250,2249,null,8);
         this._secrets[this._secrets.length] = new SecretConstVO(217,2251,2250,[new BaseRewardVO(68002,2),new BaseRewardVO(11501,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(217,2252,2251,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(217,2253,2252,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(217,2254,2253,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(217,2255,2254,null,12);
         this._secrets[this._secrets.length] = new SecretConstVO(217,2256,2255,[new BaseRewardVO(11157,1),new BaseRewardVO(10300,2),new BaseRewardVO(50046,3)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(218,2257,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(218,2258,2257,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(218,2259,2258,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(218,2260,2259,null,4);
         this._secrets[this._secrets.length] = new SecretConstVO(218,2261,2260,[new BaseRewardVO(68001,2),new BaseRewardVO(10034,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(218,2262,2261,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(218,2263,2262,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(218,2264,2263,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(218,2265,2264,null,8);
         this._secrets[this._secrets.length] = new SecretConstVO(218,2266,2265,[new BaseRewardVO(68002,2),new BaseRewardVO(10034,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(218,2267,2266,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(218,2268,2267,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(218,2269,2268,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(218,2270,2269,null,12);
         this._secrets[this._secrets.length] = new SecretConstVO(218,2271,2270,[new BaseRewardVO(10204,2),new BaseRewardVO(10034,3),new BaseRewardVO(50045,3)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(219,2272,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(219,2273,2272,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(219,2274,2273,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(219,2275,2274,null,4);
         this._secrets[this._secrets.length] = new SecretConstVO(219,2276,2275,[new BaseRewardVO(68001,2),new BaseRewardVO(11601,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(219,2277,2276,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(219,2278,2277,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(219,2279,2278,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(219,2280,2279,null,8);
         this._secrets[this._secrets.length] = new SecretConstVO(219,2281,2280,[new BaseRewardVO(68002,2),new BaseRewardVO(11501,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(219,2282,2281,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(219,2283,2282,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(219,2284,2283,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(219,2285,2284,null,12);
         this._secrets[this._secrets.length] = new SecretConstVO(219,2286,2285,[new BaseRewardVO(11157,1),new BaseRewardVO(10300,2),new BaseRewardVO(50039,3)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(220,2287,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(220,2288,2287,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(220,2289,2288,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(220,2290,2289,null,4);
         this._secrets[this._secrets.length] = new SecretConstVO(220,2291,2290,[new BaseRewardVO(68001,2),new BaseRewardVO(11601,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(220,2292,2291,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(220,2293,2292,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(220,2294,2293,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(220,2295,2294,null,8);
         this._secrets[this._secrets.length] = new SecretConstVO(220,2296,2295,[new BaseRewardVO(68002,2),new BaseRewardVO(11501,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(220,2297,2296,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(220,2298,2297,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(220,2299,2298,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(220,2300,2299,null,12);
         this._secrets[this._secrets.length] = new SecretConstVO(220,2301,2300,[new BaseRewardVO(10204,2),new BaseRewardVO(10034,2),new BaseRewardVO(50041,3)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(221,2302,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(221,2303,2302,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(221,2304,2303,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(221,2305,2304,null,4);
         this._secrets[this._secrets.length] = new SecretConstVO(221,2306,2305,[new BaseRewardVO(68001,2),new BaseRewardVO(11601,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(221,2307,2306,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(221,2308,2307,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(221,2309,2308,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(221,2310,2309,null,8);
         this._secrets[this._secrets.length] = new SecretConstVO(221,2311,2310,[new BaseRewardVO(68002,2),new BaseRewardVO(11501,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(221,2312,2311,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(221,2313,2312,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(221,2314,2313,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(221,2315,2314,null,12);
         this._secrets[this._secrets.length] = new SecretConstVO(221,2316,2315,[new BaseRewardVO(10204,2),new BaseRewardVO(11011,2),new BaseRewardVO(50037,3)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(222,2317,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(222,2318,2317,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(222,2319,2318,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(222,2320,2319,null,4);
         this._secrets[this._secrets.length] = new SecretConstVO(222,2321,2320,[new BaseRewardVO(68001,2),new BaseRewardVO(10034,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(222,2322,2321,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(222,2323,2322,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(222,2324,2323,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(222,2325,2324,null,8);
         this._secrets[this._secrets.length] = new SecretConstVO(222,2326,2325,[new BaseRewardVO(68002,2),new BaseRewardVO(10034,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(222,2327,2326,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(222,2328,2327,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(222,2329,2328,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(222,2330,2329,null,12);
         this._secrets[this._secrets.length] = new SecretConstVO(222,2331,2330,[new BaseRewardVO(10204,2),new BaseRewardVO(10034,3),new BaseRewardVO(10571,3)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(223,2332,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(223,2333,2332,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(223,2334,2333,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(223,2335,2334,null,4);
         this._secrets[this._secrets.length] = new SecretConstVO(223,2336,2335,[new BaseRewardVO(68001,2),new BaseRewardVO(11501,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(223,2337,2336,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(223,2338,2337,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(223,2339,2338,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(223,2340,2339,null,8);
         this._secrets[this._secrets.length] = new SecretConstVO(223,2341,2340,[new BaseRewardVO(68002,2),new BaseRewardVO(11601,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(223,2342,2341,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(223,2343,2342,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(223,2344,2343,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(223,2345,2344,null,12);
         this._secrets[this._secrets.length] = new SecretConstVO(223,2346,2345,[new BaseRewardVO(10204,2),new BaseRewardVO(11011,2),new BaseRewardVO(50043,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(224,2347,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(224,2348,2347,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(224,2349,2348,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(224,2350,2349,null,4);
         this._secrets[this._secrets.length] = new SecretConstVO(224,2351,2350,[new BaseRewardVO(68001,2),new BaseRewardVO(11601,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(224,2352,2351,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(224,2353,2352,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(224,2354,2353,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(224,2355,2354,null,8);
         this._secrets[this._secrets.length] = new SecretConstVO(224,2356,2355,[new BaseRewardVO(68002,2),new BaseRewardVO(11501,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(224,2357,2356,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(224,2358,2357,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(224,2359,2358,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(224,2360,2359,null,12);
         this._secrets[this._secrets.length] = new SecretConstVO(224,2361,2360,[new BaseRewardVO(11157,1),new BaseRewardVO(10300,2),new BaseRewardVO(10574,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(225,2362,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(225,2363,2362,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(225,2364,2363,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(225,2365,2364,null,4);
         this._secrets[this._secrets.length] = new SecretConstVO(225,2366,2365,[new BaseRewardVO(68001,2),new BaseRewardVO(11601,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(225,2367,2366,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(225,2368,2367,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(225,2369,2368,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(225,2370,2369,null,8);
         this._secrets[this._secrets.length] = new SecretConstVO(225,2371,2370,[new BaseRewardVO(68002,2),new BaseRewardVO(11501,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(225,2372,2371,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(225,2373,2372,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(225,2374,2373,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(225,2375,2374,null,12);
         this._secrets[this._secrets.length] = new SecretConstVO(225,2376,2375,[new BaseRewardVO(11157,1),new BaseRewardVO(10300,2),new BaseRewardVO(10576,3)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(226,2377,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(226,2378,2377,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(226,2379,2378,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(226,2380,2379,[new BaseRewardVO(68001,2),new BaseRewardVO(11601,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(226,2381,2380,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(226,2382,2381,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(226,2383,2382,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(226,2384,2383,[new BaseRewardVO(68002,2),new BaseRewardVO(11501,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(226,2385,2384,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(226,2386,2385,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(226,2387,2386,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(226,2388,2387,[new BaseRewardVO(11157,1),new BaseRewardVO(10300,2),new BaseRewardVO(10580,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(227,2389,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(227,2390,2389,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(227,2391,2390,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(227,2392,2391,[new BaseRewardVO(68001,2),new BaseRewardVO(11601,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(227,2393,2392,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(227,2394,2393,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(227,2395,2394,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(227,2396,2395,[new BaseRewardVO(68002,2),new BaseRewardVO(11501,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(227,2397,2396,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(227,2398,2397,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(227,2399,2398,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(227,2400,2399,[new BaseRewardVO(11157,1),new BaseRewardVO(10300,2),new BaseRewardVO(10584,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(228,2401,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(228,2402,2401,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(228,2403,2402,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(228,2404,2403,[new BaseRewardVO(68001,2),new BaseRewardVO(11601,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(228,2405,2404,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(228,2406,2405,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(228,2407,2406,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(228,2408,2407,[new BaseRewardVO(68002,2),new BaseRewardVO(11501,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(228,2409,2408,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(228,2410,2409,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(228,2411,2410,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(228,2412,2411,[new BaseRewardVO(11157,1),new BaseRewardVO(10300,2),new BaseRewardVO(10588,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(229,2413,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(229,2414,2413,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(229,2415,2414,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(229,2416,2415,[new BaseRewardVO(68001,2),new BaseRewardVO(11601,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(229,2417,2416,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(229,2418,2417,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(229,2419,2418,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(229,2420,2419,[new BaseRewardVO(68002,2),new BaseRewardVO(11501,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(229,2421,2420,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(229,2422,2421,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(229,2423,2422,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(229,2424,2423,[new BaseRewardVO(11157,1),new BaseRewardVO(10300,2),new BaseRewardVO(10593,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(230,2425,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(230,2426,2425,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(230,2427,2426,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(230,2428,2427,[new BaseRewardVO(68001,2),new BaseRewardVO(11601,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(230,2429,2428,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(230,2430,2429,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(230,2431,2430,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(230,2432,2431,[new BaseRewardVO(68002,2),new BaseRewardVO(11501,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(230,2433,2432,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(230,2434,2433,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(230,2435,2434,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(230,2436,2435,[new BaseRewardVO(11157,1),new BaseRewardVO(10300,2),new BaseRewardVO(10597,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(231,2437,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(231,2438,2437,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(231,2439,2438,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(231,2440,2439,[new BaseRewardVO(68001,2),new BaseRewardVO(11601,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(231,2441,2440,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(231,2442,2441,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(231,2443,2442,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(231,2444,2443,[new BaseRewardVO(68002,2),new BaseRewardVO(11501,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(231,2445,2444,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(231,2446,2445,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(231,2447,2446,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(231,2448,2447,[new BaseRewardVO(11157,1),new BaseRewardVO(10300,2),new BaseRewardVO(10589,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(232,2449,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(232,2450,2449,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(232,2451,2450,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(232,2452,2451,[new BaseRewardVO(68001,2),new BaseRewardVO(11601,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(232,2453,2452,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(232,2454,2453,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(232,2455,2454,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(232,2456,2455,[new BaseRewardVO(68002,2),new BaseRewardVO(11501,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(232,2457,2456,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(232,2458,2457,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(232,2459,2458,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(232,2460,2459,[new BaseRewardVO(11157,1),new BaseRewardVO(10300,2),new BaseRewardVO(10320,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(233,2461,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(233,2462,2461,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(233,2463,2462,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(233,2464,2463,[new BaseRewardVO(68001,10),new BaseRewardVO(11601,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(233,2465,2464,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(233,2466,2465,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(233,2467,2466,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(233,2468,2467,[new BaseRewardVO(68002,10),new BaseRewardVO(11501,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(233,2469,2468,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(233,2470,2469,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(233,2471,2470,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(233,2472,2471,[new BaseRewardVO(11157,1),new BaseRewardVO(10300,2),new BaseRewardVO(10920,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(234,2473,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(234,2474,2473,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(234,2475,2474,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(234,2476,2475,[new BaseRewardVO(68001,10),new BaseRewardVO(11601,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(234,2477,2476,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(234,2478,2477,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(234,2479,2478,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(234,2480,2479,[new BaseRewardVO(68002,10),new BaseRewardVO(11501,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(234,2481,2480,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(234,2482,2481,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(234,2483,2482,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(234,2484,2483,[new BaseRewardVO(11157,1),new BaseRewardVO(10300,2),new BaseRewardVO(10946,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(235,2485,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(235,2486,2485,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(235,2487,2486,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(235,2488,2487,[new BaseRewardVO(68001,10),new BaseRewardVO(11601,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(235,2489,2488,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(235,2490,2489,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(235,2491,2490,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(235,2492,2491,[new BaseRewardVO(68002,10),new BaseRewardVO(11501,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(235,2493,2492,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(235,2494,2493,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(235,2495,2494,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(235,2496,2495,[new BaseRewardVO(11157,1),new BaseRewardVO(10300,2),new BaseRewardVO(10947,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(236,2497,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(236,2498,2497,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(236,2499,2498,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(236,2500,2499,[new BaseRewardVO(68001,10),new BaseRewardVO(11601,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(236,2501,2500,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(236,2502,2501,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(236,2503,2502,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(236,2504,2503,[new BaseRewardVO(68002,10),new BaseRewardVO(11501,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(236,2505,2504,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(236,2506,2505,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(236,2507,2506,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(236,2508,2507,[new BaseRewardVO(11157,1),new BaseRewardVO(10300,2),new BaseRewardVO(10964,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(237,2509,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(237,2510,2509,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(237,2511,2510,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(237,2512,2511,[new BaseRewardVO(68001,10),new BaseRewardVO(11601,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(237,2513,2512,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(237,2514,2513,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(237,2515,2514,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(237,2516,2515,[new BaseRewardVO(68002,10),new BaseRewardVO(11501,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(237,2517,2516,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(237,2518,2517,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(237,2519,2518,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(237,2520,2519,[new BaseRewardVO(11157,1),new BaseRewardVO(10300,2),new BaseRewardVO(10968,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(238,2521,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(238,2522,2521,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(238,2523,2522,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(238,2524,2523,[new BaseRewardVO(68001,10),new BaseRewardVO(11601,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(238,2525,2524,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(238,2526,2525,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(238,2527,2526,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(238,2528,2527,[new BaseRewardVO(68002,10),new BaseRewardVO(11501,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(238,2529,2528,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(238,2530,2529,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(238,2531,2530,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(238,2532,2531,[new BaseRewardVO(11157,1),new BaseRewardVO(10300,2),new BaseRewardVO(10974,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(239,2533,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(239,2534,2533,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(239,2535,2534,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(239,2536,2535,[new BaseRewardVO(68001,10),new BaseRewardVO(11601,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(239,2537,2536,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(239,2538,2537,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(239,2539,2538,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(239,2540,2539,[new BaseRewardVO(68002,10),new BaseRewardVO(11501,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(239,2541,2540,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(239,2542,2541,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(239,2543,2542,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(239,2544,2543,[new BaseRewardVO(11157,1),new BaseRewardVO(10300,2),new BaseRewardVO(10979,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(240,2545,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(240,2546,2545,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(240,2547,2546,null,3);
         this._secrets[this._secrets.length] = new SecretConstVO(240,2548,2547,[new BaseRewardVO(68001,10),new BaseRewardVO(11601,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(240,2549,2548,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(240,2550,2549,null,6);
         this._secrets[this._secrets.length] = new SecretConstVO(240,2551,2550,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(240,2552,2551,[new BaseRewardVO(68002,10),new BaseRewardVO(11501,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(240,2553,2552,null,9);
         this._secrets[this._secrets.length] = new SecretConstVO(240,2554,2553,null,10);
         this._secrets[this._secrets.length] = new SecretConstVO(240,2555,2554,null,11);
         this._secrets[this._secrets.length] = new SecretConstVO(240,2556,2555,[new BaseRewardVO(11157,1),new BaseRewardVO(10300,2),new BaseRewardVO(10993,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(241,2557,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(241,2558,2557,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(241,2559,2558,[new BaseRewardVO(18501,3),new BaseRewardVO(11601,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(241,2560,2559,null,4);
         this._secrets[this._secrets.length] = new SecretConstVO(241,2561,2560,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(241,2562,2561,[new BaseRewardVO(18501,3),new BaseRewardVO(11501,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(241,2563,2562,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(241,2564,2563,null,8);
         this._secrets[this._secrets.length] = new SecretConstVO(241,2565,2564,[new BaseRewardVO(11157,1),new BaseRewardVO(10300,2),new BaseRewardVO(10997,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(242,2566,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(242,2567,2566,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(242,2568,2567,[new BaseRewardVO(18501,3),new BaseRewardVO(11601,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(242,2569,2568,null,4);
         this._secrets[this._secrets.length] = new SecretConstVO(242,2570,2569,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(242,2571,2570,[new BaseRewardVO(18501,3),new BaseRewardVO(11501,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(242,2572,2571,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(242,2573,2572,null,8);
         this._secrets[this._secrets.length] = new SecretConstVO(242,2574,2573,[new BaseRewardVO(11157,1),new BaseRewardVO(10300,2),new BaseRewardVO(10844,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(243,2575,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(243,2576,2575,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(243,2577,2576,[new BaseRewardVO(18501,3),new BaseRewardVO(11601,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(243,2578,2577,null,4);
         this._secrets[this._secrets.length] = new SecretConstVO(243,2579,2578,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(243,2580,2579,[new BaseRewardVO(18501,3),new BaseRewardVO(11501,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(243,2581,2580,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(243,2582,2581,null,8);
         this._secrets[this._secrets.length] = new SecretConstVO(243,2583,2582,[new BaseRewardVO(11157,1),new BaseRewardVO(10300,2),new BaseRewardVO(10848,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(244,2584,0,null,1);
         this._secrets[this._secrets.length] = new SecretConstVO(244,2585,2584,null,2);
         this._secrets[this._secrets.length] = new SecretConstVO(244,2586,2585,[new BaseRewardVO(18501,3),new BaseRewardVO(11601,1)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(244,2587,2586,null,4);
         this._secrets[this._secrets.length] = new SecretConstVO(244,2588,2587,null,5);
         this._secrets[this._secrets.length] = new SecretConstVO(244,2589,2588,[new BaseRewardVO(18501,3),new BaseRewardVO(11501,2)],0);
         this._secrets[this._secrets.length] = new SecretConstVO(244,2590,2589,null,7);
         this._secrets[this._secrets.length] = new SecretConstVO(244,2591,2590,null,8);
         this._secrets[this._secrets.length] = new SecretConstVO(244,2592,2591,[new BaseRewardVO(11157,1),new BaseRewardVO(10300,2),new BaseRewardVO(10894,1)],0);
      }
      
      public function findConst(param1:int) : SecretConstVO
      {
         var _loc2_:SecretConstVO = null;
         for each(_loc2_ in this._secrets)
         {
            if(_loc2_.mid == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function findArea(param1:int) : Array
      {
         var _loc3_:SecretConstVO = null;
         var _loc2_:Array = [];
         for each(_loc3_ in this._secrets)
         {
            if(_loc3_.areaID == param1)
            {
               _loc2_[_loc2_.length] = _loc3_;
            }
         }
         return _loc2_;
      }
      
      public function newFlags() : Array
      {
         var _loc2_:SecretConstVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._secrets)
         {
            _loc1_.push(new SecretFlagVO(_loc2_));
         }
         return _loc1_;
      }
   }
}

