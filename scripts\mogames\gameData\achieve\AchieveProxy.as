package mogames.gameData.achieve
{
   import com.mogames.event.GameSignal;
   import com.mogames.event.SignalManager;
   import mogames.ConstData;
   import mogames.gameData.achieve.base.Achieve417VO;
   import mogames.gameData.achieve.base.AchieveHeroVO;
   import mogames.gameData.achieve.base.AchieveLockVO;
   import mogames.gameData.achieve.base.BaseAchieveVO;
   import mogames.gameData.achieve.base.NumAchieveVO0;
   import mogames.gameData.achieve.base.NumAchieveVO1;
   import mogames.gameData.achieve.vo.AchieveRecordHeroVO;
   import mogames.gameData.achieve.vo.AreaAchieveVO;
   import mogames.gameData.achieve.vo.StarAchieveVO;
   import mogames.gameData.achieve.vo.TownAchieveVO;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.base.vo.EquipRewardVO;
   
   public class AchieveProxy
   {
      
      private static var _instance:AchieveProxy;
      
      private var _list:Array;
      
      public function AchieveProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
      }
      
      public static function instance() : AchieveProxy
      {
         if(!_instance)
         {
            _instance = new AchieveProxy();
         }
         return _instance;
      }
      
      public function startNew() : void
      {
         this._list = [];
         this._list[this._list.length] = new AchieveLockVO(100,[new BaseRewardVO(10000,20000)],"初次任命","内政时任命1名太守。");
         this._list[this._list.length] = new AchieveLockVO(101,[new BaseRewardVO(11071,10)],"初次批阅","内政时批阅1次太守上奏。");
         this._list[this._list.length] = new AchieveLockVO(102,[new BaseRewardVO(11002,5)],"初次征收","内政时征收1次资源。");
         this._list[this._list.length] = new AchieveLockVO(103,[new BaseRewardVO(11005,8)],"初次罢免","内政时罢免任意1名太守。");
         this._list[this._list.length] = new AchieveLockVO(104,[new BaseRewardVO(10006,50)],"初次建造","在建筑升级中升级1次建筑。");
         this._list[this._list.length] = new AchieveLockVO(105,[new BaseRewardVO(11002,15)],"了解军事","在研究军事中升级1次兵种天赋。");
         this._list[this._list.length] = new AchieveLockVO(106,[new BaseRewardVO(10404,10)],"了解策略","在研究策略中升级1次主公技能。");
         this._list[this._list.length] = new AchieveLockVO(107,[new BaseRewardVO(10005,50)],"初次购买","找酿酒师杜康购买任意酒类道具。");
         this._list[this._list.length] = new AchieveLockVO(108,[new BaseRewardVO(11104,2)],"初次酿造","找酿酒师杜康酿造1个道具。");
         this._list[this._list.length] = new AchieveLockVO(109,[new BaseRewardVO(10902,2)],"初次强化","找铁匠大叔强化1次装备。");
         this._list[this._list.length] = new TownAchieveVO(201,1001,[new BaseRewardVO(10000,20000)],"初战告捷","剧情模式下通关【豫州·项县】。");
         this._list[this._list.length] = new NumAchieveVO0(202,5,[new BaseRewardVO(10272,5)],"连战连捷","战役模式下连胜五次。");
         this._list[this._list.length] = new NumAchieveVO0(203,5,[new BaseRewardVO(10273,5)],"连战连败","战役模式下连败五次。");
         this._list[this._list.length] = new AreaAchieveVO(204,201,[new BaseRewardVO(10001,50)],"小试身手","剧情模式下通关豫州所有关卡。");
         this._list[this._list.length] = new StarAchieveVO(205,201,[new BaseRewardVO(10001,100)],"一统豫州","战役模式下豫州获得满星。");
         this._list[this._list.length] = new TownAchieveVO(206,1257,[new BaseRewardVO(10852,30)],"一统江山","剧情模式下通关【益州·乐成】。");
         this._list[this._list.length] = new AchieveLockVO(301,[new BaseRewardVO(10271,5)],"初次见面","完成1次某武将的好感度任务。");
         this._list[this._list.length] = new AchieveLockVO(302,[new BaseRewardVO(10000,15000)],"招募一星将","在酒馆招募到1名一星武将。");
         this._list[this._list.length] = new AchieveLockVO(303,[new BaseRewardVO(10272,5)],"招募二星将","在酒馆招募到1名二星武将。");
         this._list[this._list.length] = new AchieveLockVO(304,[new BaseRewardVO(10273,5)],"招募三星将","在酒馆招募到1名三星武将。");
         this._list[this._list.length] = new AchieveLockVO(305,[new BaseRewardVO(10006,150)],"招募四星将","在酒馆招募到1名四星武将。");
         this._list[this._list.length] = new AchieveHeroVO(306,3,2,[new BaseRewardVO(10311,1)],"良将投奔I","拥有3名三星武将。");
         this._list[this._list.length] = new AchieveHeroVO(307,5,3,[new BaseRewardVO(10312,1)],"良将投奔II","拥有5名四星武将。");
         this._list[this._list.length] = new AchieveLockVO(400,[new BaseRewardVO(10000,20000)],"就是这么任性","开始剧情中点击四次【否】按钮。");
         this._list[this._list.length] = new NumAchieveVO1(402,50,[new BaseRewardVO(10007,400)],"砍人砍上瘾","斩首敌将超过50名。");
         this._list[this._list.length] = new NumAchieveVO1(403,50,[new BaseRewardVO(10001,1000)],"善用曹孟德","使用奸雄之怒技能秒刹超过50名武将。");
         this._list[this._list.length] = new NumAchieveVO1(405,100,[new BaseRewardVO(10000,50000)],"我的前世是周扒皮","搜刮城中百姓超过100次。");
         this._list[this._list.length] = new NumAchieveVO1(406,100,[new BaseRewardVO(11005,10)],"逃跑专家","在战场中全军撤退100次。");
         this._list[this._list.length] = new NumAchieveVO1(407,100,[new BaseRewardVO(10302,10)],"爷的大将就是多","在战场中换将100次。");
         this._list[this._list.length] = new AchieveLockVO(408,[new BaseRewardVO(10302,3)],"警察叔叔快救我！","拨打110报警电话。");
         this._list[this._list.length] = new AchieveLockVO(409,[new BaseRewardVO(11002,15)],"一时失手","地牢拷打S一名武将。");
         this._list[this._list.length] = new NumAchieveVO1(410,10,[new BaseRewardVO(11005,10)],"失手成习惯","地牢拷打S10名武将。");
         this._list[this._list.length] = new NumAchieveVO1(411,300,[new BaseRewardVO(11104,10)],"拷打上瘾","地牢拷打300次。");
         this._list[this._list.length] = new NumAchieveVO1(412,100,[new BaseRewardVO(11157,10)],"山贼终结者","攻打山贼胜利100次。");
         this._list[this._list.length] = new NumAchieveVO1(413,100,[new BaseRewardVO(11011,10)],"开锁之王","战场中开启宝箱100次。");
         this._list[this._list.length] = new NumAchieveVO1(414,500000,[new BaseRewardVO(11073,10)],"富甲一方","累计获得50万银票。");
         this._list[this._list.length] = new NumAchieveVO1(415,100,[new BaseRewardVO(11204,10)],"暴乱终结者","平定城市暴动100次。");
         this._list[this._list.length] = new NumAchieveVO1(416,100,[new BaseRewardVO(11151,5)],"日理万机","批阅奏章100次。");
         this._list[this._list.length] = new Achieve417VO(417,[new BaseRewardVO(11005,5)],"巾帼不让须眉","获得一名女性武将。");
         this._list[this._list.length] = new AchieveLockVO(418,[new BaseRewardVO(10000,100000)],"发现秘密基地","进入大都居委会。");
         this._list[this._list.length] = new AchieveRecordHeroVO(419,20,[new BaseRewardVO(50042,30)],"程公辅佐I","收集20个橙色武将。");
         this._list[this._list.length] = new AchieveRecordHeroVO(420,25,[new EquipRewardVO(42491,3)],"程公辅佐II","收集25个橙色武将。");
         this._list[this._list.length] = new AchieveRecordHeroVO(421,30,[new BaseRewardVO(50043,30)],"东吴名将投靠I","收集30个橙色武将。");
         this._list[this._list.length] = new AchieveRecordHeroVO(422,35,[new EquipRewardVO(42501,3)],"东吴名将投靠II","收集35个橙色武将。");
      }
      
      public function set loadData(param1:Array) : void
      {
         var _loc2_:Array = null;
         var _loc3_:BaseAchieveVO = null;
         var _loc4_:String = null;
         this.startNew();
         if(!param1)
         {
            return;
         }
         for each(_loc4_ in param1)
         {
            _loc2_ = _loc4_.split("H");
            _loc3_ = this.findVO(int(_loc2_[0]));
            if(_loc3_)
            {
               _loc3_.loadData = _loc2_;
            }
         }
      }
      
      public function get saveData() : Array
      {
         var _loc2_:BaseAchieveVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._list)
         {
            _loc1_[_loc1_.length] = _loc2_.saveData;
         }
         return _loc1_;
      }
      
      public function handleLoadAchieve() : void
      {
         var _loc1_:BaseAchieveVO = null;
         for each(_loc1_ in this._list)
         {
            if(!_loc1_.isOpen)
            {
               _loc1_.checkOpen();
            }
         }
      }
      
      public function checkOpen(param1:int, param2:int = 1) : void
      {
         var _loc3_:BaseAchieveVO = this.findVO(param1);
         if(!_loc3_)
         {
            return;
         }
         if(!_loc3_.isOpen)
         {
            _loc3_.checkOpen(param2);
         }
         if(_loc3_.isOpen)
         {
            SignalManager.signalUI.dispatchEvent({
               "signal":GameSignal.REFRESH_UI_TIP,
               "type":"achieve"
            });
         }
      }
      
      public function setFail(param1:int) : void
      {
         var _loc2_:BaseAchieveVO = this.findVO(param1);
         if(!_loc2_ || _loc2_.isOpen)
         {
            return;
         }
         _loc2_.setFail();
      }
      
      public function setReset(param1:int) : void
      {
         var _loc2_:BaseAchieveVO = this.findVO(param1);
         if(!_loc2_ || _loc2_.isOpen)
         {
            return;
         }
         _loc2_.reset();
      }
      
      public function findList(param1:int, param2:Boolean = true) : Array
      {
         var _loc4_:BaseAchieveVO = null;
         if(param1 == ConstData.INT4.v && param2)
         {
            return this.hideList;
         }
         var _loc3_:Array = [];
         for each(_loc4_ in this._list)
         {
            if(_loc4_.label == param1)
            {
               _loc3_[_loc3_.length] = _loc4_;
            }
         }
         _loc3_.sortOn("sortIndex",Array.DESCENDING);
         return _loc3_;
      }
      
      public function findVO(param1:int) : BaseAchieveVO
      {
         var _loc2_:BaseAchieveVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.id == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function curInfor(param1:int) : String
      {
         var _loc3_:int = 0;
         var _loc4_:BaseAchieveVO = null;
         var _loc2_:Array = this.findList(param1,false);
         for each(_loc4_ in _loc2_)
         {
            if(_loc4_.isOpen)
            {
               _loc3_++;
            }
         }
         return _loc3_ + "/" + _loc2_.length;
      }
      
      public function get totalInfor() : String
      {
         var _loc1_:int = 0;
         var _loc2_:BaseAchieveVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.isOpen)
            {
               _loc1_++;
            }
         }
         return _loc1_ + "/" + this._list.length;
      }
      
      private function get hideList() : Array
      {
         var _loc2_:BaseAchieveVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._list)
         {
            if(!(_loc2_.label != ConstData.INT4.v || !_loc2_.isOpen))
            {
               _loc1_[_loc1_.length] = _loc2_;
            }
         }
         _loc1_.sortOn("sortIndex",Array.DESCENDING);
         return _loc1_;
      }
      
      public function get hasReward() : Boolean
      {
         var _loc1_:BaseAchieveVO = null;
         for each(_loc1_ in this._list)
         {
            if(_loc1_.isOpen && !_loc1_.isGet)
            {
               return true;
            }
         }
         return false;
      }
      
      public function hasLabelReward(param1:int) : Boolean
      {
         var _loc3_:BaseAchieveVO = null;
         var _loc2_:Array = this.findList(param1,false);
         for each(_loc3_ in _loc2_)
         {
            if(_loc3_.isOpen && !_loc3_.isGet)
            {
               return true;
            }
         }
         return false;
      }
   }
}

