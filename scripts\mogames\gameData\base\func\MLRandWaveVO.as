package mogames.gameData.base.func
{
   public class MLRandWaveVO extends RandWaveVO
   {
      
      protected var _location:Array;
      
      public function MLRandWaveVO(param1:int, param2:Array, param3:Array, param4:Array, param5:Array)
      {
         super(param1,param2,param3,param4);
         this._location = param5;
      }
      
      public function get location() : Array
      {
         return this._location;
      }
   }
}

