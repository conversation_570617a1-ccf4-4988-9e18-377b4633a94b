package mogames.gameData.mission.extra
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ExtraWave2519
   {
      
      public function ExtraWave2519()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2519);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(852,712000000,4080000,2200000,120,150,300,90,new BossSkillData0(200,{
            "hurt":5000000,
            "roleNum":5
         },7),1016,0);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(775,13350000,575000,520000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(775,13350000,575000,520000,100,100,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(775,13350000,575000,520000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(774,13350000,575000,520000,100,100,200,120,0)));
         _loc2_.addFu(new BossArgVO(777,92000000,1050000,550000,100,120,150,80,new BossSkillData1(10,{"hurt":800000},3),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(775,13350000,575000,520000,100,100,200,120,0)));
         _loc2_.addFu(new BossArgVO(778,92000000,1050000,550000,100,120,150,80,new BossSkillData1(12,{"hurt":800000},3),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(775,13350000,575000,520000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(774,13350000,575000,520000,100,100,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(775,13350000,575000,520000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(774,13350000,575000,520000,100,100,200,120,0)));
         _loc2_.addFu(new BossArgVO(779,92000000,1050000,550000,100,120,150,80,new BossSkillData1(11,{"hurt":800000},3),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(775,13350000,575000,520000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(774,13350000,575000,520000,100,100,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(775,13350000,575000,520000,100,100,200,120,0)));
         _loc2_.addFu(new BossArgVO(778,92000000,1050000,550000,100,120,150,80,new BossSkillData1(11,{"hurt":800000},3),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(775,13350000,575000,520000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(774,13350000,575000,520000,100,100,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(775,13350000,575000,520000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(774,13350000,575000,520000,100,100,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(775,13350000,575000,520000,100,100,200,120,0)));
         _loc2_.addFu(new BossArgVO(779,92000000,1050000,550000,100,120,150,80,new BossSkillData1(12,{"hurt":800000},3),1011,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

