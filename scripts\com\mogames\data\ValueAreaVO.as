package com.mogames.data
{
   import com.mogames.utils.MathUtil;
   
   public class ValueAreaVO
   {
      
      private var _min:Oint = new Oint();
      
      private var _max:Oint = new Oint();
      
      public function ValueAreaVO(param1:int, param2:int)
      {
         super();
         MathUtil.saveINT(this._min,param1);
         MathUtil.saveINT(this._max,param2);
      }
      
      public function isArea(param1:int) : Boolean
      {
         return MathUtil.checkInRate(param1,this.min,this.max);
      }
      
      public function get min() : int
      {
         return MathUtil.loadINT(this._min);
      }
      
      public function get max() : int
      {
         return MathUtil.loadINT(this._max);
      }
   }
}

