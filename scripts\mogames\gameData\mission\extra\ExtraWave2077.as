package mogames.gameData.mission.extra
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ExtraWave2077
   {
      
      public function ExtraWave2077()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2077);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(245,725000,4100,700,80,80,300,90,new BossSkillData0(150,{
            "hurt":5200,
            "roleNum":4
         },5),1004,0);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(248,12400,1410,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(248,12400,1410,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(248,12400,1410,100,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(241,170000,4100,450,50,80,150,80,new BossSkillData1(16,{
            "hurt":3850,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(12,new RoleArgVO(248,12400,1410,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(14,new RoleArgVO(248,12400,1410,100,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(241,170000,4100,450,50,80,150,80,new BossSkillData0(150,{
            "hurt":3900,
            "roleNum":3
         },2),1016,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(248,12400,1410,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(248,12400,1410,100,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(241,170000,4100,450,50,80,150,80,new BossSkillData1(16,{
            "hurt":4053,
            "keepTime":8,
            "hurtCount":3
         },2),1015,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(14,new RoleArgVO(248,12400,1410,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(14,new RoleArgVO(248,12400,1410,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(248,12400,1410,100,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(241,170000,4100,450,50,80,150,80,new BossSkillData0(150,{"hurt":3710},2),1011,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

