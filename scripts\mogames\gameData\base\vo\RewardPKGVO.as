package mogames.gameData.base.vo
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   
   public class RewardPKGVO
   {
      
      private var _id:Oint = new Oint();
      
      private var _rewards:Array;
      
      public function RewardPKGVO(param1:int, param2:Array)
      {
         super();
         MathUtil.saveINT(this._id,param1);
         this._rewards = param2;
      }
      
      public function get id() : int
      {
         return MathUtil.loadINT(this._id);
      }
      
      public function get rewards() : Array
      {
         return this._rewards;
      }
   }
}

