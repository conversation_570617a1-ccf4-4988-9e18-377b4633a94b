package mogames.gameBuff.base
{
   import mogames.gameData.buff.ConstBuffVO;
   import mogames.gameData.buff.base.BuffVO;
   import mogames.gameRole.base.IRole;
   
   public class LoopRoleBuff extends RoleBuff
   {
      
      public function LoopRoleBuff(param1:ConstBuffVO)
      {
         super(param1);
      }
      
      override public function start(param1:IRole, param2:BuffVO) : void
      {
         super.start(param1,param2);
         _timer.setInterval(1,_buffVO.time,this.onLoop,handlerEnd,true,true);
      }
      
      protected function onLoop() : void
      {
      }
   }
}

