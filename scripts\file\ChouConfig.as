package file
{
   import mogames.gameData.base.func.WJChouVO;
   import mogames.gameData.base.vo.BaseRewardVO;
   
   public class ChouConfig
   {
      
      private static var _instance:ChouConfig;
      
      public var timeStr:String;
      
      public var oneReward:BaseRewardVO;
      
      public var tenReward:BaseRewardVO;
      
      public var url:String = "https://my.4399.com/forums/thread-58803589";
      
      private var _list:Array;
      
      public function ChouConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : ChouConfig
      {
         if(!_instance)
         {
            _instance = new ChouConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this.timeStr = "";
         this.oneReward = new BaseRewardVO(10300,1);
         this.tenReward = new BaseRewardVO(10300,10);
         this._list = [];
         this._list[this._list.length] = new WJChouVO(1,526,3,166,new BaseRewardVO(18813,1));
         this._list[this._list.length] = new WJChouVO(2,304,18,116,new BaseRewardVO(18814,1));
         this._list[this._list.length] = new WJChouVO(3,455,16,87,new BaseRewardVO(18815,1));
         this._list[this._list.length] = new WJChouVO(4,319,17,98,new BaseRewardVO(18816,1));
         this._list[this._list.length] = new WJChouVO(5,457,28,32,new BaseRewardVO(18817,1));
         this._list[this._list.length] = new WJChouVO(6,456,23,47,new BaseRewardVO(18818,1));
      }
      
      public function get list() : Array
      {
         return this._list;
      }
      
      public function findVO(param1:int) : WJChouVO
      {
         var _loc2_:WJChouVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.infor.roleID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

