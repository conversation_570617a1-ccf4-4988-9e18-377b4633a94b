package mogames.gameData.flag.base
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   
   public class FlagVO
   {
      
      protected var _id:Oint = new Oint();
      
      protected var _flag:Oint = new Oint();
      
      protected var _total:Oint = new Oint();
      
      protected var _daily:<PERSON><PERSON>an;
      
      protected var _over:<PERSON><PERSON><PERSON>;
      
      protected var _save:<PERSON><PERSON><PERSON>;
      
      public function FlagVO(param1:int, param2:int = 1, param3:<PERSON><PERSON>an = false, param4:<PERSON><PERSON><PERSON> = false, param5:<PERSON>olean = true)
      {
         super();
         MathUtil.saveINT(this._id,param1);
         MathUtil.saveINT(this._flag,0);
         MathUtil.saveINT(this._total,param2);
         this._daily = param4;
         this._over = param3;
         this._save = param5;
      }
      
      public function dailyRefresh() : void
      {
         if(!this._daily)
         {
            return;
         }
         this.setValue(0);
      }
      
      public function changeValue(param1:int) : void
      {
         this.setValue(MathUtil.loadINT(this._flag) + param1);
      }
      
      public function setValue(param1:int) : void
      {
         if(this._over)
         {
            MathUtil.saveINT(this._flag,param1);
            return;
         }
         if(param1 < this.total)
         {
            MathUtil.saveINT(this._flag,param1);
         }
         else if(param1 <= 0)
         {
            MathUtil.saveINT(this._flag,0);
         }
         else
         {
            MathUtil.saveINT(this._flag,this.total);
         }
      }
      
      public function get isComplete() : Boolean
      {
         return this.cur >= this.total;
      }
      
      public function get id() : int
      {
         return MathUtil.loadINT(this._id);
      }
      
      public function get cur() : int
      {
         return MathUtil.loadINT(this._flag);
      }
      
      public function get total() : int
      {
         return MathUtil.loadINT(this._total);
      }
      
      public function get left() : int
      {
         return this.total - this.cur;
      }
      
      public function get needSave() : Boolean
      {
         return this._save;
      }
   }
}

