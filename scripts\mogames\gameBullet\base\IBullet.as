package mogames.gameBullet.base
{
   import com.mogames.display.IDestroy;
   import mogames.gameData.bullet.BulletConstVO;
   import mogames.gameData.role.battle.HurtData;
   import mogames.gameRole.base.IRole;
   
   public interface IBullet extends IDestroy
   {
      
      function createBullet(param1:BulletConstVO, param2:HurtData, param3:IRole) : void;
      
      function start(param1:int, param2:int) : void;
      
      function recyle() : void;
   }
}

