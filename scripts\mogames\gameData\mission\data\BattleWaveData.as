package mogames.gameData.mission.data
{
   public class BattleWaveData
   {
      
      public var cur:int;
      
      public var next:int;
      
      public var total:int;
      
      public var isTrain:Boolean;
      
      public var isPause:Boolean;
      
      public function BattleWaveData()
      {
         super();
      }
      
      public function init() : void
      {
         this.isTrain = false;
         this.isPause = false;
      }
      
      public function get lastWave() : Boolean
      {
         return this.next == this.total;
      }
   }
}

