package mogames.gameData.bag.vo
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import mogames.gameData.bag.base.BagLockVO;
   import mogames.gameData.master.MasterProxy;
   
   public class BagFreeVO1 extends BagLockVO
   {
      
      private var _level:Oint = new Oint();
      
      public function BagFreeVO1(param1:int, param2:String = "")
      {
         super(param1,param2);
         MathUtil.saveINT(this._level,20);
      }
      
      override public function get isOpen() : Boolean
      {
         return MasterProxy.instance().masterVO.level >= MathUtil.loadINT(this._level);
      }
   }
}

