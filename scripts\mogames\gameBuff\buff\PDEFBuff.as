package mogames.gameBuff.buff
{
   import mogames.gameBuff.base.TimeRoleBuff;
   import mogames.gameData.buff.ConstBuffVO;
   
   public class PDEFBuff extends TimeRoleBuff
   {
      
      public function PDEFBuff(param1:ConstBuffVO)
      {
         super(param1);
      }
      
      override protected function createData() : void
      {
         _owner.isPDEF = true;
      }
      
      override protected function onCleanRole() : void
      {
         _owner.isPDEF = false;
      }
   }
}

