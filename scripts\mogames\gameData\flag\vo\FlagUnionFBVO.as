package mogames.gameData.flag.vo
{
   import mogames.gameData.ServerProxy;
   import mogames.gameData.flag.base.FlagVO;
   
   public class FlagUnionFBVO extends FlagVO
   {
      
      public function FlagUnionFBVO(param1:int)
      {
         super(param1,1,false,true,true);
      }
      
      override public function dailyRefresh() : void
      {
         if(!ServerProxy.instance().nextWeek)
         {
            return;
         }
         setValue(0);
      }
   }
}

