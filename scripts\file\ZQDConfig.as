package file
{
   import com.mogames.utils.MathUtil;
   import mogames.ConstData;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.fuben.vo.FubenVO;
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ZQDConfig
   {
      
      private static var _instance:ZQDConfig;
      
      public var waveData:WaveDataVO;
      
      public var shouling:Array;
      
      public function ZQDConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : ZQDConfig
      {
         if(!_instance)
         {
            _instance = new ZQDConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         var _loc1_:OneWaveVO = null;
         this.shouling = [new BossArgVO(271,100000,800,0,50,25,150,150,null,0,0),new Boss<PERSON>rgVO(271,100000,400,0,50,25,150,150,null,0,0),new BossArgVO(271,100000,600,0,50,25,150,150,null,0,0),new BossArgVO(271,100000,600,0,50,25,150,150,null,0,0),new BossArgVO(271,100000,800,0,50,25,150,150,null,0,0)];
         this.waveData = new WaveDataVO(0);
         this.waveData.limitBR = new WaveLimitVO(10000,5,5);
         this.waveData.zhuBoss = new BossArgVO(1003,500000,2500,900,50,80,300,150,new BossSkillData1(15,{"hurt":3000},5),4004,0);
         _loc1_ = new OneWaveVO(300);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(271,200000,10000,50,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
      }
      
      public function newReward(param1:Boolean) : Array
      {
         if(!param1)
         {
            return this.loseReward;
         }
         return this.winReward;
      }
      
      private function get winReward() : Array
      {
         var _loc5_:int = 0;
         var _loc1_:Array = [];
         var _loc2_:int = ConstData.INT2.v;
         if(MathUtil.checkOdds(300))
         {
            _loc2_ = ConstData.INT3.v;
         }
         var _loc3_:FubenVO = FubenConfig.instance().findFuben(407);
         var _loc4_:Array = _loc3_.drops.slice(1);
         var _loc6_:int = 0;
         while(_loc6_ < _loc2_)
         {
            _loc5_ = Math.random() * _loc4_.length;
            _loc1_[_loc1_.length] = _loc4_[_loc5_];
            _loc4_.splice(_loc5_,1);
            _loc6_++;
         }
         return _loc1_;
      }
      
      private function get loseReward() : Array
      {
         return [new BaseRewardVO(10000,35000)];
      }
   }
}

