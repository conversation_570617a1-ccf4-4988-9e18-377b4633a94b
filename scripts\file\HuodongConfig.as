package file
{
   import mogames.gameData.GameProxy;
   import mogames.gameData.huodong.HuodongVO;
   import mogames.gameData.huodong.vo.BaoXiaoExchange;
   import mogames.gameData.huodong.vo.DuJiaExchange;
   import mogames.gameData.huodong.vo.JifenExchange0;
   import mogames.gameUI.main.gift.GiftCodeFrame;
   import mogames.gameUI.main.gift.GiftGameFrame;
   
   public class HuodongConfig
   {
      
      private static var _instance:HuodongConfig;
      
      private var _list:Array;
      
      public var zhuoquVO:HuodongVO;
      
      public function HuodongConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : HuodongConfig
      {
         if(!_instance)
         {
            _instance = new HuodongConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = [];
      }
      
      public function openHuodong(param1:HuodongVO) : void
      {
         switch(param1.type)
         {
            case "DUJIA":
               GiftCodeFrame.instance().init(75,new DuJiaExchange(),param1,GameProxy.instance().dujia960,"https://www.4399.com/djyx/");
               break;
            case "BAOXIAO":
               GiftCodeFrame.instance().init(0,new BaoXiaoExchange(),param1,GameProxy.instance().baoxiao380,"https://huodong.4399.com/baoxiao/mall/pc/");
               break;
            case "YUANXIAO":
               GiftGameFrame.instance().init(GameProxy.instance().yuanxiao830,param1);
               break;
            case "ZHUOQU":
               GiftCodeFrame.instance().init(33625,new JifenExchange0(),param1,GameProxy.instance().zhuoqu820,"https://my.4399.com/forums/thread-59592687");
         }
      }
      
      public function get huodongs() : Array
      {
         return this._list;
      }
   }
}

