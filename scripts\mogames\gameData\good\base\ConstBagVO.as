package mogames.gameData.good.base
{
   import com.mogames.data.Onum;
   import com.mogames.utils.MathUtil;
   
   public class ConstBagVO extends ConstGoodVO
   {
      
      protected var _cd:Onum = new Onum();
      
      public function ConstBagVO(param1:int, param2:Number, param3:int, param4:int, param5:String, param6:String, param7:String, param8:String)
      {
         super(param1,param3,1,param4,0,param5,param6,param7,param8);
         MathUtil.saveNUM(this._cd,param2);
      }
      
      public function get constCD() : Number
      {
         return MathUtil.loadNUM(this._cd);
      }
      
      override public function get type() : String
      {
         return "行囊物品";
      }
   }
}

