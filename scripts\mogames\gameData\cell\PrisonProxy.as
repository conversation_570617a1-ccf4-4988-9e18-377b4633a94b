package mogames.gameData.cell
{
   import com.mogames.utils.MathUtil;
   import com.mogames.utils.TxtUtil;
   import mogames.gameData.main.MainProxy;
   import mogames.gameData.role.DealHandler;
   import mogames.gameUI.main.prison.PrisonHandler;
   
   public class PrisonProxy
   {
      
      private static var _instance:PrisonProxy;
      
      private var _list:Array;
      
      private var _handler:PrisonHandler;
      
      private var _deal:DealHandler;
      
      public function PrisonProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this._handler = new PrisonHandler();
         this._deal = new DealHandler();
      }
      
      public static function instance() : PrisonProxy
      {
         if(!_instance)
         {
            _instance = new PrisonProxy();
         }
         return _instance;
      }
      
      public function startNew() : void
      {
         this._list = [];
         var _loc1_:int = 0;
         while(_loc1_ < 7)
         {
            this._list[_loc1_] = new CellGameVO(_loc1_);
            _loc1_++;
         }
      }
      
      public function set loadData(param1:Array) : void
      {
         var _loc2_:Array = null;
         var _loc3_:CellGameVO = null;
         var _loc4_:String = null;
         this.startNew();
         if(!param1)
         {
            return;
         }
         for each(_loc4_ in param1)
         {
            _loc2_ = _loc4_.split("H");
            _loc3_ = this.findCell(int(_loc2_[0]));
            if(_loc3_)
            {
               _loc3_.loadData = _loc2_;
            }
         }
      }
      
      public function get saveData() : Array
      {
         var _loc2_:CellGameVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._list)
         {
            _loc1_[_loc1_.length] = _loc2_.saveData;
         }
         return _loc1_;
      }
      
      public function refreshPrisoner() : void
      {
         var _loc1_:int = 0;
         var _loc2_:CellGameVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.hasPrisoner)
            {
               if(this._handler.isEscape(_loc2_.level,_loc2_.heroVO.quality))
               {
                  this._deal.breakPrison(_loc2_.heroVO.heroInfo);
                  MainProxy.instance().addTip(_loc2_.heroVO.heroInfo.name + "越狱不知所踪！");
                  _loc2_.removePrisoner();
               }
               else
               {
                  _loc1_ = MathUtil.randomNum(30,60);
                  _loc2_.changeHunger(_loc1_);
                  if(_loc2_.isDead)
                  {
                     this._deal.killHero(_loc2_.heroVO.heroInfo);
                     MainProxy.instance().addTip(_loc2_.heroVO.heroInfo.name + "饿S了！");
                     _loc2_.removePrisoner();
                  }
                  else
                  {
                     MainProxy.instance().addTip(_loc2_.heroVO.heroInfo.name + "的饥饿度增加了" + TxtUtil.setColor(_loc1_ + "点！") + "<br>" + TxtUtil.setColor(TxtUtil.addKuoHao("当前饥饿：" + _loc2_.hunger)));
                  }
               }
            }
         }
      }
      
      public function hasHero(param1:int) : Boolean
      {
         var _loc2_:CellGameVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.hasPrisoner)
            {
               if(_loc2_.prisoner == param1)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function addPrisoner(param1:int, param2:int) : void
      {
         this.removePrisoner(param1);
         this.findCell(param2).addPrisoner(param1);
         MainProxy.instance().cleanHero(param1);
      }
      
      public function removePrisoner(param1:int) : void
      {
         var _loc2_:CellGameVO = null;
         for each(_loc2_ in this._list)
         {
            if(!(!_loc2_.isOpen || !_loc2_.hasPrisoner))
            {
               if(_loc2_.prisoner == param1)
               {
                  _loc2_.removePrisoner();
                  return;
               }
            }
         }
      }
      
      public function get cells() : Array
      {
         return this._list;
      }
      
      public function get hasCell() : Boolean
      {
         var _loc1_:CellGameVO = null;
         for each(_loc1_ in this._list)
         {
            if(_loc1_.isOpen && !_loc1_.hasPrisoner)
            {
               return true;
            }
         }
         return false;
      }
      
      public function findCell(param1:int) : CellGameVO
      {
         var _loc2_:CellGameVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.index == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

