package mogames.gameBullet
{
   import mogames.gameBullet.base.PaoBullet;
   import mogames.gameBullet.base.TSCBullet;
   import mogames.gameBullet.base.TrackBullet;
   
   public class BulletFactory
   {
      
      public function BulletFactory()
      {
         super();
      }
      
      public static function findBulletClass(param1:int) : Object
      {
         switch(param1)
         {
            case 1:
               return TrackBullet;
            case 2:
               return PaoBullet;
            case 3:
               return TSCBullet;
            default:
               return TrackBullet;
         }
      }
   }
}

