package mogames.gameData.mall.vo
{
   import com.mogames.utils.TxtUtil;
   import mogames.gameData.base.RewardProxy;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.game.LackVO;
   import mogames.gameData.good.vo.GameGoodVO;
   import mogames.gameData.master.MasterProxy;
   
   public class MallGoldVO extends MallBaseVO
   {
      
      public function MallGoldVO(param1:int, param2:BaseRewardVO)
      {
         super(param1,1,param2);
      }
      
      override public function checkLack(param1:int) : LackVO
      {
         return MasterProxy.instance().checkValue(10000,param1 * price);
      }
      
      override public function handlerBuy(param1:int, param2:Function) : void
      {
         var func:Function = null;
         var buyNum:int = param1;
         var okFunc:Function = param2;
         func = function():void
         {
            MasterProxy.instance().changeValue(10000,-buyNum * price);
            if(okFunc != null)
            {
               okFunc();
            }
         };
         var vo:GameGoodVO = rewardVO.newGood();
         if(buyNum > 1)
         {
            vo.amount *= buyNum;
         }
         RewardProxy.instance().addReward([vo],func);
      }
      
      override public function askBuy(param1:int) : String
      {
         return "是否花费" + TxtUtil.setColor("银票X" + param1 * price,"ffff00") + "购买" + TxtUtil.setColor(param1 + "个" + rewardVO.constGood.name,"99ff00") + "？";
      }
   }
}

