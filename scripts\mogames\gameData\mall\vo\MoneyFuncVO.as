package mogames.gameData.mall.vo
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import mogames.gameData.game.LackVO;
   import mogames.gameData.master.MasterProxy;
   import mogames.gameNet.MoneyProxy;
   import mogames.gameNet.MulitProxy;
   import mogames.gameNet.PKGProxy;
   import mogames.gameNet.SaveManager;
   import mogames.gameUI.prompt.LockMessage;
   
   public class MoneyFuncVO
   {
      
      private var _mallID:Oint = new Oint();
      
      private var _price:Oint = new Oint();
      
      private var _save:Boolean;
      
      public function MoneyFuncVO(param1:int, param2:int)
      {
         super();
         MathUtil.saveINT(this._mallID,param1);
         MathUtil.saveINT(this._price,param2);
      }
      
      public function checkLack(param1:int = 1) : LackVO
      {
         if(MoneyProxy.instance().money < this.price * param1)
         {
            return new LackVO("金票不足！");
         }
         return MasterProxy.instance().checkValue(10010,this.price * param1);
      }
      
      public function handlerBuy(param1:Function, param2:Boolean, param3:int = 1) : void
      {
         var sendBuy:Function = null;
         var okFunc:Function = param1;
         var $save:Boolean = param2;
         var $buyNum:int = param3;
         sendBuy = function():void
         {
            var func:Function = null;
            func = function():void
            {
               MasterProxy.instance().changeValue(10010,-price * $buyNum);
               LockMessage.instance().destroy();
               if(okFunc != null)
               {
                  okFunc();
               }
               if(_save)
               {
                  SaveManager.instance().saveAuto(false);
               }
            };
            handlerMoney($buyNum,func);
         };
         this._save = $save;
         LockMessage.instance().showMsg("正在处理数据，请稍等！");
         MulitProxy.instance().checkMulit(sendBuy,LockMessage.instance().destroy);
      }
      
      private function handlerMoney(param1:int, param2:Function) : void
      {
         var func:Function = null;
         var $buyNum:int = param1;
         var okFunc:Function = param2;
         func = function(param1:Object):void
         {
            if(!param1 || !param1.propId)
            {
               return;
            }
            if(int(param1.propId) != mallID)
            {
               return;
            }
            MoneyProxy.instance().refreshSpend(okFunc);
         };
         var temp:Object = new Object();
         temp.propId = String(this.mallID);
         temp.count = $buyNum;
         temp.price = this.price;
         temp.idx = PKGProxy.instance().curIndex;
         MoneyProxy.instance().moneySDK.reqMallBuy(temp,func);
      }
      
      private function get mallID() : int
      {
         return MathUtil.loadINT(this._mallID);
      }
      
      public function get price() : int
      {
         return MathUtil.loadINT(this._price);
      }
   }
}

