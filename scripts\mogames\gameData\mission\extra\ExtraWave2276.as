package mogames.gameData.mission.extra
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ExtraWave2276
   {
      
      public function ExtraWave2276()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2276);
         _loc1_.limitBR = new WaveLimitVO(99999999,0.85,0.85);
         _loc1_.zhuBoss = new BossArgVO(764,4500000,40000,5000,80,80,300,90,new BossSkillData0(150,{"hurt":76500},5),1010,0);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(753,200000,10000,110,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(765,850000,15600,450,50,80,150,80,new BossSkillData0(250,{"hurt":38000},2),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(752,200000,10000,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(751,200000,10000,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(750,200000,10000,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(753,200000,10000,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(751,200000,10000,110,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(760,850000,15600,450,50,80,150,80,new BossSkillData1(10,{"hurt":38500},2),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(750,200000,10000,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(752,200000,10000,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(750,200000,10000,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(751,200000,10000,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(753,200000,10000,110,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(762,850000,15600,450,50,80,150,80,new BossSkillData1(12,{
            "hurt":38500,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(750,200000,10000,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(752,200000,10000,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(750,200000,10000,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(752,200000,10000,110,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(762,850000,15600,450,50,80,150,80,new BossSkillData1(12,{
            "hurt":38500,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(752,200000,10000,110,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(763,850000,15600,450,50,80,150,80,new BossSkillData0(250,{"hurt":38500},2),1008,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

