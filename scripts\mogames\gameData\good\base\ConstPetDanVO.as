package mogames.gameData.good.base
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   
   public class ConstPetDanVO extends ConstGoodVO
   {
      
      private var _max:Oint = new Oint();
      
      private var _petID:Oint = new Oint();
      
      public function ConstPetDanVO(param1:int, param2:int, param3:int, param4:int, param5:String, param6:String, param7:String, param8:String)
      {
         super(param1,4,1,param4,1,param5,param6,param7,param8);
         MathUtil.saveINT(this._max,param3);
         MathUtil.saveINT(this._petID,param2);
      }
      
      public function get isMax() : Boolean
      {
         return MathUtil.loadINT(this._max) == 1;
      }
      
      public function get petID() : int
      {
         return MathUtil.loadINT(this._petID);
      }
   }
}

