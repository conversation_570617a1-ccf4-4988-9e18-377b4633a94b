package com.mogames.system
{
   import mogames.Layers;
   
   public class SysTimer
   {
      
      private var _count:int;
      
      private var _interval:int;
      
      private var _total:int;
      
      private var _loop:Boolean;
      
      private var _running:Boolean;
      
      private var _processFunc:Function;
      
      private var _completeFunc:Function;
      
      public function SysTimer()
      {
         super();
      }
      
      public function setLoop(param1:Number, param2:Function = null, param3:Boolean = true) : void
      {
         this._loop = true;
         this._interval = param1 * SysRender.FPS;
         this._processFunc = param2;
         this._count = 0;
         if(param3)
         {
            this.start();
         }
      }
      
      public function setInterval(param1:Number, param2:Number = 0, param3:Function = null, param4:Function = null, param5:Boolean = true, param6:Boolean = false) : void
      {
         this._loop = false;
         this._interval = param1 * SysRender.FPS;
         this._total = param2 * SysRender.FPS;
         this._processFunc = param3;
         this._completeFunc = param4;
         this._count = 0;
         if(param5)
         {
            this.start();
         }
         if(param6 && this._processFunc != null)
         {
            this._processFunc();
         }
      }
      
      public function setTimeOut(param1:Number, param2:Function = null, param3:Boolean = true) : void
      {
         this._loop = false;
         this._interval = param1 * SysRender.FPS;
         this._total = param1 * SysRender.FPS;
         this._processFunc = null;
         this._completeFunc = param2;
         this._count = 1;
         if(param3)
         {
            this.start();
         }
      }
      
      public function stop() : void
      {
         Layers.looper.remove(this.startCount);
         this._running = false;
      }
      
      public function start() : void
      {
         Layers.looper.add(this.startCount);
         this._running = true;
      }
      
      public function reset(param1:Boolean = false) : void
      {
         this._count = 1;
         if(param1)
         {
            this.stop();
         }
         else
         {
            this.start();
         }
      }
      
      private function startCount() : void
      {
         if(!this._loop && this._count >= this._total)
         {
            this.stop();
            if(this._completeFunc != null)
            {
               this._completeFunc();
            }
         }
         else if(this._count % this._interval == 0)
         {
            if(this._processFunc != null)
            {
               this._processFunc();
            }
         }
         ++this._count;
      }
      
      public function get running() : Boolean
      {
         return this._running;
      }
      
      public function get leftTime() : Number
      {
         return Math.ceil((this._total - this._count) / SysRender.FPS);
      }
      
      public function get intervalTime() : Number
      {
         return this._interval / SysRender.FPS;
      }
      
      public function get curCount() : int
      {
         return this._count;
      }
      
      public function set curCount(param1:int) : void
      {
         this._count = param1;
      }
      
      public function destroy() : void
      {
         this.stop();
         this._processFunc = null;
         this._completeFunc = null;
      }
   }
}

