package mogames.gameData.mission.base
{
   import com.mogames.data.IntList;
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import file.TownConfig;
   import mogames.gameData.mission.MissionProxy;
   
   public class MissionConstVO
   {
      
      private var _areaID:int;
      
      private var _mid:Oint = new Oint();
      
      private var _list:IntList;
      
      public var name:String;
      
      public function MissionConstVO(param1:int, param2:int, param3:Array, param4:String)
      {
         super();
         MathUtil.saveINT(this._mid,param2);
         this._areaID = param1;
         this._list = new IntList(param3);
         this.name = param4;
      }
      
      public function get mid() : int
      {
         return MathUtil.loadINT(this._mid);
      }
      
      public function get isOpen() : Boolean
      {
         var _loc1_:int = 0;
         var _loc2_:int = this._list.length;
         if(_loc2_ <= 0)
         {
            return true;
         }
         while(_loc1_ < _loc2_)
         {
            if(MissionProxy.instance().isFinish(this._list.findValue(_loc1_)))
            {
               return true;
            }
            _loc1_++;
         }
         return false;
      }
      
      public function get areaID() : int
      {
         return this._areaID;
      }
      
      public function get index() : int
      {
         return this._areaID % 200;
      }
      
      public function get isLarge() : Boolean
      {
         return TownConfig.instance().findTown(this.mid) != null;
      }
      
      public function get normalSkin() : String
      {
         if(String(this.mid).charAt(0) == "2")
         {
            return "TOWN_NORMAL_XN_ITEM" + int(this.isLarge);
         }
         return "TOWN_NORMAL_ITEM" + int(this.isLarge);
      }
      
      public function get extraSkin() : String
      {
         if(String(this.mid).charAt(0) == "2")
         {
            return "TOWN_EXTRA_XN_ITEM" + int(this.isLarge);
         }
         return "TOWN_EXTRA_ITEM" + int(this.isLarge);
      }
   }
}

