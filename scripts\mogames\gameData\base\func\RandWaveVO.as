package mogames.gameData.base.func
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   
   public class RandWaveVO
   {
      
      private var _total:Oint = new Oint();
      
      private var _fuBoss:Array;
      
      private var _times:Array;
      
      private var _enemies:Array;
      
      public function RandWaveVO(param1:int, param2:Array, param3:Array, param4:Array)
      {
         super();
         MathUtil.saveINT(this._total,param1);
         this._fuBoss = param2;
         this._times = param3;
         this._enemies = param4;
      }
      
      public function get total() : int
      {
         return MathUtil.loadINT(this._total);
      }
      
      public function findFu(param1:int) : int
      {
         var _loc2_:Object = null;
         for each(_loc2_ in this._fuBoss)
         {
            if(_loc2_.index == param1)
            {
               return _loc2_.num;
            }
         }
         return 0;
      }
      
      public function findTime(param1:int) : Number
      {
         return this._times[param1];
      }
      
      public function findEmemies(param1:int) : int
      {
         return this._enemies[param1];
      }
   }
}

