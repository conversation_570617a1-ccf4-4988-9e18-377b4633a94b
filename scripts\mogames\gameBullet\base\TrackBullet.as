package mogames.gameBullet.base
{
   import flash.geom.Point;
   import mogames.gameData.bullet.BulletConstVO;
   import mogames.gameData.role.battle.HurtData;
   import mogames.gameRole.base.IRole;
   
   public class TrackBullet extends BaseBullet
   {
      
      private var _xspeed:Number;
      
      private var _yspeed:Number;
      
      private var _location:Point;
      
      public function TrackBullet()
      {
         super();
         this._location = new Point();
      }
      
      override public function createBullet(param1:BulletConstVO, param2:HurtData, param3:IRole) : void
      {
         super.createBullet(param1,param2,param3);
         this._location.setTo(_target.x,_target.y - _target.height * 0.5);
      }
      
      override protected function updateLocation() : void
      {
         if(canHurt)
         {
            handlerEnd();
            return;
         }
         if(targetEnabled)
         {
            this._location.setTo(_target.x,_target.y - _target.height * 0.5);
         }
         this.moveToTarget(this._location);
      }
      
      protected function moveToTarget(param1:Point) : void
      {
         this._xspeed = _speed * Math.cos(Math.atan2(param1.y - _bmc.y,param1.x - _bmc.x));
         this._yspeed = _speed * Math.sin(Math.atan2(param1.y - _bmc.y,param1.x - _bmc.x));
         _bmc.x += this._xspeed;
         _bmc.y += this._yspeed;
         _bmc.rotation = Math.atan2(param1.y - _bmc.y,param1.x - _bmc.x) / Math.PI * 180;
         if(Point.distance(_bmc.location,param1) < _speed * 2)
         {
            handlerEnd();
         }
      }
   }
}

