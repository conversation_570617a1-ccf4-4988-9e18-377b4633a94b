package mogames.gameData.build.vo
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   
   public class GameBuildVO
   {
      
      private var _constVO:ConstBuildVO;
      
      private var _level:Oint = new Oint();
      
      private var _maxLv:Oint = new Oint();
      
      public function GameBuildVO(param1:ConstBuildVO)
      {
         super();
         this._constVO = param1;
         MathUtil.saveINT(this._level,0);
         MathUtil.saveINT(this._maxLv,3);
      }
      
      public function levelup() : void
      {
         MathUtil.saveINT(this._level,this.level + 1);
      }
      
      public function get level() : int
      {
         return MathUtil.loadINT(this._level);
      }
      
      public function get isMaxLv() : Bo<PERSON>an
      {
         return this.level >= MathUtil.loadINT(this._maxLv);
      }
      
      public function get learnNeeds() : Array
      {
         return this.constVO.findLearn(this.level);
      }
      
      public function get constVO() : ConstBuildVO
      {
         return this._constVO;
      }
      
      public function get saveData() : String
      {
         return this._constVO.buildID + "H" + this.level;
      }
      
      public function set loadData(param1:Array) : void
      {
         MathUtil.saveINT(this._level,param1[1]);
      }
   }
}

