package com.mogames.display
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   
   public class BMPSprite extends EventSprite
   {
      
      private var _bmp:Bitmap;
      
      public function BMPSprite(param1:BitmapData = null)
      {
         super();
         this._bmp = new Bitmap();
         addChild(this._bmp);
         mouseChildren = false;
         this.bmd = param1;
      }
      
      public function set bmd(param1:BitmapData) : void
      {
         this._bmp.bitmapData = param1;
         if(!param1)
         {
            return;
         }
         this._bmp.x = -this._bmp.width * 0.5;
         this._bmp.y = -this._bmp.height * 0.5;
         this._bmp.smoothing = true;
      }
      
      public function setWH(param1:int, param2:int) : void
      {
         this._bmp.width = param1;
         this._bmp.height = param2;
      }
      
      public function setPivot(param1:int, param2:int) : void
      {
         this._bmp.x = param1;
         this._bmp.y = param2;
      }
      
      override public function destroy() : void
      {
         super.destroy();
         this._bmp = null;
      }
      
      public function dispose() : void
      {
         super.destroy();
         this._bmp.bitmapData.dispose();
         this._bmp = null;
      }
   }
}

