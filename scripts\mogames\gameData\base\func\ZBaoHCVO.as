package mogames.gameData.base.func
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import mogames.gameData.base.UseProxy;
   import mogames.gameData.base.vo.EquipRewardVO;
   import mogames.gameData.depot.DepotProxy;
   import mogames.gameData.game.LackVO;
   import mogames.gameData.master.MasterProxy;
   import mogames.gameData.role.HeroProxy;
   
   public class ZBaoHCVO
   {
      
      private var _reward:EquipRewardVO;
      
      private var _gold:Oint = new Oint();
      
      private var _jin:Oint = new Oint();
      
      private var _rate:Oint = new Oint();
      
      private var _list:Array;
      
      public function ZBaoHCVO(param1:int, param2:int, param3:int, param4:int, param5:Array)
      {
         super();
         this._reward = new EquipRewardVO(param1,3);
         MathUtil.saveINT(this._gold,param2);
         MathUtil.saveINT(this._jin,param3);
         MathUtil.saveINT(this._rate,param4);
         this._list = param5;
      }
      
      public function checkLack() : LackVO
      {
         var _loc1_:LackVO = null;
         _loc1_ = MasterProxy.instance().checkValue(10000,this.gold);
         if(_loc1_ != null)
         {
            return _loc1_;
         }
         _loc1_ = MasterProxy.instance().checkValue(10007,this.jin);
         if(_loc1_ != null)
         {
            return _loc1_;
         }
         _loc1_ = UseProxy.instance().checkLack(this._list);
         if(_loc1_ != null)
         {
            return _loc1_;
         }
         return null;
      }
      
      public function handlerUse() : void
      {
         MasterProxy.instance().changeValue(10000,-this.gold);
         MasterProxy.instance().changeValue(10007,-this.jin);
         UseProxy.instance().useStuff(this._list,null);
      }
      
      public function get reward() : EquipRewardVO
      {
         return this._reward;
      }
      
      public function get gold() : int
      {
         return MathUtil.loadINT(this._gold);
      }
      
      public function get jin() : int
      {
         return MathUtil.loadINT(this._jin);
      }
      
      public function get rate() : int
      {
         return MathUtil.loadINT(this._rate);
      }
      
      public function get needList() : Array
      {
         return this._list;
      }
      
      public function get hasGet() : Boolean
      {
         var _loc1_:int = this._reward.constGood.id;
         return HeroProxy.instance().hasEquip(_loc1_) || DepotProxy.instance().findNum(_loc1_) > 0;
      }
   }
}

