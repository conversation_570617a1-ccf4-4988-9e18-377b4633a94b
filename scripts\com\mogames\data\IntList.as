package com.mogames.data
{
   import com.mogames.utils.MathUtil;
   import flash.utils.Dictionary;
   
   public class IntList
   {
      
      private var _dic:Dictionary;
      
      private var _length:int;
      
      public function IntList(param1:Array)
      {
         var _loc3_:Oint = null;
         super();
         this._dic = new Dictionary();
         this._length = param1.length;
         var _loc2_:int = 0;
         while(_loc2_ < this._length)
         {
            _loc3_ = new Oint();
            MathUtil.saveINT(_loc3_,param1[_loc2_]);
            this._dic[_loc2_] = _loc3_;
            _loc2_++;
         }
      }
      
      public function findValue(param1:int) : int
      {
         return MathUtil.loadINT(this._dic[param1]);
      }
      
      public function hasValue(param1:int) : Boolean
      {
         var _loc2_:int = 0;
         while(_loc2_ < this._length)
         {
            if(this.findValue(_loc2_) == param1)
            {
               return true;
            }
            _loc2_++;
         }
         return false;
      }
      
      public function get randomValue() : int
      {
         var _loc1_:int = Math.random() * this.length;
         return this.findValue(_loc1_);
      }
      
      public function get length() : int
      {
         return this._length;
      }
   }
}

