package mogames.gameData.boon
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import file.HuoYueConfig;
   import mogames.gameData.base.RewardProxy;
   import mogames.gameData.flag.FlagProxy;
   
   public class HYRewardVO
   {
      
      private var _flag:Oint = new Oint();
      
      private var _needNum:Oint = new Oint();
      
      private var _rewards:Array;
      
      public function HYRewardVO(param1:int, param2:int, param3:Array)
      {
         super();
         MathUtil.saveINT(this._flag,param1);
         MathUtil.saveINT(this._needNum,param2);
         this._rewards = param3;
      }
      
      public function get rewards() : Array
      {
         return this._rewards;
      }
      
      public function setGet() : void
      {
         FlagProxy.instance().openFlag.setValue(MathUtil.loadINT(this._flag),1);
      }
      
      public function get hasGet() : Boolean
      {
         return FlagProxy.instance().openFlag.isComplete(MathUtil.loadINT(this._flag));
      }
      
      public function get canGet() : Boolean
      {
         return HuoYueConfig.instance().total >= MathUtil.loadINT(this._needNum) && !this.hasGet;
      }
      
      public function get tip() : String
      {
         return "奖励内容如下：<br>" + RewardProxy.instance().parseName1(this._rewards,false);
      }
   }
}

