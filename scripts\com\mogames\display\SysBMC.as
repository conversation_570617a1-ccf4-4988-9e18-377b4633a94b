package com.mogames.display
{
   import mogames.Layers;
   
   public class SysBMC extends BMCSprite
   {
      
      private var _forceStop:Boolean;
      
      public function SysBMC()
      {
         super();
      }
      
      override public function play() : void
      {
         if(this._forceStop)
         {
            return;
         }
         Layers.render.add(updateFrame);
      }
      
      override public function stop() : void
      {
         Layers.render.remove(updateFrame);
      }
      
      public function setForceStop(param1:<PERSON><PERSON><PERSON>) : void
      {
         this._forceStop = param1;
         if(param1)
         {
            this.stop();
         }
         else
         {
            this.play();
         }
      }
      
      override public function clean() : void
      {
         this._forceStop = false;
         super.clean();
      }
   }
}

