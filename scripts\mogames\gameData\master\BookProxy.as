package mogames.gameData.master
{
   import mogames.gameData.skill.SkillFactory;
   import mogames.gameData.skill.base.BookSkillVO;
   
   public class BookProxy
   {
      
      private static var _instance:BookProxy;
      
      private var _books:Array;
      
      public function BookProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
      }
      
      public static function instance() : BookProxy
      {
         if(!_instance)
         {
            _instance = new BookProxy();
         }
         return _instance;
      }
      
      public function startNew() : void
      {
         this._books = [];
      }
      
      public function set loadData(param1:Array) : void
      {
         var _loc2_:Array = null;
         var _loc3_:String = null;
         var _loc4_:BookSkillVO = null;
         this.startNew();
         if(!param1)
         {
            return;
         }
         for each(_loc3_ in param1)
         {
            _loc2_ = _loc3_.split("H");
            _loc4_ = SkillFactory.newBookSkillVO(int(_loc2_[0]));
            _loc4_.setLevel(int(_loc2_[1]));
            this._books[this._books.length] = _loc4_;
         }
      }
      
      public function get saveData() : Array
      {
         var _loc2_:BookSkillVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._books)
         {
            _loc1_[_loc1_.length] = _loc2_.constSkill.skillID + "H" + _loc2_.level;
         }
         return _loc1_;
      }
      
      public function addBookSkill(param1:int) : void
      {
         if(this.findSkill(param1) != null)
         {
            return;
         }
         var _loc2_:BookSkillVO = SkillFactory.newBookSkillVO(param1);
         this._books[this._books.length] = _loc2_;
      }
      
      public function findSkill(param1:int) : BookSkillVO
      {
         var _loc2_:BookSkillVO = null;
         for each(_loc2_ in this._books)
         {
            if(_loc2_.constSkill.skillID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function get allSkills() : Array
      {
         return this._books;
      }
   }
}

