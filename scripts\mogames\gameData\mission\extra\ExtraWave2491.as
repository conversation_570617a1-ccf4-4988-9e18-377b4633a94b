package mogames.gameData.mission.extra
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ExtraWave2491
   {
      
      public function ExtraWave2491()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2491);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(849,177000000,620000,260000,80,80,300,90,new BossSkillData0(150,{"hurt":400000},5),1011,0);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(775,3350000,250000,130000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(775,3350000,250000,130000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(775,3350000,250000,130000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(775,3350000,250000,130000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(774,3350000,250000,130000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(16,new RoleArgVO(776,3350000,250000,130000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(778,25000000,220000,120000,50,80,150,80,new BossSkillData1(10,{
            "hurt":250000,
            "roleNum":20
         },3),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(774,3350000,250000,130000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(776,3350000,250000,130000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(775,3350000,250000,130000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(17,new RoleArgVO(776,3350000,250000,130000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(778,25000000,220000,120000,50,80,150,80,new BossSkillData1(10,{
            "hurt":250000,
            "hurtCount":5
         },3),1003,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(775,3350000,250000,130000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(776,3350000,250000,130000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(774,3350000,250000,130000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(774,3350000,250000,130000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(778,25000000,220000,120000,50,80,150,80,new BossSkillData1(10,{
            "hurt":250000,
            "roleNum":20
         },3),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(775,3350000,250000,130000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(775,3350000,250000,130000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(774,3350000,250000,130000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(775,3350000,250000,130000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(774,3350000,250000,130000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(776,3350000,250000,130000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(777,25000000,220000,120000,50,80,150,80,new BossSkillData0(250,{
            "hurt":250000,
            "hurtCount":5
         },3),1003,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

