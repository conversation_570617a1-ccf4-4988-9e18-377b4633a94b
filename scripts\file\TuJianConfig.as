package file
{
   import flash.utils.Dictionary;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.base.vo.EquipRewardVO;
   import mogames.gameData.main.vo.LabelVO;
   import mogames.gameData.role.hero.HeroGameVO;
   import mogames.gameData.role.hero.HeroOtherVO;
   
   public class TuJianConfig
   {
      
      private static var _instance:TuJianConfig;
      
      public var tuRoles:Array = [101,102,103,104,105,106,107,108,109,110,111,112,201,202,203,204,205,206,207,208,209,210,211,212,231,232,233,234,235,251,252,253,254,255,256,257,258,259,260,261,236,237,238,272,273,274,275,290,291,292,298,299,810,811,812,813,814];
      
      public var labels:Array = [new LabelVO("物品","GOOD"),new LabelVO("武将单位","HERO"),new LabelVO("武将皮肤","FASHION"),new LabelVO("士兵单位","ARMY"),new LabelVO("鬼怪单位","GHOST")];
      
      private var _dict:Dictionary;
      
      public function TuJianConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : TuJianConfig
      {
         if(!_instance)
         {
            _instance = new TuJianConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._dict = new Dictionary();
         this._dict["PROP"] = [new BaseRewardVO(10380,1),new BaseRewardVO(10982,1),new BaseRewardVO(10981,1),new BaseRewardVO(10983,1),new BaseRewardVO(10308,1),new BaseRewardVO(10921,1),new BaseRewardVO(10303,1),new BaseRewardVO(10304,1),new BaseRewardVO(10305,1),new BaseRewardVO(10306,1),new BaseRewardVO(50046,1),new BaseRewardVO(50045,1),new BaseRewardVO(50044,1),new BaseRewardVO(50043,1),new BaseRewardVO(50042,1),new BaseRewardVO(50041,1),new BaseRewardVO(50040,1),new BaseRewardVO(50039,1),new BaseRewardVO(50038,1),new BaseRewardVO(50037,1),new BaseRewardVO(50036,1),new BaseRewardVO(50035,1),new BaseRewardVO(50034,1),new BaseRewardVO(50033,1),new BaseRewardVO(50032,1),new BaseRewardVO(50031,1),new BaseRewardVO(50030,1),new BaseRewardVO(50029,1),new BaseRewardVO(50028,1),new BaseRewardVO(50027,1),new BaseRewardVO(50026,1),new BaseRewardVO(50025,1),new BaseRewardVO(50024,1),new BaseRewardVO(50023,1),new BaseRewardVO(50022,1),new BaseRewardVO(50021,1),new BaseRewardVO(50020,1),new BaseRewardVO(50019
         ,1),new BaseRewardVO(50018,1),new BaseRewardVO(50017,1),new BaseRewardVO(50016,1),new BaseRewardVO(50015,1),new BaseRewardVO(50014,1),new BaseRewardVO(50013,1),new BaseRewardVO(50012,1),new BaseRewardVO(50011,1),new BaseRewardVO(50010,1),new BaseRewardVO(50009,1),new BaseRewardVO(50007,1),new BaseRewardVO(50006,1),new BaseRewardVO(50005,1),new BaseRewardVO(50004,1),new BaseRewardVO(50003,1),new BaseRewardVO(50002,1),new BaseRewardVO(50001,1),new BaseRewardVO(10302,1),new BaseRewardVO(10020,1),new BaseRewardVO(10271,1),new BaseRewardVO(10272,1),new BaseRewardVO(10273,1),new BaseRewardVO(10274,1),new BaseRewardVO(10275,1),new BaseRewardVO(10276,1),new BaseRewardVO(10277,1),new BaseRewardVO(10901,1),new BaseRewardVO(10902,1),new BaseRewardVO(10903,1),new BaseRewardVO(10904,1),new BaseRewardVO(10905,1),new BaseRewardVO(10906,1),new BaseRewardVO(10907,1),new BaseRewardVO(10951,1),new BaseRewardVO(10952,1),new BaseRewardVO(10953,1),new BaseRewardVO(10954,1),new BaseRewardVO(10955,1),new BaseRewardVO(10956
         ,1),new BaseRewardVO(10957,1),new BaseRewardVO(10301,1),new BaseRewardVO(18501,1),new BaseRewardVO(18502,1),new BaseRewardVO(10801,1),new BaseRewardVO(10806,1),new BaseRewardVO(10811,1),new BaseRewardVO(10250,1),new BaseRewardVO(10103,1),new BaseRewardVO(10102,1),new BaseRewardVO(10101,1)];
         this._dict["BAG"] = [new BaseRewardVO(11401,1),new BaseRewardVO(11073,1),new BaseRewardVO(11071,1),new BaseRewardVO(11072,1),new BaseRewardVO(11003,1),new BaseRewardVO(11302,1),new BaseRewardVO(11205,1),new BaseRewardVO(11204,1),new BaseRewardVO(11501,1),new BaseRewardVO(11201,1),new BaseRewardVO(11158,1),new BaseRewardVO(11157,1),new BaseRewardVO(11154,1),new BaseRewardVO(11151,1),new BaseRewardVO(11101,1),new BaseRewardVO(11104,1),new BaseRewardVO(11011,1),new BaseRewardVO(11008,1),new BaseRewardVO(11005,1),new BaseRewardVO(11002,1),new BaseRewardVO(11001,1)];
         this._dict["CAILIAO"] = [new BaseRewardVO(10853,1),new BaseRewardVO(10553,1),new BaseRewardVO(10551,1),new BaseRewardVO(10550,1),new BaseRewardVO(10558,1),new BaseRewardVO(10562,1),new BaseRewardVO(10560,1),new BaseRewardVO(10559,1),new BaseRewardVO(10557,1),new BaseRewardVO(10556,1),new BaseRewardVO(10555,1),new BaseRewardVO(10554,1),new BaseRewardVO(10552,1),new BaseRewardVO(10549,1),new BaseRewardVO(10548,1),new BaseRewardVO(10547,1),new BaseRewardVO(10546,1),new BaseRewardVO(10544,1),new BaseRewardVO(10543,1),new BaseRewardVO(10542,1),new BaseRewardVO(10541,1),new BaseRewardVO(10532,1),new BaseRewardVO(10531,1),new BaseRewardVO(10530,1),new BaseRewardVO(10528,1),new BaseRewardVO(10527,1),new BaseRewardVO(10537,1),new BaseRewardVO(10538,1),new BaseRewardVO(10151,1),new BaseRewardVO(10152,1),new BaseRewardVO(10153,1),new BaseRewardVO(10154,1),new BaseRewardVO(10539,1),new BaseRewardVO(10533,1),new BaseRewardVO(10534,1),new BaseRewardVO(10535,1),new BaseRewardVO(10526,1),new BaseRewardVO(10525
         ,1),new BaseRewardVO(10524,1),new BaseRewardVO(10523,1),new BaseRewardVO(10522,1),new BaseRewardVO(10521,1),new BaseRewardVO(10510,1),new BaseRewardVO(10509,1),new BaseRewardVO(10508,1),new BaseRewardVO(10501,1),new BaseRewardVO(10503,1),new BaseRewardVO(10505,1),new BaseRewardVO(10852,1),new BaseRewardVO(10851,1),new BaseRewardVO(10644,1),new BaseRewardVO(10643,1),new BaseRewardVO(10642,1),new BaseRewardVO(10641,1),new BaseRewardVO(10640,1),new BaseRewardVO(10639,1),new BaseRewardVO(10638,1),new BaseRewardVO(10637,1),new BaseRewardVO(10636,1),new BaseRewardVO(10635,1),new BaseRewardVO(10631,1),new BaseRewardVO(10632,1),new BaseRewardVO(10633,1),new BaseRewardVO(10634,1),new BaseRewardVO(10630,1),new BaseRewardVO(10629,1),new BaseRewardVO(10628,1),new BaseRewardVO(10627,1),new BaseRewardVO(10626,1),new BaseRewardVO(10625,1),new BaseRewardVO(10624,1),new BaseRewardVO(10616,1),new BaseRewardVO(10617,1),new BaseRewardVO(10618,1),new BaseRewardVO(10619,1),new BaseRewardVO(10620,1),new BaseRewardVO(10621
         ,1),new BaseRewardVO(10622,1),new BaseRewardVO(10623,1),new BaseRewardVO(10411,1),new BaseRewardVO(10412,1),new BaseRewardVO(10601,1),new BaseRewardVO(10602,1),new BaseRewardVO(10603,1),new BaseRewardVO(10604,1),new BaseRewardVO(10415,1),new BaseRewardVO(10605,1),new BaseRewardVO(10606,1),new BaseRewardVO(10607,1),new BaseRewardVO(10608,1),new BaseRewardVO(10609,1),new BaseRewardVO(10610,1),new BaseRewardVO(10612,1),new BaseRewardVO(10613,1),new BaseRewardVO(10614,1),new BaseRewardVO(10615,1),new BaseRewardVO(10401,1),new BaseRewardVO(10403,1),new BaseRewardVO(10404,1),new BaseRewardVO(10405,1),new BaseRewardVO(10406,1),new BaseRewardVO(10407,1),new BaseRewardVO(10408,1),new BaseRewardVO(10409,1),new BaseRewardVO(10410,1),new BaseRewardVO(10201,1),new BaseRewardVO(10027,1),new BaseRewardVO(10028,1),new BaseRewardVO(10029,1),new BaseRewardVO(10031,1),new BaseRewardVO(10032,1),new BaseRewardVO(10033,1)];
         this._dict["TASK"] = [new BaseRewardVO(10021,1),new BaseRewardVO(10022,1),new BaseRewardVO(10023,1),new BaseRewardVO(10024,1),new BaseRewardVO(10025,1),new BaseRewardVO(10026,1)];
         this._dict["HECHENG"] = [new BaseRewardVO(10201,1),new BaseRewardVO(10202,1),new BaseRewardVO(10203,1),new BaseRewardVO(68001,1),new BaseRewardVO(68002,1),null,null,null,new BaseRewardVO(12351,1),new BaseRewardVO(12356,1),new BaseRewardVO(12361,1),new BaseRewardVO(12366,1),new BaseRewardVO(12371,1),null,null,null,new BaseRewardVO(12321,1),new BaseRewardVO(12326,1),new BaseRewardVO(12331,1),new BaseRewardVO(12336,1),new BaseRewardVO(12341,1),null,null,null,new BaseRewardVO(12291,1),new BaseRewardVO(12296,1),new BaseRewardVO(12301,1),new BaseRewardVO(12306,1),new BaseRewardVO(12311,1),null,null,null,new BaseRewardVO(12261,1),new BaseRewardVO(12266,1),new BaseRewardVO(12271,1),new BaseRewardVO(12276,1),new BaseRewardVO(12281,1),null,null,null,new BaseRewardVO(12231,1),new BaseRewardVO(12236,1),new BaseRewardVO(12241,1),new BaseRewardVO(12246,1),new BaseRewardVO(12251,1),null,null,null,new BaseRewardVO(12201,1),new BaseRewardVO(12206,1),new BaseRewardVO(12211,1),new BaseRewardVO(12216,1),new BaseRewardVO(12221
         ,1),null,null,null,new BaseRewardVO(12161,1),new BaseRewardVO(12166,1),new BaseRewardVO(12171,1),new BaseRewardVO(12176,1),new BaseRewardVO(12181,1),null,null,null,new BaseRewardVO(12131,1),new BaseRewardVO(12136,1),new BaseRewardVO(12141,1),new BaseRewardVO(12146,1),new BaseRewardVO(12151,1),null,null,null,new BaseRewardVO(12086,1),new BaseRewardVO(12091,1),new BaseRewardVO(12096,1),new BaseRewardVO(12101,1),new BaseRewardVO(12106,1),null,null,null,new BaseRewardVO(12061,1),new BaseRewardVO(12066,1),new BaseRewardVO(12071,1),new BaseRewardVO(12076,1),new BaseRewardVO(12081,1),null,null,null,new BaseRewardVO(12031,1),new BaseRewardVO(12036,1),new BaseRewardVO(12041,1),new BaseRewardVO(12046,1),new BaseRewardVO(12051,1),null,null,null,new BaseRewardVO(12001,1),new BaseRewardVO(12006,1),new BaseRewardVO(12011,1),new BaseRewardVO(12016,1),new BaseRewardVO(12021,1),null,null,null,new BaseRewardVO(12352,1),new BaseRewardVO(12357,1),new BaseRewardVO(12362,1),new BaseRewardVO(12367,1),new BaseRewardVO(12372
         ,1),null,null,null,new BaseRewardVO(12322,1),new BaseRewardVO(12327,1),new BaseRewardVO(12332,1),new BaseRewardVO(12337,1),new BaseRewardVO(12342,1),null,null,null,new BaseRewardVO(12292,1),new BaseRewardVO(12297,1),new BaseRewardVO(12302,1),new BaseRewardVO(12307,1),new BaseRewardVO(12312,1),null,null,null,new BaseRewardVO(12262,1),new BaseRewardVO(12267,1),new BaseRewardVO(12272,1),new BaseRewardVO(12277,1),new BaseRewardVO(12282,1),null,null,null,new BaseRewardVO(12232,1),new BaseRewardVO(12237,1),new BaseRewardVO(12242,1),new BaseRewardVO(12247,1),new BaseRewardVO(12252,1),null,null,null,new BaseRewardVO(12202,1),new BaseRewardVO(12207,1),new BaseRewardVO(12212,1),new BaseRewardVO(12217,1),new BaseRewardVO(12222,1),null,null,null,new BaseRewardVO(12162,1),new BaseRewardVO(12167,1),new BaseRewardVO(12172,1),new BaseRewardVO(12177,1),new BaseRewardVO(12182,1),null,null,null,new BaseRewardVO(12132,1),new BaseRewardVO(12137,1),new BaseRewardVO(12142,1),new BaseRewardVO(12147,1),new BaseRewardVO(12152
         ,1),null,null,null,new BaseRewardVO(12087,1),new BaseRewardVO(12092,1),new BaseRewardVO(12097,1),new BaseRewardVO(12102,1),new BaseRewardVO(12107,1),null,null,null,new BaseRewardVO(12062,1),new BaseRewardVO(12067,1),new BaseRewardVO(12072,1),new BaseRewardVO(12077,1),new BaseRewardVO(12082,1),null,null,null,new BaseRewardVO(12032,1),new BaseRewardVO(12037,1),new BaseRewardVO(12042,1),new BaseRewardVO(12047,1),new BaseRewardVO(12052,1),null,null,null,new BaseRewardVO(12002,1),new BaseRewardVO(12007,1),new BaseRewardVO(12012,1),new BaseRewardVO(12017,1),new BaseRewardVO(12022,1),null,null,null,new BaseRewardVO(12353,1),new BaseRewardVO(12358,1),new BaseRewardVO(12363,1),new BaseRewardVO(12368,1),new BaseRewardVO(12373,1),null,null,null,new BaseRewardVO(12323,1),new BaseRewardVO(12328,1),new BaseRewardVO(12333,1),new BaseRewardVO(12338,1),new BaseRewardVO(12343,1),null,null,null,new BaseRewardVO(12293,1),new BaseRewardVO(12298,1),new BaseRewardVO(12303,1),new BaseRewardVO(12308,1),new BaseRewardVO(12313
         ,1),null,null,null,new BaseRewardVO(12263,1),new BaseRewardVO(12268,1),new BaseRewardVO(12273,1),new BaseRewardVO(12278,1),new BaseRewardVO(12283,1),null,null,null,new BaseRewardVO(12233,1),new BaseRewardVO(12238,1),new BaseRewardVO(12243,1),new BaseRewardVO(12248,1),new BaseRewardVO(12253,1),null,null,null,new BaseRewardVO(12203,1),new BaseRewardVO(12208,1),new BaseRewardVO(12213,1),new BaseRewardVO(12218,1),new BaseRewardVO(12223,1),null,null,null,new BaseRewardVO(12163,1),new BaseRewardVO(12168,1),new BaseRewardVO(12173,1),new BaseRewardVO(12178,1),new BaseRewardVO(12183,1),null,null,null,new BaseRewardVO(12133,1),new BaseRewardVO(12138,1),new BaseRewardVO(12143,1),new BaseRewardVO(12148,1),new BaseRewardVO(12153,1),null,null,null,new BaseRewardVO(12088,1),new BaseRewardVO(12093,1),new BaseRewardVO(12098,1),new BaseRewardVO(12103,1),new BaseRewardVO(12108,1),null,null,null,new BaseRewardVO(12063,1),new BaseRewardVO(12068,1),new BaseRewardVO(12073,1),new BaseRewardVO(12078,1),new BaseRewardVO(12083
         ,1),null,null,null,new BaseRewardVO(12033,1),new BaseRewardVO(12038,1),new BaseRewardVO(12043,1),new BaseRewardVO(12048,1),new BaseRewardVO(12053,1),null,null,null,new BaseRewardVO(12003,1),new BaseRewardVO(12008,1),new BaseRewardVO(12013,1),new BaseRewardVO(12018,1),new BaseRewardVO(12023,1),null,null,null,new BaseRewardVO(12354,1),new BaseRewardVO(12359,1),new BaseRewardVO(12364,1),new BaseRewardVO(12369,1),new BaseRewardVO(12374,1),null,null,null,new BaseRewardVO(12324,1),new BaseRewardVO(12329,1),new BaseRewardVO(12334,1),new BaseRewardVO(12339,1),new BaseRewardVO(12344,1),null,null,null,new BaseRewardVO(12294,1),new BaseRewardVO(12299,1),new BaseRewardVO(12304,1),new BaseRewardVO(12309,1),new BaseRewardVO(12314,1),null,null,null,new BaseRewardVO(12264,1),new BaseRewardVO(12269,1),new BaseRewardVO(12274,1),new BaseRewardVO(12279,1),new BaseRewardVO(12284,1),null,null,null,new BaseRewardVO(12234,1),new BaseRewardVO(12239,1),new BaseRewardVO(12244,1),new BaseRewardVO(12249,1),new BaseRewardVO(12254
         ,1),null,null,null,new BaseRewardVO(12204,1),new BaseRewardVO(12209,1),new BaseRewardVO(12214,1),new BaseRewardVO(12219,1),new BaseRewardVO(12224,1),null,null,null,new BaseRewardVO(12164,1),new BaseRewardVO(12169,1),new BaseRewardVO(12174,1),new BaseRewardVO(12179,1),new BaseRewardVO(12184,1),null,null,null,new BaseRewardVO(12134,1),new BaseRewardVO(12139,1),new BaseRewardVO(12144,1),new BaseRewardVO(12149,1),new BaseRewardVO(12154,1),null,null,null,new BaseRewardVO(12089,1),new BaseRewardVO(12094,1),new BaseRewardVO(12099,1),new BaseRewardVO(12104,1),new BaseRewardVO(12109,1),null,null,null,new BaseRewardVO(12064,1),new BaseRewardVO(12069,1),new BaseRewardVO(12074,1),new BaseRewardVO(12079,1),new BaseRewardVO(12084,1),null,null,null,new BaseRewardVO(12034,1),new BaseRewardVO(12039,1),new BaseRewardVO(12044,1),new BaseRewardVO(12049,1),new BaseRewardVO(12054,1),null,null,null,new BaseRewardVO(12004,1),new BaseRewardVO(12009,1),new BaseRewardVO(12014,1),new BaseRewardVO(12019,1),new BaseRewardVO(12024
         ,1),null,null,null,new BaseRewardVO(12355,1),new BaseRewardVO(12360,1),new BaseRewardVO(12365,1),new BaseRewardVO(12370,1),new BaseRewardVO(12375,1),null,null,null,new BaseRewardVO(12325,1),new BaseRewardVO(12330,1),new BaseRewardVO(12335,1),new BaseRewardVO(12340,1),new BaseRewardVO(12345,1),null,null,null,new BaseRewardVO(12295,1),new BaseRewardVO(12300,1),new BaseRewardVO(12305,1),new BaseRewardVO(12310,1),new BaseRewardVO(12315,1),null,null,null,new BaseRewardVO(12265,1),new BaseRewardVO(12270,1),new BaseRewardVO(12275,1),new BaseRewardVO(12280,1),new BaseRewardVO(12285,1),null,null,null,new BaseRewardVO(12235,1),new BaseRewardVO(12240,1),new BaseRewardVO(12245,1),new BaseRewardVO(12250,1),new BaseRewardVO(12255,1),null,null,null,new BaseRewardVO(12205,1),new BaseRewardVO(12210,1),new BaseRewardVO(12215,1),new BaseRewardVO(12220,1),new BaseRewardVO(12225,1),null,null,null,new BaseRewardVO(12165,1),new BaseRewardVO(12170,1),new BaseRewardVO(12175,1),new BaseRewardVO(12180,1),new BaseRewardVO(12185
         ,1),null,null,null,new BaseRewardVO(12135,1),new BaseRewardVO(12140,1),new BaseRewardVO(12145,1),new BaseRewardVO(12150,1),new BaseRewardVO(12155,1),null,null,null,new BaseRewardVO(12090,1),new BaseRewardVO(12095,1),new BaseRewardVO(12100,1),new BaseRewardVO(12105,1),new BaseRewardVO(12110,1),null,null,null,new BaseRewardVO(12065,1),new BaseRewardVO(12070,1),new BaseRewardVO(12075,1),new BaseRewardVO(12080,1),new BaseRewardVO(12085,1),null,null,null,new BaseRewardVO(12035,1),new BaseRewardVO(12040,1),new BaseRewardVO(12045,1),new BaseRewardVO(12050,1),new BaseRewardVO(12055,1),null,null,null,new BaseRewardVO(12005,1),new BaseRewardVO(12010,1),new BaseRewardVO(12015,1),new BaseRewardVO(12020,1),new BaseRewardVO(12025,1),null,null,null];
         this._dict["FASHION"] = [new EquipRewardVO(40105,3),new EquipRewardVO(42145,3),new EquipRewardVO(42215,3),new EquipRewardVO(42515,3),new EquipRewardVO(42564,3),new EquipRewardVO(42385,3),new EquipRewardVO(42555,3),new EquipRewardVO(40905,3),new EquipRewardVO(40505,3),new EquipRewardVO(41705,3),new EquipRewardVO(40705,3),new EquipRewardVO(41105,3),new EquipRewardVO(42365,3),new EquipRewardVO(42405,3),new EquipRewardVO(41505,3),new EquipRewardVO(42255,3),new EquipRewardVO(42475,3),new EquipRewardVO(42275,3),new EquipRewardVO(40104,3),new EquipRewardVO(40504,3),new EquipRewardVO(40904,3),new EquipRewardVO(42144,3),new EquipRewardVO(42214,3),new EquipRewardVO(42004,3),new EquipRewardVO(42404,3),new EquipRewardVO(40604,3),new EquipRewardVO(42384,3),new EquipRewardVO(41104,3),new EquipRewardVO(42364,3),new EquipRewardVO(40204,3),new EquipRewardVO(40704,3),new EquipRewardVO(42514,3),new EquipRewardVO(42554,3),new EquipRewardVO(42544,3),new EquipRewardVO(42474,3),new EquipRewardVO(41704,3),new EquipRewardVO(49990
         ,3),new EquipRewardVO(49991,3),new EquipRewardVO(49992,3),new EquipRewardVO(49993,3),new EquipRewardVO(49994,3),new EquipRewardVO(49995,3),new EquipRewardVO(49996,3),new EquipRewardVO(49997,3),new EquipRewardVO(49998,3),new EquipRewardVO(49999,3),new EquipRewardVO(42543,3),new EquipRewardVO(42523,3),new EquipRewardVO(42503,3),new EquipRewardVO(42483,3),new EquipRewardVO(42463,3),new EquipRewardVO(42453,3),new EquipRewardVO(42443,3),new EquipRewardVO(42433,3),new EquipRewardVO(42423,3),new EquipRewardVO(42413,3),new EquipRewardVO(42403,3),new EquipRewardVO(42393,3),new EquipRewardVO(42233,3),new EquipRewardVO(42133,3),new EquipRewardVO(40203,3),new EquipRewardVO(40303,3),new EquipRewardVO(40403,3),new EquipRewardVO(40803,3),new EquipRewardVO(41003,3),new EquipRewardVO(41103,3),new EquipRewardVO(41303,3),new EquipRewardVO(41403,3),new EquipRewardVO(41503,3),new EquipRewardVO(41603,3),new EquipRewardVO(41903,3),new EquipRewardVO(42003,3),new EquipRewardVO(42103,3),new EquipRewardVO(42113,3)
         ,new EquipRewardVO(42123,3),new EquipRewardVO(42153,3),new EquipRewardVO(41703,3),new EquipRewardVO(42163,3),new EquipRewardVO(42173,3),new EquipRewardVO(42183,3),new EquipRewardVO(42193,3),new EquipRewardVO(42203,3),new EquipRewardVO(42223,3),new EquipRewardVO(42313,3),new EquipRewardVO(42243,3),new EquipRewardVO(42253,3),new EquipRewardVO(42263,3),new EquipRewardVO(42273,3),new EquipRewardVO(42283,3),new EquipRewardVO(42293,3),new EquipRewardVO(42303,3),new EquipRewardVO(42323,3),new EquipRewardVO(42333,3),new EquipRewardVO(42343,3),new EquipRewardVO(42353,3),new EquipRewardVO(42373,3)];
         this._dict["HERO"] = this.collectHeros();
      }
      
      public function findList(param1:String) : Array
      {
         return this._dict[param1];
      }
      
      private function collectHeros() : Array
      {
         var _loc3_:HeroOtherVO = null;
         var _loc1_:Vector.<HeroOtherVO> = HeroConfig.instance().otherList;
         var _loc2_:Array = [];
         for each(_loc3_ in _loc1_)
         {
            if(_loc3_.heroID != 330)
            {
               _loc2_[_loc2_.length] = new HeroGameVO(_loc3_.heroID);
            }
         }
         _loc2_.sortOn(["country","quality"],Array.DESCENDING);
         return _loc2_;
      }
   }
}

