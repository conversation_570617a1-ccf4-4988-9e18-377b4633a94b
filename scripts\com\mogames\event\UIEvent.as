package com.mogames.event
{
   import flash.events.Event;
   
   public class UIEvent extends Event
   {
      
      public static const PANEL_EVENT:String = "PANEL_EVENT";
      
      public var data:Object;
      
      public var nameType:String;
      
      public function UIEvent(param1:String, param2:String, param3:Object = null, param4:Boolean = false, param5:<PERSON>olean = false)
      {
         this.nameType = param2;
         super(param1,param4,param5);
         this.data = param3;
      }
   }
}

