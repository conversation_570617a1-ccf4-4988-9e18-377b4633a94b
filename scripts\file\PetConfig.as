package file
{
   import com.mogames.data.RandomVO;
   import mogames.gameData.pet.PetRandVO;
   import mogames.gameData.role.pet.PetGameVO;
   
   public class PetConfig
   {
      
      private static var _instance:PetConfig;
      
      private var _list:Array;
      
      public function PetConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : PetConfig
      {
         if(!_instance)
         {
            _instance = new PetConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = [];
         this._list[this._list.length] = new PetRandVO(1000,10308,new RandomVO(30,170),new RandomVO(10,70),new RandomVO(15,99),new RandomVO(20,80),new RandomVO(10,40),new RandomVO(110,300),new RandomVO(120,160));
         this._list[this._list.length] = new PetRandVO(1001,10310,new RandomVO(50,195),new RandomVO(15,80),new RandomVO(10,89),new RandomVO(10,50),new RandomVO(5,25),new RandomVO(150,400),new RandomVO(110,150));
         this._list[this._list.length] = new PetRandVO(1002,10313,new RandomVO(50,210),new RandomVO(30,140),new RandomVO(5,79),new RandomVO(5,43),new RandomVO(5,35),new RandomVO(100,350),new RandomVO(100,140));
         this._list[this._list.length] = new PetRandVO(1003,10314,new RandomVO(70,240),new RandomVO(20,120),new RandomVO(20,119),new RandomVO(15,65),new RandomVO(10,50),new RandomVO(110,325),new RandomVO(100,160));
         this._list[this._list.length] = new PetRandVO(1004,10315,new RandomVO(100,330),new RandomVO(40,200),new RandomVO(50,159),new RandomVO(30,65),new RandomVO(15,55),new RandomVO(110,420),new RandomVO(120,180));
         this._list[this._list.length] = new PetRandVO(1005,10317,new RandomVO(100,350),new RandomVO(40,220),new RandomVO(50,169),new RandomVO(35,75),new RandomVO(15,65),new RandomVO(110,500),new RandomVO(120,200));
      }
      
      public function findPetRandVO(param1:int) : PetRandVO
      {
         var _loc2_:PetRandVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.id == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function newPet(param1:int, param2:Boolean) : PetGameVO
      {
         var _loc3_:PetRandVO = this.findPetRandVO(param1);
         if(!_loc3_)
         {
            return null;
         }
         var _loc4_:PetGameVO = new PetGameVO(param1);
         var _loc5_:int = _loc3_.randATK.newMaxValue(param2);
         var _loc6_:int = _loc3_.randDEF.newMaxValue(param2);
         var _loc7_:int = _loc3_.randWIT.newMaxValue(param2);
         var _loc8_:int = _loc3_.randMISS.newMaxValue(param2);
         var _loc9_:int = _loc3_.randCRIT.newMaxValue(param2);
         var _loc10_:int = _loc3_.randBEI.newMaxValue(param2);
         var _loc11_:int = _loc3_.randSPD.newMaxValue(param2);
         _loc4_.newPetData(_loc5_,_loc6_,_loc7_,_loc8_,_loc9_,_loc10_,_loc11_,1);
         return _loc4_;
      }
   }
}

