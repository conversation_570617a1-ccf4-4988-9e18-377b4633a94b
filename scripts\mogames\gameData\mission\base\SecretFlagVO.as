package mogames.gameData.mission.base
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   
   public class SecretFlagVO
   {
      
      private var _constVO:SecretConstVO;
      
      private var _finish:Oint = new Oint();
      
      public function SecretFlagVO(param1:SecretConstVO)
      {
         super();
         this._constVO = param1;
         MathUtil.saveINT(this._finish,0);
      }
      
      public function setFinishValue(param1:int) : void
      {
         MathUtil.saveINT(this._finish,param1);
      }
      
      public function setFinish() : void
      {
         MathUtil.saveINT(this._finish,1);
      }
      
      public function get isFinish() : Bo<PERSON>an
      {
         return MathUtil.loadINT(this._finish) == 1;
      }
      
      public function get mid() : int
      {
         return this._constVO.mid;
      }
      
      public function get constVO() : SecretConstVO
      {
         return this._constVO;
      }
      
      public function get saveData() : String
      {
         return [this.mid,MathUtil.loadINT(this._finish)].join("H");
      }
      
      public function set loadData(param1:Array) : void
      {
         MathUtil.saveINT(this._finish,param1[1]);
      }
   }
}

