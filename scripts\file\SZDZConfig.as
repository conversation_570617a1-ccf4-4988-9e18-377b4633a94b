package file
{
   import mogames.gameData.base.func.SZDZVO;
   import mogames.gameData.base.vo.NeedVO;
   
   public class SZDZConfig
   {
      
      private static var _instance:SZDZConfig;
      
      private var _list:Array;
      
      public var stoneNeed:NeedVO;
      
      public function SZDZConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : SZDZConfig
      {
         if(!_instance)
         {
            _instance = new SZDZConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this.stoneNeed = new NeedVO(10983,1);
         this._list = [];
         this._list[this._list.length] = new SZDZVO(89521,999999,7000,50,[new NeedVO(10852,64),new NeedVO(10853,64),new NeedVO(10854,64)]);
         this._list[this._list.length] = new SZDZVO(89522,999999,7000,50,[new NeedVO(10852,64),new NeedVO(10853,64),new NeedVO(10854,64)]);
         this._list[this._list.length] = new SZDZVO(89523,999999,7000,50,[new NeedVO(10852,64),new NeedVO(10853,64),new NeedVO(10854,64)]);
         this._list[this._list.length] = new SZDZVO(89421,999999,7000,50,[new NeedVO(10852,64),new NeedVO(10853,64),new NeedVO(10854,64)]);
         this._list[this._list.length] = new SZDZVO(89422,999999,7000,50,[new NeedVO(10852,64),new NeedVO(10853,64),new NeedVO(10854,64)]);
         this._list[this._list.length] = new SZDZVO(89423,999999,7000,50,[new NeedVO(10852,64),new NeedVO(10853,64),new NeedVO(10854,64)]);
         this._list[this._list.length] = new SZDZVO(89321,999999,7000,50,[new NeedVO(10852,32),new NeedVO(10853,32),new NeedVO(10854,32)]);
         this._list[this._list.length] = new SZDZVO(89322,999999,7000,50,[new NeedVO(10852,32),new NeedVO(10853,32),new NeedVO(10854,32)]);
         this._list[this._list.length] = new SZDZVO(89323,999999,7000,50,[new NeedVO(10852,32),new NeedVO(10853,32),new NeedVO(10854,32)]);
         this._list[this._list.length] = new SZDZVO(89221,999999,7000,50,[new NeedVO(10852,32),new NeedVO(10853,32),new NeedVO(10854,32)]);
         this._list[this._list.length] = new SZDZVO(89222,999999,7000,50,[new NeedVO(10852,32),new NeedVO(10853,32),new NeedVO(10854,32)]);
         this._list[this._list.length] = new SZDZVO(89223,999999,7000,50,[new NeedVO(10852,32),new NeedVO(10853,32),new NeedVO(10854,32)]);
         this._list[this._list.length] = new SZDZVO(89121,999999,7000,50,[new NeedVO(10852,32),new NeedVO(10853,32),new NeedVO(10854,32)]);
         this._list[this._list.length] = new SZDZVO(89122,999999,7000,50,[new NeedVO(10852,32),new NeedVO(10853,32),new NeedVO(10854,32)]);
         this._list[this._list.length] = new SZDZVO(89123,999999,7000,50,[new NeedVO(10852,32),new NeedVO(10853,32),new NeedVO(10854,32)]);
         this._list[this._list.length] = new SZDZVO(89021,999999,7000,50,[new NeedVO(10852,32),new NeedVO(10853,32),new NeedVO(10854,32)]);
         this._list[this._list.length] = new SZDZVO(89022,999999,7000,50,[new NeedVO(10852,32),new NeedVO(10853,32),new NeedVO(10854,32)]);
         this._list[this._list.length] = new SZDZVO(89023,999999,7000,50,[new NeedVO(10852,32),new NeedVO(10853,32),new NeedVO(10854,32)]);
         this._list[this._list.length] = new SZDZVO(89511,999999,6000,100,[new NeedVO(10852,32),new NeedVO(10853,32),new NeedVO(10854,32)]);
         this._list[this._list.length] = new SZDZVO(89512,999999,6000,100,[new NeedVO(10852,32),new NeedVO(10853,32),new NeedVO(10854,32)]);
         this._list[this._list.length] = new SZDZVO(89513,999999,6000,100,[new NeedVO(10852,32),new NeedVO(10853,32),new NeedVO(10854,32)]);
         this._list[this._list.length] = new SZDZVO(89411,999999,6000,100,[new NeedVO(10852,32),new NeedVO(10853,32),new NeedVO(10854,32)]);
         this._list[this._list.length] = new SZDZVO(89412,999999,6000,100,[new NeedVO(10852,32),new NeedVO(10853,32),new NeedVO(10854,32)]);
         this._list[this._list.length] = new SZDZVO(89413,999999,6000,100,[new NeedVO(10852,32),new NeedVO(10853,32),new NeedVO(10854,32)]);
         this._list[this._list.length] = new SZDZVO(89311,999999,6000,100,[new NeedVO(10852,16),new NeedVO(10853,16),new NeedVO(10854,16)]);
         this._list[this._list.length] = new SZDZVO(89312,999999,6000,100,[new NeedVO(10852,16),new NeedVO(10853,16),new NeedVO(10854,16)]);
         this._list[this._list.length] = new SZDZVO(89313,999999,6000,100,[new NeedVO(10852,16),new NeedVO(10853,16),new NeedVO(10854,16)]);
         this._list[this._list.length] = new SZDZVO(89211,999999,6000,100,[new NeedVO(10852,16),new NeedVO(10853,16),new NeedVO(10854,16)]);
         this._list[this._list.length] = new SZDZVO(89212,999999,6000,100,[new NeedVO(10852,16),new NeedVO(10853,16),new NeedVO(10854,16)]);
         this._list[this._list.length] = new SZDZVO(89213,999999,6000,100,[new NeedVO(10852,16),new NeedVO(10853,16),new NeedVO(10854,16)]);
         this._list[this._list.length] = new SZDZVO(89111,999999,6000,100,[new NeedVO(10852,16),new NeedVO(10853,16),new NeedVO(10854,16)]);
         this._list[this._list.length] = new SZDZVO(89112,999999,6000,100,[new NeedVO(10852,16),new NeedVO(10853,16),new NeedVO(10854,16)]);
         this._list[this._list.length] = new SZDZVO(89113,999999,6000,100,[new NeedVO(10852,16),new NeedVO(10853,16),new NeedVO(10854,16)]);
         this._list[this._list.length] = new SZDZVO(89011,999999,6000,100,[new NeedVO(10852,16),new NeedVO(10853,16),new NeedVO(10854,16)]);
         this._list[this._list.length] = new SZDZVO(89012,999999,6000,100,[new NeedVO(10852,16),new NeedVO(10853,16),new NeedVO(10854,16)]);
         this._list[this._list.length] = new SZDZVO(89013,999999,6000,100,[new NeedVO(10852,16),new NeedVO(10853,16),new NeedVO(10854,16)]);
         this._list[this._list.length] = new SZDZVO(89501,999999,5000,200,[new NeedVO(10852,16),new NeedVO(10853,16),new NeedVO(10854,16)]);
         this._list[this._list.length] = new SZDZVO(89502,999999,5000,200,[new NeedVO(10852,16),new NeedVO(10853,16),new NeedVO(10854,16)]);
         this._list[this._list.length] = new SZDZVO(89503,999999,5000,200,[new NeedVO(10852,16),new NeedVO(10853,16),new NeedVO(10854,16)]);
         this._list[this._list.length] = new SZDZVO(89401,999999,5000,200,[new NeedVO(10852,16),new NeedVO(10853,16),new NeedVO(10854,16)]);
         this._list[this._list.length] = new SZDZVO(89402,999999,5000,200,[new NeedVO(10852,16),new NeedVO(10853,16),new NeedVO(10854,16)]);
         this._list[this._list.length] = new SZDZVO(89403,999999,5000,200,[new NeedVO(10852,16),new NeedVO(10853,16),new NeedVO(10854,16)]);
         this._list[this._list.length] = new SZDZVO(89301,999999,5000,200,[new NeedVO(10852,8),new NeedVO(10853,8),new NeedVO(10854,8)]);
         this._list[this._list.length] = new SZDZVO(89302,999999,5000,200,[new NeedVO(10852,8),new NeedVO(10853,8),new NeedVO(10854,8)]);
         this._list[this._list.length] = new SZDZVO(89303,999999,5000,200,[new NeedVO(10852,8),new NeedVO(10853,8),new NeedVO(10854,8)]);
         this._list[this._list.length] = new SZDZVO(89201,999999,5000,200,[new NeedVO(10852,8),new NeedVO(10853,8),new NeedVO(10854,8)]);
         this._list[this._list.length] = new SZDZVO(89202,999999,5000,200,[new NeedVO(10852,8),new NeedVO(10853,8),new NeedVO(10854,8)]);
         this._list[this._list.length] = new SZDZVO(89203,999999,5000,200,[new NeedVO(10852,8),new NeedVO(10853,8),new NeedVO(10854,8)]);
         this._list[this._list.length] = new SZDZVO(89101,999999,5000,200,[new NeedVO(10852,8),new NeedVO(10853,8),new NeedVO(10854,8)]);
         this._list[this._list.length] = new SZDZVO(89102,999999,5000,200,[new NeedVO(10852,8),new NeedVO(10853,8),new NeedVO(10854,8)]);
         this._list[this._list.length] = new SZDZVO(89103,999999,5000,200,[new NeedVO(10852,8),new NeedVO(10853,8),new NeedVO(10854,8)]);
         this._list[this._list.length] = new SZDZVO(89001,999999,5000,200,[new NeedVO(10852,8),new NeedVO(10853,8),new NeedVO(10854,8)]);
         this._list[this._list.length] = new SZDZVO(89002,999999,5000,200,[new NeedVO(10852,8),new NeedVO(10853,8),new NeedVO(10854,8)]);
         this._list[this._list.length] = new SZDZVO(89003,999999,5000,200,[new NeedVO(10852,8),new NeedVO(10853,8),new NeedVO(10854,8)]);
         this._list[this._list.length] = new SZDZVO(85401,999999,5000,200,[new NeedVO(10151,75),new NeedVO(10152,75),new NeedVO(10153,75)]);
         this._list[this._list.length] = new SZDZVO(85402,999999,5000,200,[new NeedVO(10154,75),new NeedVO(10155,75),new NeedVO(10156,75)]);
         this._list[this._list.length] = new SZDZVO(85403,999999,5000,200,[new NeedVO(10157,75),new NeedVO(10158,75),new NeedVO(10151,75)]);
         this._list[this._list.length] = new SZDZVO(85501,999999,5000,200,[new NeedVO(10158,75),new NeedVO(10161,75),new NeedVO(10162,75)]);
         this._list[this._list.length] = new SZDZVO(85502,999999,5000,200,[new NeedVO(10163,75),new NeedVO(10164,75),new NeedVO(10165,75)]);
         this._list[this._list.length] = new SZDZVO(85503,999999,5000,200,[new NeedVO(10166,75),new NeedVO(10167,75),new NeedVO(10168,75)]);
         this._list[this._list.length] = new SZDZVO(85001,999999,5000,200,[new NeedVO(10151,75),new NeedVO(10152,75),new NeedVO(10153,75)]);
         this._list[this._list.length] = new SZDZVO(85002,999999,5000,200,[new NeedVO(10151,75),new NeedVO(10153,75),new NeedVO(10154,75)]);
         this._list[this._list.length] = new SZDZVO(85003,999999,5000,200,[new NeedVO(10151,75),new NeedVO(10154,75),new NeedVO(10152,75)]);
         this._list[this._list.length] = new SZDZVO(85101,999999,5000,200,[new NeedVO(10155,75),new NeedVO(10156,75),new NeedVO(10157,75)]);
         this._list[this._list.length] = new SZDZVO(85102,999999,5000,200,[new NeedVO(10155,75),new NeedVO(10157,75),new NeedVO(10158,75)]);
         this._list[this._list.length] = new SZDZVO(85103,999999,5000,200,[new NeedVO(10155,75),new NeedVO(10158,75),new NeedVO(10156,75)]);
         this._list[this._list.length] = new SZDZVO(85201,999999,5000,200,[new NeedVO(10161,75),new NeedVO(10162,75),new NeedVO(10163,75)]);
         this._list[this._list.length] = new SZDZVO(85202,999999,5000,200,[new NeedVO(10161,75),new NeedVO(10163,75),new NeedVO(10164,75)]);
         this._list[this._list.length] = new SZDZVO(85203,999999,5000,200,[new NeedVO(10161,75),new NeedVO(10164,75),new NeedVO(10162,75)]);
         this._list[this._list.length] = new SZDZVO(85301,999999,5000,200,[new NeedVO(10165,75),new NeedVO(10166,75),new NeedVO(10167,75)]);
         this._list[this._list.length] = new SZDZVO(85302,999999,5000,200,[new NeedVO(10165,75),new NeedVO(10167,75),new NeedVO(10168,75)]);
         this._list[this._list.length] = new SZDZVO(85303,999999,5000,200,[new NeedVO(10165,75),new NeedVO(10168,75),new NeedVO(10166,75)]);
         this._list[this._list.length] = new SZDZVO(84401,999999,4000,200,[new NeedVO(10151,60),new NeedVO(10152,60),new NeedVO(10153,60)]);
         this._list[this._list.length] = new SZDZVO(84402,999999,4000,200,[new NeedVO(10154,60),new NeedVO(10155,60),new NeedVO(10156,60)]);
         this._list[this._list.length] = new SZDZVO(84403,999999,4000,200,[new NeedVO(10157,60),new NeedVO(10158,60),new NeedVO(10151,60)]);
         this._list[this._list.length] = new SZDZVO(84501,999999,4000,200,[new NeedVO(10158,60),new NeedVO(10161,60),new NeedVO(10162,60)]);
         this._list[this._list.length] = new SZDZVO(84502,999999,4000,200,[new NeedVO(10163,60),new NeedVO(10164,60),new NeedVO(10165,60)]);
         this._list[this._list.length] = new SZDZVO(84503,999999,4000,200,[new NeedVO(10166,60),new NeedVO(10167,60),new NeedVO(10168,60)]);
         this._list[this._list.length] = new SZDZVO(84001,999999,4000,200,[new NeedVO(10151,60),new NeedVO(10152,60),new NeedVO(10153,60)]);
         this._list[this._list.length] = new SZDZVO(84002,999999,4000,200,[new NeedVO(10151,60),new NeedVO(10153,60),new NeedVO(10154,60)]);
         this._list[this._list.length] = new SZDZVO(84003,999999,4000,200,[new NeedVO(10151,60),new NeedVO(10154,60),new NeedVO(10152,60)]);
         this._list[this._list.length] = new SZDZVO(84101,999999,4000,200,[new NeedVO(10155,60),new NeedVO(10156,60),new NeedVO(10157,60)]);
         this._list[this._list.length] = new SZDZVO(84102,999999,4000,200,[new NeedVO(10155,60),new NeedVO(10157,60),new NeedVO(10158,60)]);
         this._list[this._list.length] = new SZDZVO(84103,999999,4000,200,[new NeedVO(10155,60),new NeedVO(10158,60),new NeedVO(10156,60)]);
         this._list[this._list.length] = new SZDZVO(84201,999999,4000,200,[new NeedVO(10161,60),new NeedVO(10162,60),new NeedVO(10163,60)]);
         this._list[this._list.length] = new SZDZVO(84202,999999,4000,200,[new NeedVO(10161,60),new NeedVO(10163,60),new NeedVO(10164,60)]);
         this._list[this._list.length] = new SZDZVO(84203,999999,4000,200,[new NeedVO(10161,60),new NeedVO(10164,60),new NeedVO(10162,60)]);
         this._list[this._list.length] = new SZDZVO(84301,999999,4000,200,[new NeedVO(10165,60),new NeedVO(10166,60),new NeedVO(10167,60)]);
         this._list[this._list.length] = new SZDZVO(84302,999999,4000,200,[new NeedVO(10165,60),new NeedVO(10167,60),new NeedVO(10168,60)]);
         this._list[this._list.length] = new SZDZVO(84303,999999,4000,200,[new NeedVO(10165,60),new NeedVO(10168,60),new NeedVO(10166,60)]);
         this._list[this._list.length] = new SZDZVO(83401,900000,3000,400,[new NeedVO(10151,45),new NeedVO(10152,45),new NeedVO(10153,45)]);
         this._list[this._list.length] = new SZDZVO(83402,900000,3000,400,[new NeedVO(10154,45),new NeedVO(10155,45),new NeedVO(10156,45)]);
         this._list[this._list.length] = new SZDZVO(83403,900000,3000,400,[new NeedVO(10157,45),new NeedVO(10158,45),new NeedVO(10151,45)]);
         this._list[this._list.length] = new SZDZVO(83501,900000,3000,400,[new NeedVO(10158,45),new NeedVO(10161,45),new NeedVO(10162,45)]);
         this._list[this._list.length] = new SZDZVO(83502,900000,3000,400,[new NeedVO(10163,45),new NeedVO(10164,45),new NeedVO(10165,45)]);
         this._list[this._list.length] = new SZDZVO(83503,900000,3000,400,[new NeedVO(10166,45),new NeedVO(10167,45),new NeedVO(10168,45)]);
         this._list[this._list.length] = new SZDZVO(83001,900000,3000,400,[new NeedVO(10151,45),new NeedVO(10152,45),new NeedVO(10153,45)]);
         this._list[this._list.length] = new SZDZVO(83002,900000,3000,400,[new NeedVO(10151,45),new NeedVO(10153,45),new NeedVO(10154,45)]);
         this._list[this._list.length] = new SZDZVO(83003,900000,3000,400,[new NeedVO(10151,45),new NeedVO(10154,45),new NeedVO(10152,45)]);
         this._list[this._list.length] = new SZDZVO(83101,900000,3000,400,[new NeedVO(10155,45),new NeedVO(10156,45),new NeedVO(10157,45)]);
         this._list[this._list.length] = new SZDZVO(83102,900000,3000,400,[new NeedVO(10155,45),new NeedVO(10157,45),new NeedVO(10158,45)]);
         this._list[this._list.length] = new SZDZVO(83103,900000,3000,400,[new NeedVO(10155,45),new NeedVO(10158,45),new NeedVO(10156,45)]);
         this._list[this._list.length] = new SZDZVO(83201,900000,3000,400,[new NeedVO(10161,45),new NeedVO(10162,45),new NeedVO(10163,45)]);
         this._list[this._list.length] = new SZDZVO(83202,900000,3000,400,[new NeedVO(10161,45),new NeedVO(10163,45),new NeedVO(10164,45)]);
         this._list[this._list.length] = new SZDZVO(83203,900000,3000,400,[new NeedVO(10161,45),new NeedVO(10164,45),new NeedVO(10162,45)]);
         this._list[this._list.length] = new SZDZVO(83301,900000,3000,400,[new NeedVO(10165,45),new NeedVO(10166,45),new NeedVO(10167,45)]);
         this._list[this._list.length] = new SZDZVO(83302,900000,3000,400,[new NeedVO(10165,45),new NeedVO(10167,45),new NeedVO(10168,45)]);
         this._list[this._list.length] = new SZDZVO(83303,900000,3000,400,[new NeedVO(10165,45),new NeedVO(10168,45),new NeedVO(10166,45)]);
         this._list[this._list.length] = new SZDZVO(82401,800000,2000,600,[new NeedVO(10151,30),new NeedVO(10152,30),new NeedVO(10153,30)]);
         this._list[this._list.length] = new SZDZVO(82402,800000,2000,600,[new NeedVO(10154,30),new NeedVO(10155,30),new NeedVO(10156,30)]);
         this._list[this._list.length] = new SZDZVO(82403,800000,2000,600,[new NeedVO(10157,30),new NeedVO(10158,30),new NeedVO(10151,30)]);
         this._list[this._list.length] = new SZDZVO(82501,800000,2000,600,[new NeedVO(10158,30),new NeedVO(10161,30),new NeedVO(10162,30)]);
         this._list[this._list.length] = new SZDZVO(82502,800000,2000,600,[new NeedVO(10163,30),new NeedVO(10164,30),new NeedVO(10165,30)]);
         this._list[this._list.length] = new SZDZVO(82503,800000,2000,600,[new NeedVO(10166,30),new NeedVO(10167,30),new NeedVO(10168,30)]);
         this._list[this._list.length] = new SZDZVO(82001,800000,2000,600,[new NeedVO(10151,30),new NeedVO(10152,30),new NeedVO(10153,30)]);
         this._list[this._list.length] = new SZDZVO(82002,800000,2000,600,[new NeedVO(10151,30),new NeedVO(10153,30),new NeedVO(10154,30)]);
         this._list[this._list.length] = new SZDZVO(82003,800000,2000,600,[new NeedVO(10151,30),new NeedVO(10154,30),new NeedVO(10152,30)]);
         this._list[this._list.length] = new SZDZVO(82101,800000,2000,600,[new NeedVO(10155,30),new NeedVO(10156,30),new NeedVO(10157,30)]);
         this._list[this._list.length] = new SZDZVO(82102,800000,2000,600,[new NeedVO(10155,30),new NeedVO(10157,30),new NeedVO(10158,30)]);
         this._list[this._list.length] = new SZDZVO(82103,800000,2000,600,[new NeedVO(10155,30),new NeedVO(10158,30),new NeedVO(10156,30)]);
         this._list[this._list.length] = new SZDZVO(82201,800000,2000,600,[new NeedVO(10161,30),new NeedVO(10162,30),new NeedVO(10163,30)]);
         this._list[this._list.length] = new SZDZVO(82202,800000,2000,600,[new NeedVO(10161,30),new NeedVO(10163,30),new NeedVO(10164,30)]);
         this._list[this._list.length] = new SZDZVO(82203,800000,2000,600,[new NeedVO(10161,30),new NeedVO(10164,30),new NeedVO(10162,30)]);
         this._list[this._list.length] = new SZDZVO(82301,800000,2000,600,[new NeedVO(10165,30),new NeedVO(10166,30),new NeedVO(10167,30)]);
         this._list[this._list.length] = new SZDZVO(82302,800000,2000,600,[new NeedVO(10165,30),new NeedVO(10167,30),new NeedVO(10168,30)]);
         this._list[this._list.length] = new SZDZVO(82303,800000,2000,600,[new NeedVO(10165,30),new NeedVO(10168,30),new NeedVO(10166,30)]);
         this._list[this._list.length] = new SZDZVO(81001,400000,600,800,[new NeedVO(10151,15),new NeedVO(10152,15),new NeedVO(10153,15)]);
         this._list[this._list.length] = new SZDZVO(81002,400000,600,800,[new NeedVO(10151,15),new NeedVO(10153,15),new NeedVO(10154,15)]);
         this._list[this._list.length] = new SZDZVO(81003,400000,600,800,[new NeedVO(10151,15),new NeedVO(10154,15),new NeedVO(10152,15)]);
         this._list[this._list.length] = new SZDZVO(81101,500000,600,800,[new NeedVO(10155,15),new NeedVO(10156,15),new NeedVO(10157,15)]);
         this._list[this._list.length] = new SZDZVO(81102,500000,600,800,[new NeedVO(10155,15),new NeedVO(10157,15),new NeedVO(10158,15)]);
         this._list[this._list.length] = new SZDZVO(81103,500000,600,800,[new NeedVO(10155,15),new NeedVO(10158,15),new NeedVO(10156,15)]);
         this._list[this._list.length] = new SZDZVO(81201,500000,600,800,[new NeedVO(10161,15),new NeedVO(10162,15),new NeedVO(10163,15)]);
         this._list[this._list.length] = new SZDZVO(81202,500000,600,800,[new NeedVO(10161,15),new NeedVO(10163,15),new NeedVO(10164,15)]);
         this._list[this._list.length] = new SZDZVO(81203,500000,600,800,[new NeedVO(10161,15),new NeedVO(10164,15),new NeedVO(10162,15)]);
         this._list[this._list.length] = new SZDZVO(81301,500000,600,800,[new NeedVO(10165,15),new NeedVO(10166,15),new NeedVO(10167,15)]);
         this._list[this._list.length] = new SZDZVO(81302,500000,600,800,[new NeedVO(10165,15),new NeedVO(10167,15),new NeedVO(10168,15)]);
         this._list[this._list.length] = new SZDZVO(81303,500000,600,800,[new NeedVO(10165,15),new NeedVO(10168,15),new NeedVO(10166,15)]);
         this._list[this._list.length] = new SZDZVO(80001,200000,300,1000,[new NeedVO(10151,6),new NeedVO(10152,6),new NeedVO(10153,6)]);
         this._list[this._list.length] = new SZDZVO(80002,200000,300,1000,[new NeedVO(10151,6),new NeedVO(10153,6),new NeedVO(10154,6)]);
         this._list[this._list.length] = new SZDZVO(80003,200000,300,1000,[new NeedVO(10151,6),new NeedVO(10154,6),new NeedVO(10152,6)]);
         this._list[this._list.length] = new SZDZVO(80101,250000,300,1000,[new NeedVO(10155,6),new NeedVO(10156,6),new NeedVO(10157,6)]);
         this._list[this._list.length] = new SZDZVO(80102,250000,300,1000,[new NeedVO(10155,6),new NeedVO(10157,6),new NeedVO(10158,6)]);
         this._list[this._list.length] = new SZDZVO(80103,250000,300,1000,[new NeedVO(10155,6),new NeedVO(10158,6),new NeedVO(10156,6)]);
         this._list[this._list.length] = new SZDZVO(80201,250000,300,1000,[new NeedVO(10161,6),new NeedVO(10162,6),new NeedVO(10163,6)]);
         this._list[this._list.length] = new SZDZVO(80202,250000,300,1000,[new NeedVO(10161,6),new NeedVO(10163,6),new NeedVO(10164,6)]);
         this._list[this._list.length] = new SZDZVO(80203,250000,300,1000,[new NeedVO(10161,6),new NeedVO(10164,6),new NeedVO(10162,6)]);
         this._list[this._list.length] = new SZDZVO(80301,250000,300,1000,[new NeedVO(10165,6),new NeedVO(10166,6),new NeedVO(10167,6)]);
         this._list[this._list.length] = new SZDZVO(80302,250000,300,1000,[new NeedVO(10165,6),new NeedVO(10167,6),new NeedVO(10168,6)]);
         this._list[this._list.length] = new SZDZVO(80303,250000,300,1000,[new NeedVO(10165,6),new NeedVO(10168,6),new NeedVO(10166,6)]);
      }
      
      public function get list() : Array
      {
         return this._list;
      }
   }
}

