package mogames.gameData.flag
{
   import mogames.gameData.flag.base.FlagVO;
   import mogames.gameData.flag.base.FubenFlagVO;
   import mogames.gameData.flag.base.GameFlag;
   import mogames.gameData.flag.base.RoleFlag;
   import mogames.gameData.flag.vo.FlagLiangVO;
   import mogames.gameData.flag.vo.FlagMJSDVO;
   import mogames.gameData.flag.vo.FlagPalaceVO;
   import mogames.gameData.flag.vo.FlagTavernVO;
   import mogames.gameData.flag.vo.FlagUnionFBVO;
   import mogames.gameData.flag.vo.FlagZhuanVO;
   
   public class FlagProxy
   {
      
      private static var _instance:FlagProxy;
      
      public var openFlag:GameFlag;
      
      public var limitFlag:GameFlag;
      
      public var numFlag:GameFlag;
      
      public var roleFlag:RoleFlag;
      
      public var fubenFlag:GameFlag;
      
      public function FlagProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.openFlag = new GameFlag();
         this.limitFlag = new GameFlag();
         this.numFlag = new GameFlag();
         this.roleFlag = new RoleFlag();
         this.fubenFlag = new GameFlag();
      }
      
      public static function instance() : FlagProxy
      {
         if(!_instance)
         {
            _instance = new FlagProxy();
         }
         return _instance;
      }
      
      public function startNew() : void
      {
         this.openFlag.startNew();
         this.limitFlag.startNew();
         this.numFlag.startNew();
         this.roleFlag.startNew();
         this.fubenFlag.startNew();
         this.openFlag.addFlag(new FlagVO(10000,50,false,true));
         this.openFlag.addFlag(new FlagVO(10001,50,false,true));
         this.openFlag.addFlag(new FlagVO(10,1,false,true));
         this.openFlag.addFlag(new FlagVO(11));
         this.openFlag.addFlag(new FlagVO(12));
         this.openFlag.addFlag(new FlagVO(13));
         this.openFlag.addFlag(new FlagVO(14));
         this.openFlag.addFlag(new FlagVO(15));
         this.openFlag.addFlag(new FlagVO(16));
         this.openFlag.addFlag(new FlagVO(17));
         this.openFlag.addFlag(new FlagVO(18));
         this.openFlag.addFlag(new FlagVO(19));
         this.openFlag.addFlag(new FlagVO(20));
         this.openFlag.addFlag(new FlagVO(21));
         this.openFlag.addFlag(new FlagVO(22));
         this.openFlag.addFlag(new FlagVO(23));
         this.openFlag.addFlag(new FlagVO(24));
         this.openFlag.addFlag(new FlagVO(25));
         this.openFlag.addFlag(new FlagVO(26));
         this.openFlag.addFlag(new FlagVO(29));
         this.openFlag.addFlag(new FlagVO(30));
         this.openFlag.addFlag(new FlagVO(31));
         this.openFlag.addFlag(new FlagVO(32));
         this.openFlag.addFlag(new FlagVO(33));
         this.openFlag.addFlag(new FlagVO(34));
         this.openFlag.addFlag(new FlagVO(35));
         this.openFlag.addFlag(new FlagVO(36));
         this.openFlag.addFlag(new FlagVO(37));
         this.openFlag.addFlag(new FlagVO(38));
         this.openFlag.addFlag(new FlagVO(39));
         this.openFlag.addFlag(new FlagVO(40));
         this.openFlag.addFlag(new FlagVO(41));
         this.openFlag.addFlag(new FlagVO(42));
         this.openFlag.addFlag(new FlagVO(43));
         this.openFlag.addFlag(new FlagVO(44));
         this.openFlag.addFlag(new FlagVO(45));
         this.openFlag.addFlag(new FlagVO(46));
         this.openFlag.addFlag(new FlagVO(47));
         this.openFlag.addFlag(new FlagVO(48));
         this.openFlag.addFlag(new FlagVO(100));
         this.openFlag.addFlag(new FlagVO(101));
         this.openFlag.addFlag(new FlagVO(102));
         this.openFlag.addFlag(new FlagVO(103));
         this.openFlag.addFlag(new FlagVO(111));
         this.openFlag.addFlag(new FlagVO(112));
         this.openFlag.addFlag(new FlagVO(113));
         this.openFlag.addFlag(new FlagVO(114));
         this.openFlag.addFlag(new FlagVO(115));
         this.openFlag.addFlag(new FlagVO(116));
         this.openFlag.addFlag(new FlagVO(117));
         this.openFlag.addFlag(new FlagVO(118));
         this.openFlag.addFlag(new FlagVO(119));
         this.openFlag.addFlag(new FlagVO(120));
         this.openFlag.addFlag(new FlagVO(121));
         this.openFlag.addFlag(new FlagVO(122));
         this.openFlag.addFlag(new FlagVO(123));
         this.openFlag.addFlag(new FlagVO(124));
         this.openFlag.addFlag(new FlagVO(125));
         this.openFlag.addFlag(new FlagVO(126));
         this.openFlag.addFlag(new FlagVO(127));
         this.openFlag.addFlag(new FlagVO(128));
         this.openFlag.addFlag(new FlagVO(129));
         this.openFlag.addFlag(new FlagVO(130));
         this.openFlag.addFlag(new FlagVO(131));
         this.openFlag.addFlag(new FlagVO(132));
         this.openFlag.addFlag(new FlagVO(133));
         this.openFlag.addFlag(new FlagVO(134));
         this.openFlag.addFlag(new FlagVO(135));
         this.openFlag.addFlag(new FlagVO(136));
         this.openFlag.addFlag(new FlagVO(137));
         this.openFlag.addFlag(new FlagVO(138));
         this.openFlag.addFlag(new FlagVO(139));
         this.openFlag.addFlag(new FlagVO(140));
         this.openFlag.addFlag(new FlagVO(141));
         this.openFlag.addFlag(new FlagVO(142));
         this.openFlag.addFlag(new FlagVO(143));
         this.openFlag.addFlag(new FlagVO(144));
         this.openFlag.addFlag(new FlagVO(145));
         this.openFlag.addFlag(new FlagVO(146));
         this.openFlag.addFlag(new FlagVO(147));
         this.openFlag.addFlag(new FlagVO(148));
         this.openFlag.addFlag(new FlagVO(149));
         this.openFlag.addFlag(new FlagVO(150));
         this.openFlag.addFlag(new FlagVO(151));
         this.openFlag.addFlag(new FlagVO(152,1,false,true));
         this.openFlag.addFlag(new FlagVO(153,1,false,true));
         this.openFlag.addFlag(new FlagVO(154,1,false,true));
         this.openFlag.addFlag(new FlagVO(155,1,false,true));
         this.openFlag.addFlag(new FlagVO(156));
         this.openFlag.addFlag(new FlagVO(157));
         this.openFlag.addFlag(new FlagVO(158));
         this.openFlag.addFlag(new FlagVO(159));
         this.openFlag.addFlag(new FlagVO(160));
         this.openFlag.addFlag(new FlagVO(161));
         this.openFlag.addFlag(new FlagVO(162));
         this.openFlag.addFlag(new FlagVO(163));
         this.openFlag.addFlag(new FlagVO(164));
         this.openFlag.addFlag(new FlagVO(165));
         this.openFlag.addFlag(new FlagVO(166));
         this.openFlag.addFlag(new FlagVO(167));
         this.openFlag.addFlag(new FlagVO(168));
         this.openFlag.addFlag(new FlagVO(169));
         this.openFlag.addFlag(new FlagVO(170));
         this.openFlag.addFlag(new FlagVO(171));
         this.openFlag.addFlag(new FlagVO(172));
         this.openFlag.addFlag(new FlagVO(173));
         this.openFlag.addFlag(new FlagVO(174));
         this.openFlag.addFlag(new FlagVO(175));
         this.openFlag.addFlag(new FlagVO(176));
         this.openFlag.addFlag(new FlagVO(177));
         this.openFlag.addFlag(new FlagVO(178));
         this.openFlag.addFlag(new FlagVO(179));
         this.openFlag.addFlag(new FlagVO(180));
         this.openFlag.addFlag(new FlagVO(181));
         this.openFlag.addFlag(new FlagVO(182));
         this.openFlag.addFlag(new FlagVO(183));
         this.openFlag.addFlag(new FlagVO(184));
         this.openFlag.addFlag(new FlagVO(185));
         this.openFlag.addFlag(new FlagVO(186));
         this.openFlag.addFlag(new FlagVO(187));
         this.openFlag.addFlag(new FlagVO(188));
         this.openFlag.addFlag(new FlagVO(189));
         this.openFlag.addFlag(new FlagVO(190));
         this.openFlag.addFlag(new FlagVO(191));
         this.openFlag.addFlag(new FlagVO(192));
         this.openFlag.addFlag(new FlagVO(193));
         this.openFlag.addFlag(new FlagVO(194));
         this.openFlag.addFlag(new FlagVO(195));
         this.openFlag.addFlag(new FlagVO(196));
         this.openFlag.addFlag(new FlagVO(197));
         this.openFlag.addFlag(new FlagVO(198));
         this.openFlag.addFlag(new FlagVO(199));
         this.openFlag.addFlag(new FlagVO(200));
         this.openFlag.addFlag(new FlagVO(201));
         this.openFlag.addFlag(new FlagVO(202));
         this.openFlag.addFlag(new FlagVO(203));
         this.openFlag.addFlag(new FlagVO(204));
         this.openFlag.addFlag(new FlagVO(205,1,false,true));
         this.openFlag.addFlag(new FlagVO(206,1,false,true));
         this.openFlag.addFlag(new FlagVO(207,1,false,true));
         this.openFlag.addFlag(new FlagVO(208,1,false,true));
         this.openFlag.addFlag(new FlagVO(209,1,false,true));
         this.openFlag.addFlag(new FlagVO(210,1,false,true));
         this.openFlag.addFlag(new FlagVO(211));
         this.openFlag.addFlag(new FlagVO(212));
         this.openFlag.addFlag(new FlagVO(213));
         this.openFlag.addFlag(new FlagVO(214));
         this.openFlag.addFlag(new FlagVO(215));
         this.openFlag.addFlag(new FlagVO(216));
         this.openFlag.addFlag(new FlagVO(217));
         this.openFlag.addFlag(new FlagVO(218));
         this.openFlag.addFlag(new FlagVO(219));
         this.openFlag.addFlag(new FlagVO(220));
         this.openFlag.addFlag(new FlagVO(221));
         this.openFlag.addFlag(new FlagVO(222));
         this.openFlag.addFlag(new FlagVO(223));
         this.openFlag.addFlag(new FlagVO(224));
         this.openFlag.addFlag(new FlagVO(225));
         this.openFlag.addFlag(new FlagVO(226));
         this.openFlag.addFlag(new FlagVO(227));
         this.openFlag.addFlag(new FlagVO(228));
         this.openFlag.addFlag(new FlagVO(229));
         this.openFlag.addFlag(new FlagVO(230));
         this.openFlag.addFlag(new FlagVO(231));
         this.openFlag.addFlag(new FlagVO(232));
         this.openFlag.addFlag(new FlagVO(233));
         this.openFlag.addFlag(new FlagVO(234));
         this.openFlag.addFlag(new FlagVO(235));
         this.openFlag.addFlag(new FlagVO(236));
         this.openFlag.addFlag(new FlagVO(237));
         this.openFlag.addFlag(new FlagVO(238));
         this.openFlag.addFlag(new FlagVO(239));
         this.openFlag.addFlag(new FlagVO(240));
         this.openFlag.addFlag(new FlagVO(241));
         this.openFlag.addFlag(new FlagVO(242));
         this.openFlag.addFlag(new FlagVO(243));
         this.openFlag.addFlag(new FlagVO(244));
         this.openFlag.addFlag(new FlagVO(245));
         this.openFlag.addFlag(new FlagVO(246));
         this.openFlag.addFlag(new FlagVO(247));
         this.openFlag.addFlag(new FlagVO(248));
         this.openFlag.addFlag(new FlagVO(249));
         this.openFlag.addFlag(new FlagVO(250));
         this.openFlag.addFlag(new FlagVO(2400));
         this.openFlag.addFlag(new FlagVO(2401));
         this.openFlag.addFlag(new FlagVO(2402));
         this.openFlag.addFlag(new FlagVO(2403));
         this.openFlag.addFlag(new FlagVO(2404));
         this.openFlag.addFlag(new FlagVO(2405));
         this.openFlag.addFlag(new FlagUnionFBVO(251));
         this.openFlag.addFlag(new FlagUnionFBVO(252));
         this.openFlag.addFlag(new FlagUnionFBVO(253));
         this.openFlag.addFlag(new FlagUnionFBVO(254));
         this.openFlag.addFlag(new FlagUnionFBVO(255));
         this.openFlag.addFlag(new FlagUnionFBVO(256));
         this.openFlag.addFlag(new FlagUnionFBVO(257));
         this.openFlag.addFlag(new FlagVO(271));
         this.openFlag.addFlag(new FlagVO(272,1,false,true));
         this.openFlag.addFlag(new FlagVO(273,1,false,true));
         this.openFlag.addFlag(new FlagVO(274,1,false,true));
         this.openFlag.addFlag(new FlagVO(275,1,false,true));
         this.openFlag.addFlag(new FlagVO(276,1,false,true));
         this.openFlag.addFlag(new FlagVO(277,1,false,true));
         this.openFlag.addFlag(new FlagVO(278,1,false,true));
         this.openFlag.addFlag(new FlagVO(279));
         this.openFlag.addFlag(new FlagVO(280));
         this.openFlag.addFlag(new FlagVO(281));
         this.openFlag.addFlag(new FlagVO(282));
         this.openFlag.addFlag(new FlagVO(283));
         this.openFlag.addFlag(new FlagVO(284));
         this.openFlag.addFlag(new FlagVO(285));
         this.openFlag.addFlag(new FlagVO(286));
         this.openFlag.addFlag(new FlagVO(287));
         this.openFlag.addFlag(new FlagVO(288));
         this.openFlag.addFlag(new FlagVO(289));
         this.limitFlag.addFlag(new FlagVO(300,12));
         this.limitFlag.addFlag(new SevenFlagVO(301));
         this.limitFlag.addFlag(new FlagVO(302,1,false,true));
         this.limitFlag.addFlag(new FlagVO(303,1,false,true));
         this.limitFlag.addFlag(new FlagVO(304,1,false,true));
         this.limitFlag.addFlag(new FlagVO(305,1,false,true));
         this.limitFlag.addFlag(new FlagVO(306,1,false,true));
         this.limitFlag.addFlag(new FlagVO(307,1,false,true));
         this.limitFlag.addFlag(new FlagVO(308,1,false,true));
         this.limitFlag.addFlag(new FlagVO(309,1,false,true));
         this.limitFlag.addFlag(new FlagVO(310,2,false,true));
         this.limitFlag.addFlag(new FlagVO(311,1,false,true));
         this.limitFlag.addFlag(new FlagVO(312,3,false,true));
         this.limitFlag.addFlag(new FlagTavernVO(320));
         this.limitFlag.addFlag(new FlagPalaceVO(321));
         this.limitFlag.addFlag(new FlagLiangVO(322));
         this.limitFlag.addFlag(new FlagVO(325,5));
         this.limitFlag.addFlag(new FlagZhuanVO(330));
         this.limitFlag.addFlag(new FlagVO(333,5,false,true));
         this.limitFlag.addFlag(new FlagVO(334,5,false,true));
         this.limitFlag.addFlag(new FlagVO(335,5,false,true));
         this.limitFlag.addFlag(new FlagMJSDVO(338));
         this.limitFlag.addFlag(new FlagVO(345,3,false,true));
         this.limitFlag.addFlag(new FlagVO(355,5,false,true));
         this.limitFlag.addFlag(new FlagVO(369,3,false,true));
         this.numFlag.addFlag(new FlagVO(600,999,true));
         this.numFlag.addFlag(new FlagVO(601,999,true));
         this.numFlag.addFlag(new FlagVO(602,999,true,true));
         this.numFlag.addFlag(new FlagVO(603,1,true));
         this.numFlag.addFlag(new FlagVO(604,1,true));
         this.numFlag.addFlag(new FlagVO(605,1,true));
         this.numFlag.addFlag(new FlagVO(606,1,true));
         this.numFlag.addFlag(new FlagVO(607,1,true,true));
         this.numFlag.addFlag(new FlagVO(608,9999,true));
         this.numFlag.addFlag(new FlagVO(610,9999,true));
         this.numFlag.addFlag(new FlagVO(611,9999,true));
         this.numFlag.addFlag(new FlagVO(612,9999,true));
         this.numFlag.addFlag(new FlagVO(613,9999,true));
         this.numFlag.addFlag(new FlagVO(614,9999,true));
         this.fubenFlag.addFlag(new FubenFlagVO(1010));
         this.fubenFlag.addFlag(new FlagVO(1011));
         this.fubenFlag.addFlag(new FubenFlagVO(1020));
         this.fubenFlag.addFlag(new FlagVO(1021));
         this.fubenFlag.addFlag(new FubenFlagVO(1030));
         this.fubenFlag.addFlag(new FlagVO(1031));
         this.fubenFlag.addFlag(new FubenFlagVO(1040));
         this.fubenFlag.addFlag(new FlagVO(1041));
         this.fubenFlag.addFlag(new FubenFlagVO(1050));
         this.fubenFlag.addFlag(new FlagVO(1051));
         this.fubenFlag.addFlag(new FubenFlagVO(1060));
         this.fubenFlag.addFlag(new FlagVO(1061));
         this.openFlag.addFlag(new FlagVO(1001));
         this.openFlag.addFlag(new FlagVO(1002));
         this.openFlag.addFlag(new FlagVO(1003));
         this.openFlag.addFlag(new FlagVO(1004));
         this.openFlag.addFlag(new FlagVO(1005));
         this.openFlag.addFlag(new FlagVO(1006));
         this.openFlag.addFlag(new FlagVO(1007));
         this.openFlag.addFlag(new FlagVO(1008));
         this.openFlag.addFlag(new FlagVO(1009));
         this.openFlag.addFlag(new FlagVO(10101));
         this.openFlag.addFlag(new FlagVO(10111));
         this.openFlag.addFlag(new FlagVO(1012));
         this.openFlag.addFlag(new FlagVO(1013));
         this.openFlag.addFlag(new FlagVO(1014));
         this.openFlag.addFlag(new FlagVO(1015));
         this.openFlag.addFlag(new FlagVO(1016));
         this.openFlag.addFlag(new FlagVO(1017));
         this.openFlag.addFlag(new FlagVO(1018));
         this.openFlag.addFlag(new FlagVO(1019));
         this.openFlag.addFlag(new FlagVO(10201));
         this.openFlag.addFlag(new FlagVO(10211));
         this.openFlag.addFlag(new FlagVO(1022));
         this.openFlag.addFlag(new FlagVO(1023));
         this.openFlag.addFlag(new FlagVO(1024));
         this.openFlag.addFlag(new FlagVO(1025));
         this.openFlag.addFlag(new FlagVO(1026));
         this.openFlag.addFlag(new FlagVO(1027));
         this.openFlag.addFlag(new FlagVO(1028));
         this.openFlag.addFlag(new FlagVO(1029));
         this.openFlag.addFlag(new FlagVO(10301));
         this.openFlag.addFlag(new FlagVO(10311));
         this.openFlag.addFlag(new FlagVO(1032));
         this.openFlag.addFlag(new FlagVO(1033));
         this.openFlag.addFlag(new FlagVO(1034));
         this.openFlag.addFlag(new FlagVO(1035));
         this.openFlag.addFlag(new FlagVO(1036));
         this.openFlag.addFlag(new FlagVO(1037));
         this.openFlag.addFlag(new FlagVO(1038));
         this.openFlag.addFlag(new FlagVO(1039));
         this.openFlag.addFlag(new FlagVO(10401));
         this.openFlag.addFlag(new FlagVO(10411));
         this.openFlag.addFlag(new FlagVO(1042));
         this.openFlag.addFlag(new FlagVO(1043));
         this.openFlag.addFlag(new FlagVO(1044));
         this.openFlag.addFlag(new FlagVO(1045));
         this.openFlag.addFlag(new FlagVO(1046));
         this.openFlag.addFlag(new FlagVO(1047));
         this.openFlag.addFlag(new FlagVO(1048));
         this.openFlag.addFlag(new FlagVO(1049));
         this.openFlag.addFlag(new FlagVO(10501));
         this.openFlag.addFlag(new FlagVO(10511));
         this.openFlag.addFlag(new FlagVO(1052));
         this.openFlag.addFlag(new FlagVO(1053));
         this.openFlag.addFlag(new FlagVO(1054));
         this.openFlag.addFlag(new FlagVO(1055));
         this.openFlag.addFlag(new FlagVO(1056));
         this.openFlag.addFlag(new FlagVO(1057));
         this.openFlag.addFlag(new FlagVO(1058));
         this.openFlag.addFlag(new FlagVO(1059));
         this.openFlag.addFlag(new FlagVO(1062));
         this.openFlag.addFlag(new FlagVO(1063));
         this.openFlag.addFlag(new FlagVO(1064));
         this.openFlag.addFlag(new FlagVO(1065));
         this.openFlag.addFlag(new FlagVO(1066));
         this.openFlag.addFlag(new FlagVO(1067));
         this.openFlag.addFlag(new FlagVO(1068));
         this.openFlag.addFlag(new FlagVO(1069));
         this.openFlag.addFlag(new FlagVO(1070));
         this.openFlag.addFlag(new FlagVO(1071));
         this.openFlag.addFlag(new FlagVO(1072));
         this.openFlag.addFlag(new FlagVO(1073));
         this.openFlag.addFlag(new FlagVO(1074));
         this.openFlag.addFlag(new FlagVO(1075));
         this.openFlag.addFlag(new FlagVO(1076));
         this.openFlag.addFlag(new FlagVO(1077));
         this.openFlag.addFlag(new FlagVO(1078));
         this.openFlag.addFlag(new FlagVO(1079));
         this.openFlag.addFlag(new FlagVO(1080));
         this.openFlag.addFlag(new FlagVO(1081));
         this.openFlag.addFlag(new FlagVO(1082));
         this.openFlag.addFlag(new FlagVO(1083));
         this.openFlag.addFlag(new FlagVO(1084));
         this.openFlag.addFlag(new FlagVO(1085));
         this.openFlag.addFlag(new FlagVO(1086));
         this.openFlag.addFlag(new FlagVO(1087));
         this.openFlag.addFlag(new FlagVO(1088));
         this.openFlag.addFlag(new FlagVO(1089));
         this.openFlag.addFlag(new FlagVO(1090));
         this.openFlag.addFlag(new FlagVO(1091));
         this.openFlag.addFlag(new FlagVO(1092));
         this.openFlag.addFlag(new FlagVO(1093));
         this.openFlag.addFlag(new FlagVO(1094));
         this.openFlag.addFlag(new FlagVO(1095));
         this.openFlag.addFlag(new FlagVO(1096));
         this.openFlag.addFlag(new FlagVO(1097));
         this.openFlag.addFlag(new FlagVO(1098));
         this.openFlag.addFlag(new FlagVO(1099));
         this.openFlag.addFlag(new FlagVO(1100));
         this.openFlag.addFlag(new FlagVO(1101));
         this.fubenFlag.addFlag(new FubenFlagVO(2000));
         this.fubenFlag.addFlag(new FlagVO(3010,2,false,true));
         this.fubenFlag.addFlag(new FlagVO(3020,2,false,true));
         this.fubenFlag.addFlag(new FlagVO(3030,2,false,true));
         this.fubenFlag.addFlag(new FlagVO(3040,2,false,true));
         this.fubenFlag.addFlag(new FlagVO(3050,2,false,true));
         this.fubenFlag.addFlag(new FlagVO(3060,2,false,true));
         this.fubenFlag.addFlag(new FlagVO(3070,2,false,true));
         this.fubenFlag.addFlag(new FubenFlagVO(4010));
         this.fubenFlag.addFlag(new FlagVO(4011));
         this.fubenFlag.addFlag(new FubenFlagVO(4020));
         this.fubenFlag.addFlag(new FlagVO(4021));
         this.fubenFlag.addFlag(new FubenFlagVO(4030));
         this.fubenFlag.addFlag(new FlagVO(4031));
         this.fubenFlag.addFlag(new FubenFlagVO(4040));
         this.fubenFlag.addFlag(new FlagVO(4041));
         this.fubenFlag.addFlag(new FubenFlagVO(4050));
         this.fubenFlag.addFlag(new FlagVO(4051));
         this.fubenFlag.addFlag(new FubenFlagVO(4060));
         this.fubenFlag.addFlag(new FlagVO(4061));
         this.fubenFlag.addFlag(new FubenFlagVO(4070));
         this.fubenFlag.addFlag(new FlagVO(4071));
         this.fubenFlag.addFlag(new FubenFlagVO(4080));
         this.fubenFlag.addFlag(new FlagVO(4081));
         this.fubenFlag.addFlag(new FubenFlagVO(4090));
         this.fubenFlag.addFlag(new FlagVO(4091));
         this.fubenFlag.addFlag(new FubenFlagVO(4100));
         this.fubenFlag.addFlag(new FlagVO(4101));
         this.fubenFlag.addFlag(new FubenFlagVO(6010));
         this.fubenFlag.addFlag(new FlagVO(6011));
         this.fubenFlag.addFlag(new FubenFlagVO(6020));
         this.fubenFlag.addFlag(new FlagVO(6021));
         this.fubenFlag.addFlag(new FubenFlagVO(6030));
         this.fubenFlag.addFlag(new FlagVO(6031));
         this.fubenFlag.addFlag(new FubenFlagVO(6040));
         this.fubenFlag.addFlag(new FlagVO(6041));
         this.fubenFlag.addFlag(new FubenFlagVO(6050));
         this.fubenFlag.addFlag(new FlagVO(6051));
         this.fubenFlag.addFlag(new FubenFlagVO(6060));
         this.fubenFlag.addFlag(new FlagVO(6061));
         this.openFlag.addFlag(new FlagVO(8011));
         this.openFlag.addFlag(new FlagVO(8012));
         this.openFlag.addFlag(new FlagVO(8013));
         this.openFlag.addFlag(new FlagVO(8014));
         this.openFlag.addFlag(new FlagVO(8015));
         this.openFlag.addFlag(new FlagVO(8016));
         this.openFlag.addFlag(new FlagVO(8017));
         this.openFlag.addFlag(new FlagVO(8018));
         this.openFlag.addFlag(new FlagVO(8019));
         this.openFlag.addFlag(new FlagVO(8020));
         this.openFlag.addFlag(new FlagVO(8021));
         this.openFlag.addFlag(new FlagVO(8022));
         this.openFlag.addFlag(new FlagVO(8023));
         this.openFlag.addFlag(new FlagVO(8024));
         this.openFlag.addFlag(new FlagVO(8025));
         this.openFlag.addFlag(new FlagVO(8026));
         this.openFlag.addFlag(new FlagVO(8027));
         this.openFlag.addFlag(new FlagVO(8028));
         this.openFlag.addFlag(new FlagVO(8029));
         this.openFlag.addFlag(new FlagVO(8030));
         this.openFlag.addFlag(new FlagVO(8031));
         this.openFlag.addFlag(new FlagVO(8032));
         this.openFlag.addFlag(new FlagVO(8033));
         this.openFlag.addFlag(new FlagVO(8034));
         this.openFlag.addFlag(new FlagVO(8035));
         this.openFlag.addFlag(new FlagVO(8036));
         this.openFlag.addFlag(new FlagVO(8037));
         this.openFlag.addFlag(new FlagVO(8038));
         this.openFlag.addFlag(new FlagVO(8039));
         this.openFlag.addFlag(new FlagVO(8040));
         this.openFlag.addFlag(new FlagVO(8041));
         this.openFlag.addFlag(new FlagVO(8042));
         this.openFlag.addFlag(new FlagVO(8043));
         this.openFlag.addFlag(new FlagVO(8044));
         this.openFlag.addFlag(new FlagVO(8045));
         this.openFlag.addFlag(new FlagVO(8046));
         this.openFlag.addFlag(new FlagVO(8047));
         this.openFlag.addFlag(new FlagVO(8048));
         this.openFlag.addFlag(new FlagVO(8049));
         this.openFlag.addFlag(new FlagVO(8050));
         this.openFlag.addFlag(new FlagVO(8051));
         this.openFlag.addFlag(new FlagVO(8052));
         this.openFlag.addFlag(new FlagVO(8053));
         this.openFlag.addFlag(new FlagVO(8054));
         this.openFlag.addFlag(new FlagVO(8055));
         this.openFlag.addFlag(new FlagVO(8056));
         this.openFlag.addFlag(new FlagVO(8057));
         this.openFlag.addFlag(new FlagVO(8058));
         this.openFlag.addFlag(new FlagVO(8059));
         this.openFlag.addFlag(new FlagVO(8060));
         this.openFlag.addFlag(new FlagVO(8061));
         this.openFlag.addFlag(new FlagVO(8062));
         this.openFlag.addFlag(new FlagVO(8063));
         this.openFlag.addFlag(new FlagVO(8064));
         this.openFlag.addFlag(new FlagVO(8065));
         this.openFlag.addFlag(new FlagVO(8066));
         this.openFlag.addFlag(new FlagVO(8067));
         this.openFlag.addFlag(new FlagVO(8068));
         this.openFlag.addFlag(new FlagVO(8069));
         this.openFlag.addFlag(new FlagVO(8070));
         this.openFlag.addFlag(new FlagVO(8071));
         this.openFlag.addFlag(new FlagVO(8072));
         this.openFlag.addFlag(new FlagVO(8073));
         this.openFlag.addFlag(new FlagVO(8074));
         this.openFlag.addFlag(new FlagVO(8075));
         this.openFlag.addFlag(new FlagVO(8076));
         this.openFlag.addFlag(new FlagVO(8077));
         this.openFlag.addFlag(new FlagVO(8078));
         this.openFlag.addFlag(new FlagVO(8079));
         this.openFlag.addFlag(new FlagVO(8080));
         this.openFlag.addFlag(new FlagVO(8081));
         this.openFlag.addFlag(new FlagVO(8082));
         this.openFlag.addFlag(new FlagVO(8083));
         this.openFlag.addFlag(new FlagVO(8084));
         this.openFlag.addFlag(new FlagVO(8085));
         this.openFlag.addFlag(new FlagVO(8086));
         this.openFlag.addFlag(new FlagVO(8087));
         this.openFlag.addFlag(new FlagVO(8088));
         this.openFlag.addFlag(new FlagVO(8089));
         this.openFlag.addFlag(new FlagVO(8090));
         this.openFlag.addFlag(new FlagVO(8091));
         this.openFlag.addFlag(new FlagVO(8092));
         this.openFlag.addFlag(new FlagVO(8093));
         this.openFlag.addFlag(new FlagVO(8094));
         this.openFlag.addFlag(new FlagVO(8095));
         this.openFlag.addFlag(new FlagVO(8096));
         this.openFlag.addFlag(new FlagVO(8097));
         this.openFlag.addFlag(new FlagVO(8098));
         this.openFlag.addFlag(new FlagVO(8099));
         this.openFlag.addFlag(new FlagVO(8100));
         this.openFlag.addFlag(new FlagVO(8101));
         this.openFlag.addFlag(new FlagVO(8102));
         this.openFlag.addFlag(new FlagVO(8103));
         this.openFlag.addFlag(new FlagVO(8104));
         this.openFlag.addFlag(new FlagVO(8105));
         this.openFlag.addFlag(new FlagVO(8106));
         this.openFlag.addFlag(new FlagVO(8107));
         this.openFlag.addFlag(new FlagVO(8108));
         this.openFlag.addFlag(new FlagVO(8109));
         this.openFlag.addFlag(new FlagVO(8110));
         this.openFlag.addFlag(new FlagVO(8111));
         this.openFlag.addFlag(new FlagVO(8112));
         this.openFlag.addFlag(new FlagVO(8113));
         this.openFlag.addFlag(new FlagVO(8114));
         this.openFlag.addFlag(new FlagVO(8115));
         this.openFlag.addFlag(new FlagVO(8116));
         this.openFlag.addFlag(new FlagVO(8117));
         this.openFlag.addFlag(new FlagVO(8118));
         this.openFlag.addFlag(new FlagVO(8119));
         this.openFlag.addFlag(new FlagVO(8120));
         this.openFlag.addFlag(new FlagVO(8121));
         this.openFlag.addFlag(new FlagVO(8122));
         this.openFlag.addFlag(new FlagVO(8123));
         this.openFlag.addFlag(new FlagVO(8124));
         this.openFlag.addFlag(new FlagVO(8125));
         this.openFlag.addFlag(new FlagVO(8126));
         this.openFlag.addFlag(new FlagVO(8127));
         this.openFlag.addFlag(new FlagVO(8128));
         this.openFlag.addFlag(new FlagVO(8129));
         this.openFlag.addFlag(new FlagVO(8130));
      }
      
      public function set loadData(param1:Object) : void
      {
         this.startNew();
         if(!param1)
         {
            return;
         }
         this.openFlag.loadData = param1.open;
         this.limitFlag.loadData = param1.limit;
         this.numFlag.loadData = param1.num;
         if(param1.fuben)
         {
            this.fubenFlag.loadData = param1.fuben;
         }
         if(param1.role)
         {
            this.roleFlag.loadData = param1.role;
         }
      }
      
      public function get saveData() : Object
      {
         return {
            "open":this.openFlag.saveData,
            "limit":this.limitFlag.saveData,
            "num":this.numFlag.saveData,
            "role":this.roleFlag.saveData,
            "fuben":this.fubenFlag.saveData
         };
      }
      
      public function dailyRefresh() : void
      {
         this.openFlag.dailyRefresh();
         this.limitFlag.dailyRefresh();
         this.numFlag.dailyRefresh();
         this.fubenFlag.dailyRefresh();
      }
   }
}

