package mogames.gameData.huodong.vo
{
   import com.adobe.crypto.MD5;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   import flash.net.URLVariables;
   import mogames.gameData.huodong.BaseExchange;
   import mogames.gameEffect.EffectManager;
   
   public class BaoXiaoExchange extends BaseExchange
   {
      
      private var _url:String = "http://huodong.4399.com/baoxiao/mall/api/ajax.php?act=exchange&";
      
      public function BaoXiaoExchange()
      {
         super();
      }
      
      override public function startExchange(... rest) : void
      {
         var _loc2_:String = String(rest[1]);
         _req = new URLRequest(this._url + "cid=3&code=" + _loc2_ + "&secure=" + MD5.hash(_loc2_ + "@#dfl3892$#90EDSF&%$"));
         var _loc3_:URLVariables = new URLVariables();
         _req.method = URLRequestMethod.GET;
         _loader.load(_req);
      }
      
      override protected function handlerLoaded(param1:Object) : void
      {
         super.handlerLoaded(param1);
         var _loc2_:String = String(param1);
         if(_loc2_ == "1")
         {
            _okFunc();
            return;
         }
         switch(_loc2_)
         {
            case "5":
               EffectManager.addPureText("加密验证失败！");
               break;
            case "2":
               EffectManager.addPureText("已经被领取！");
               break;
            case "3":
               EffectManager.addPureText("激活码不存在！");
               break;
            case "4":
               EffectManager.addPureText("请求超时！");
               break;
            default:
               EffectManager.addPureText("发生未知错误，请重试！");
         }
      }
   }
}

