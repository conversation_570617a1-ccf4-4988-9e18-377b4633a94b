package mogames.gameData.huodong.vo
{
   import com.adobe.crypto.MD5;
   import com.mogames.utils.TxtUtil;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   import flash.net.URLVariables;
   import mogames.gameData.huodong.BaseExchange;
   import mogames.gameEffect.EffectManager;
   
   public class DuJiaExchange extends BaseExchange
   {
      
      public function DuJiaExchange()
      {
         super();
         _req = new URLRequest("https://huodong.4399.com/2017/dujia/api.php");
         _key = "asdakjapsdfajslkjasdkWPUls";
      }
      
      override public function startExchange(... rest) : void
      {
         var _loc2_:URLVariables = new URLVariables();
         _loc2_.uid = Number(uid);
         _loc2_.type = rest[0];
         _loc2_.code = rest[1];
         _loc2_.key = MD5.hash(MD5.hash(_key + "" + _loc2_.uid + "" + _loc2_.type + "" + _loc2_.code));
         _req.data = _loc2_;
         _req.method = URLRequestMethod.POST;
         _loader.load(_req);
      }
      
      override protected function handlerLoaded(param1:Object) : void
      {
         super.handlerLoaded(param1);
         var _loc2_:int = int(param1);
         if(_loc2_ == 20000)
         {
            _okFunc();
            return;
         }
         switch(_loc2_)
         {
            case 10004:
               EffectManager.addPureText("请输入正确的兑换码！");
               break;
            default:
               EffectManager.addPureText("发生错误" + TxtUtil.addKuoHao(_loc2_ + ""));
         }
      }
   }
}

