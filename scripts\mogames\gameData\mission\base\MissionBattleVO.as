package mogames.gameData.mission.base
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import mogames.gameData.base.vo.BaseDropVO;
   import mogames.gameData.base.vo.BaseRewardVO;
   
   public class MissionBattleVO
   {
      
      private var _mid:Oint = new Oint();
      
      private var _expLead:Oint = new Oint();
      
      private var _expHero:Oint = new Oint();
      
      private var _rewards:Array;
      
      public var punishs:Array;
      
      public var jsonURL:String;
      
      public var title:String;
      
      public function MissionBattleVO(param1:int, param2:int, param3:int, param4:Array, param5:Array, param6:String, param7:String = "")
      {
         super();
         MathUtil.saveINT(this._mid,param1);
         MathUtil.saveINT(this._expLead,param2);
         MathUtil.saveINT(this._expHero,param3);
         this._rewards = param4;
         this.punishs = param5;
         this.jsonURL = param6;
         this.title = param7;
      }
      
      public function get mid() : int
      {
         return MathUtil.loadINT(this._mid);
      }
      
      public function get expLead() : int
      {
         return MathUtil.loadINT(this._expLead);
      }
      
      public function get expHero() : int
      {
         return MathUtil.loadINT(this._expHero);
      }
      
      public function get rewards() : Array
      {
         var _loc2_:Object = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._rewards)
         {
            if(_loc2_ is BaseRewardVO)
            {
               _loc1_[_loc1_.length] = _loc2_;
            }
            else if(_loc2_ is BaseDropVO && (_loc2_ as BaseDropVO).isDrop)
            {
               _loc1_[_loc1_.length] = (_loc2_ as BaseDropVO).rewardVO;
            }
         }
         return _loc1_;
      }
      
      public function get allRewards() : Array
      {
         var _loc2_:Object = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._rewards)
         {
            if(_loc2_ is BaseRewardVO)
            {
               _loc1_[_loc1_.length] = _loc2_;
            }
            else if(_loc2_ is BaseDropVO)
            {
               _loc1_[_loc1_.length] = (_loc2_ as BaseDropVO).rewardVO;
            }
         }
         return _loc1_;
      }
   }
}

