package mogames.gameData.good.base
{
   import com.mogames.data.ValueVO;
   
   public class ConstATTVO extends ConstGoodVO
   {
      
      protected var _attATK:ValueVO;
      
      protected var _attWIT:ValueVO;
      
      protected var _attHP:ValueVO;
      
      protected var _attDEF:ValueVO;
      
      protected var _attMISS:ValueVO;
      
      protected var _attCRIT:ValueVO;
      
      protected var _attBEI:ValueVO;
      
      protected var _attSPD:ValueVO;
      
      public function ConstATTVO(param1:int, param2:int, param3:ValueVO, param4:ValueVO, param5:ValueVO, param6:ValueVO, param7:ValueVO, param8:ValueVO, param9:ValueVO, param10:ValueVO, param11:int, param12:String, param13:String, param14:String, param15:String)
      {
         super(param1,param2,1,param11,0,param12,param13,param14,param15);
         this._attATK = param3;
         this._attWIT = param4;
         this._attHP = param5;
         this._attDEF = param6;
         this._attMISS = param7;
         this._attCRIT = param8;
         this._attBEI = param9;
         this._attSPD = param10;
      }
      
      public function get attATK() : ValueVO
      {
         return this._attATK;
      }
      
      public function get attWIT() : ValueVO
      {
         return this._attWIT;
      }
      
      public function get attHP() : ValueVO
      {
         return this._attHP;
      }
      
      public function get attDEF() : ValueVO
      {
         return this._attDEF;
      }
      
      public function get attMISS() : ValueVO
      {
         return this._attMISS;
      }
      
      public function get attCRIT() : ValueVO
      {
         return this._attCRIT;
      }
      
      public function get attBEI() : ValueVO
      {
         return this._attBEI;
      }
      
      public function get attSPD() : ValueVO
      {
         return this._attSPD;
      }
      
      public function attInfor(param1:String) : String
      {
         var _loc2_:Array = [];
         if(this.attATK)
         {
            _loc2_[_loc2_.length] = "武力+" + this.attATK.valueStr;
         }
         if(this.attWIT)
         {
            _loc2_[_loc2_.length] = "智力+" + this.attWIT.valueStr;
         }
         if(this.attHP)
         {
            _loc2_[_loc2_.length] = "生命+" + this.attHP.valueStr;
         }
         if(this.attDEF)
         {
            _loc2_[_loc2_.length] = "护甲+" + this.attDEF.valueStr;
         }
         if(this.attMISS)
         {
            _loc2_[_loc2_.length] = "闪避+" + this.attMISS.valueStr + "%";
         }
         if(this.attCRIT)
         {
            _loc2_[_loc2_.length] = "暴击+" + this.attCRIT.valueStr + "%";
         }
         if(this.attBEI)
         {
            _loc2_[_loc2_.length] = "暴击倍数+" + (this.attBEI.value * 0.01).toFixed(1) + "倍";
         }
         if(this.attSPD)
         {
            _loc2_[_loc2_.length] = "移动速度+" + this.attSPD.valueStr;
         }
         return _loc2_.join(param1);
      }
      
      override public function get infor() : String
      {
         return "卡片属性：<br>" + this.attInfor("<br>");
      }
   }
}

