package mogames.gameData.bullet
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   
   public class BulletConstVO
   {
      
      private var _bid:Oint = new Oint();
      
      private var _speed:Oint = new Oint();
      
      private var _type:Oint = new Oint();
      
      private var _width:int;
      
      private var _skin:String;
      
      private var _hurtSkin:String;
      
      private var _hurtSound:String;
      
      public function BulletConstVO(param1:int, param2:int, param3:int, param4:int, param5:String, param6:String, param7:String)
      {
         super();
         MathUtil.saveINT(this._bid,param1);
         MathUtil.saveINT(this._speed,param3);
         MathUtil.saveINT(this._type,param2);
         this._width = param4;
         this._skin = param5;
         this._hurtSkin = param6;
         this._hurtSound = param7;
      }
      
      public function get bid() : int
      {
         return MathUtil.loadINT(this._bid);
      }
      
      public function get type() : int
      {
         return MathUtil.loadINT(this._type);
      }
      
      public function get speed() : int
      {
         return MathUtil.loadINT(this._speed);
      }
      
      public function get width() : int
      {
         return this._width;
      }
      
      public function get bodySkin() : String
      {
         return this._skin;
      }
      
      public function get hurtSkin() : String
      {
         return this._hurtSkin;
      }
      
      public function get hurtSound() : String
      {
         return this._hurtSound;
      }
   }
}

