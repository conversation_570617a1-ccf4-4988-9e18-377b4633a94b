package file
{
   import com.mogames.data.ValueVO;
   import mogames.gameData.office.OfficeDataVO;
   import mogames.gameData.role.HeroProxy;
   import mogames.gameData.role.hero.HeroGameVO;
   
   public class OfficeConfig
   {
      
      private static var _instance:OfficeConfig;
      
      private var _list:Array;
      
      private var _locks:Array;
      
      private var _hires:Array;
      
      private var _fires:Array;
      
      public function OfficeConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : OfficeConfig
      {
         if(!_instance)
         {
            _instance = new OfficeConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._locks = [];
         this._hires = ["多谢主公栽培！","我愿赴汤蹈火在所不惜！","主公英明，吾定当尽忠职守！"];
         this._fires = ["主公！你翻脸也太快了......","主公，再给我一次机会吧。","一切听从主公安排！"];
         this._list = [];
         this._list[this._list.length] = new OfficeDataVO(100,"伍长",null,null,new ValueVO(800,0),null,null,null,null,null,10,220,201,0,0,0);
         this._list[this._list.length] = new OfficeDataVO(101,"什长",null,null,null,new ValueVO(8,0),null,null,null,null,15,600,202,0,0,0);
         this._list[this._list.length] = new OfficeDataVO(102,"都尉",null,null,null,null,null,new ValueVO(10,0),null,null,20,1000,203,0,0,0);
         this._list[this._list.length] = new OfficeDataVO(103,"统领",new ValueVO(22,0),null,null,null,null,null,null,null,25,1500,204,0,0,0);
         this._list[this._list.length] = new OfficeDataVO(104,"都督",null,new ValueVO(8,0),new ValueVO(800,0),null,null,null,null,null,30,2000,205,0,0,0);
         this._list[this._list.length] = new OfficeDataVO(105,"羽林校尉",null,null,new ValueVO(1600,0),null,null,null,new ValueVO(20,0),null,40,0,0,4000,800,0);
         this._list[this._list.length] = new OfficeDataVO(106,"建忠校尉",null,null,null,new ValueVO(15,0),new ValueVO(10,0),null,null,null,50,0,0,5000,0,800);
         this._list[this._list.length] = new OfficeDataVO(107,"越骑校尉",new ValueVO(33,0),null,null,null,null,new ValueVO(10,0),null,null,60,0,0,6000,0,1500);
         this._list[this._list.length] = new OfficeDataVO(108,"司隶校尉",null,null,new ValueVO(2500,0),new ValueVO(30,0),null,null,null,null,70,0,0,7000,0,2000);
         this._list[this._list.length] = new OfficeDataVO(109,"右将军",null,null,null,new ValueVO(60,0),new ValueVO(10,0),null,new ValueVO(25,0),null,80,0,0,10000,0,2500);
         this._list[this._list.length] = new OfficeDataVO(110,"中郎将军",new ValueVO(1,1),null,new ValueVO(3000,0),null,null,null,new ValueVO(30,0),null,0,0,216,15000,0,3000);
         this._list[this._list.length] = new OfficeDataVO(111,"左将军",new ValueVO(88,0),null,null,new ValueVO(5,1),null,new ValueVO(20,0),null,null,100,0,0,30000,0,3500);
         this._list[this._list.length] = new OfficeDataVO(112,"车骑将军",new ValueVO(2,1),null,null,null,new ValueVO(30,0),new ValueVO(30,0),null,null,120,0,0,100000,0,5000);
         this._list[this._list.length] = new OfficeDataVO(113,"骠骑将军",new ValueVO(3,1),new ValueVO(15,0),null,new ValueVO(5,1),null,null,null,null,140,0,0,150000,0,9000);
         this._list[this._list.length] = new OfficeDataVO(114,"大将军",new ValueVO(6,1),null,new ValueVO(10,1),null,null,null,new ValueVO(50,0),null,180,0,0,500000,0,15000);
      }
      
      public function findOffice(param1:int) : OfficeDataVO
      {
         var _loc2_:OfficeDataVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.id == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function checkHero(param1:int) : HeroGameVO
      {
         var _loc3_:HeroGameVO = null;
         var _loc2_:Array = HeroProxy.instance().ownerHeros;
         for each(_loc3_ in _loc2_)
         {
            if(_loc3_.office)
            {
               if(_loc3_.office.id == param1)
               {
                  return _loc3_;
               }
            }
         }
         return null;
      }
      
      public function isLock(param1:int) : Boolean
      {
         return this._locks.indexOf(param1) != -1;
      }
      
      public function get randomHire() : String
      {
         return this._hires[int(Math.random() * this._hires.length)];
      }
      
      public function get randomFire() : String
      {
         return this._fires[int(Math.random() * this._fires.length)];
      }
      
      public function get offices() : Array
      {
         return this._list;
      }
   }
}

