package file
{
   import mogames.ConstData;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.fuben.vo.FubenVO;
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.RoleArgVO;
   
   public class KuangShanConfig
   {
      
      private static var _instance:KuangShanConfig;
      
      public var bossArg:RoleArgVO;
      
      public var burnTime:int;
      
      public var keepTime:int;
      
      public var showTime:int;
      
      public var yuanbings:Array;
      
      public function KuangShanConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : KuangShanConfig
      {
         if(!_instance)
         {
            _instance = new KuangShanConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this.bossArg = new RoleArgVO(166,7000,1000,400,30,10,250,110,null);
         this.burnTime = 3;
         this.keepTime = 22;
         this.showTime = 8;
         this.yuanbings = [];
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(1,new RoleArgVO(101,1300,300,0,30,25,150,100,{
            "atkPer":60,
            "keepTime":10
         }));
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(1,new RoleArgVO(102,1300,300,0,30,25,150,100,{
            "rate":200,
            "hurtBei":1.5,
            "atkPer":50,
            "keepTime":5
         }));
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(1,new RoleArgVO(103,1300,300,0,30,25,150,100,{
            "rate":200,
            "defPer":20,
            "keepTime":3
         }));
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(1,new RoleArgVO(104,1300,300,0,30,25,150,100,{
            "rate":200,
            "curePer":40
         }));
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(1,new RoleArgVO(105,1300,300,0,30,25,150,100,{
            "rate":150,
            "curePer":30
         }));
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(1,new RoleArgVO(106,1300,300,0,30,25,150,100,null));
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(1,new RoleArgVO(107,1300,300,0,30,25,150,100,{
            "rate":200,
            "hurtBei":1.5
         }));
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(1,new RoleArgVO(108,1300,300,0,30,25,150,100,{
            "rate":200,
            "hurtPer":2,
            "keepTime":1
         }));
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(1,new RoleArgVO(109,1300,300,0,30,25,150,100,{
            "rate":200,
            "hurtPer":45,
            "spdPer":50,
            "keepTime":3
         }));
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(1,new RoleArgVO(110,1300,300,0,30,25,150,100,{
            "rate":200,
            "hurtBei":1.8
         }));
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(1,new RoleArgVO(111,1300,300,0,30,25,150,100,{
            "rate":150,
            "keepTime":2
         }));
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(1,new RoleArgVO(112,1300,300,0,30,25,150,100,{
            "rate":50,
            "hurtBei":1.8
         }));
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2700);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(223,8000,500,250,30,25,150,60,null)));
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(106,1600,300,0,30,25,150,100,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(223,8000,500,250,30,25,150,60,null)));
         _loc2_.addEnemy(new WaveEnemyVO(11,new RoleArgVO(107,800,300,0,30,25,150,100,{
            "rate":200,
            "hurtBei":1.5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(223,8000,500,250,30,25,150,60,null)));
         _loc2_.addEnemy(new WaveEnemyVO(11,new RoleArgVO(108,800,300,0,30,25,150,100,{
            "rate":200,
            "hurtPer":2,
            "keepTime":1
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(223,8000,500,250,30,25,150,60,null)));
         _loc2_.addEnemy(new WaveEnemyVO(12,new RoleArgVO(109,1000,300,0,30,25,150,100,{
            "rate":50,
            "hurtPer":45,
            "spdPer":50,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(223,8000,500,250,30,25,150,60,null)));
         _loc2_.addEnemy(new WaveEnemyVO(12,new RoleArgVO(110,800,300,0,30,25,150,100,{
            "rate":200,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
      
      public function newReward(param1:Boolean) : Array
      {
         if(!param1)
         {
            return this.loseReward;
         }
         return this.winReward;
      }
      
      private function get winReward() : Array
      {
         var _loc6_:int = 0;
         var _loc1_:int = ConstData.INT1.v;
         var _loc2_:int = Math.random() * 100 + 1;
         if(_loc2_ <= 60)
         {
            _loc1_ = ConstData.INT2.v;
         }
         else if(_loc2_ <= 90)
         {
            _loc1_ = ConstData.INT3.v;
         }
         else
         {
            _loc1_ = ConstData.INT4.v;
         }
         var _loc3_:Array = [];
         var _loc4_:FubenVO = FubenConfig.instance().findFuben(105);
         var _loc5_:Array = _loc4_.drops.slice(1);
         var _loc7_:int = 0;
         while(_loc7_ < _loc1_)
         {
            _loc6_ = Math.random() * _loc5_.length;
            _loc3_[_loc3_.length] = _loc5_[_loc6_];
            _loc5_.splice(_loc6_,1);
            _loc7_++;
         }
         return _loc3_;
      }
      
      private function get loseReward() : Array
      {
         return [new BaseRewardVO(10000,15000)];
      }
   }
}

