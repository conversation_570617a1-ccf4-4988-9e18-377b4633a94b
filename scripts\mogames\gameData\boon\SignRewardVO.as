package mogames.gameData.boon
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import mogames.gameData.ServerProxy;
   import mogames.gameData.base.RewardProxy;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.good.vo.GameGoodVO;
   import mogames.gameData.vip.VipProxy;
   
   public class SignRewardVO
   {
      
      private var _index:Oint = new Oint();
      
      private var _flag:Oint = new Oint();
      
      private var _day:Oint = new Oint();
      
      private var _list:Array;
      
      public function SignRewardVO(param1:int, param2:int, param3:int, param4:Array)
      {
         super();
         MathUtil.saveINT(this._index,param1);
         MathUtil.saveINT(this._flag,param2);
         MathUtil.saveINT(this._day,param3);
         this._list = param4;
      }
      
      public function get index() : int
      {
         return MathUtil.loadINT(this._index);
      }
      
      public function get canGet() : Boolean
      {
         if(this.hasGet)
         {
            return false;
         }
         if(this.index == 4)
         {
            return SignProxy.instance().signTotal >= ServerProxy.instance().curMonthDay;
         }
         return SignProxy.instance().signTotal >= MathUtil.loadINT(this._day);
      }
      
      public function get hasGet() : Boolean
      {
         return FlagProxy.instance().openFlag.isComplete(this.flagID);
      }
      
      public function setGet() : void
      {
         FlagProxy.instance().openFlag.setValue(this.flagID);
      }
      
      public function monthRefresh() : void
      {
         FlagProxy.instance().openFlag.setValue(this.flagID,0);
      }
      
      private function get flagID() : int
      {
         return MathUtil.loadINT(this._flag);
      }
      
      private function get isDouble() : Boolean
      {
         return VipProxy.instance().hasFunc(105);
      }
      
      public function get rewards() : Array
      {
         return this._list;
      }
      
      public function get rewardList() : Array
      {
         var _loc2_:GameGoodVO = null;
         var _loc1_:Array = RewardProxy.instance().newGiftReward(this._list);
         if(!this.isDouble)
         {
            return _loc1_;
         }
         for each(_loc2_ in _loc1_)
         {
            _loc2_.amount *= 2;
         }
         return _loc1_;
      }
   }
}

