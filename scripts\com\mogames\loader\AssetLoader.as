package com.mogames.loader
{
   import flash.display.Loader;
   import flash.display.LoaderInfo;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.events.ProgressEvent;
   import flash.net.URLRequest;
   
   public class AssetLoader
   {
      
      private var _loader:Loader;
      
      private var _list:Array;
      
      private var _loadFunc:Function;
      
      private var _endFunc:Function;
      
      private var _resFunc:Function;
      
      public var cur:int;
      
      public var total:int;
      
      public var per:int;
      
      public function AssetLoader()
      {
         super();
      }
      
      public function load(param1:Array, param2:Function, param3:Function, param4:Function) : void
      {
         this._list = param1;
         this._resFunc = param2;
         this._loadFunc = param3;
         this._endFunc = param4;
         this.total = param1.length;
         this.cur = 0;
         this.per = 0;
         this.initLoad();
         this._loader.load(new URLRequest(this._list[this.cur]));
      }
      
      private function initLoad() : void
      {
         if(this._loader != null)
         {
            this.destroyLoader();
         }
         this._loader = new Loader();
         this._loader.contentLoaderInfo.addEventListener(ProgressEvent.PROGRESS,this.onProcess);
         this._loader.contentLoaderInfo.addEventListener(Event.COMPLETE,this.onComplete);
         this._loader.contentLoaderInfo.addEventListener(IOErrorEvent.IO_ERROR,this.onError);
      }
      
      private function saveRes(param1:LoaderInfo) : void
      {
         if(this._resFunc != null)
         {
            this._resFunc(param1);
         }
      }
      
      private function onError(param1:IOErrorEvent) : void
      {
         this.initLoad();
         this._loader.load(new URLRequest(this._list[this.cur]));
      }
      
      private function onProcess(param1:ProgressEvent) : void
      {
         this.per = param1.bytesLoaded / param1.bytesTotal * 100;
         if(this._loadFunc != null)
         {
            this._loadFunc();
         }
      }
      
      private function onComplete(param1:Event) : void
      {
         this.saveRes(this._loader.contentLoaderInfo);
         ++this.cur;
         if(this.cur >= this.total)
         {
            this.handlerLoaded();
         }
         else
         {
            this.initLoad();
            this._loader.load(new URLRequest(this._list[this.cur]));
         }
      }
      
      private function handlerLoaded() : void
      {
         this.cur = 0;
         this.destroyLoader();
         if(this._endFunc != null)
         {
            this._endFunc();
         }
      }
      
      private function destroyLoader() : void
      {
         if(!this._loader)
         {
            return;
         }
         this._loader.unloadAndStop();
         this._loader.contentLoaderInfo.removeEventListener(ProgressEvent.PROGRESS,this.onProcess);
         this._loader.contentLoaderInfo.removeEventListener(Event.COMPLETE,this.onComplete);
         this._loader.contentLoaderInfo.removeEventListener(IOErrorEvent.IO_ERROR,this.onError);
         this._loader = null;
      }
      
      public function get infor() : String
      {
         return this.cur + 1 + "/" + this.total;
      }
      
      public function destroy() : void
      {
         this.destroyLoader();
         this._endFunc = null;
         this._loadFunc = null;
         this._resFunc = null;
         this._list = null;
      }
   }
}

