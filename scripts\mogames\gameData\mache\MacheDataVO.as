package mogames.gameData.mache
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import file.MissionConfig;
   import mogames.gameData.mission.base.MissionConstVO;
   
   public class MacheDataVO
   {
      
      private var _mid:Oint = new Oint();
      
      private var _finish:Oint = new Oint();
      
      public function MacheDataVO()
      {
         super();
         MathUtil.saveINT(this._mid,0);
         MathUtil.saveINT(this._finish,0);
      }
      
      public function setNew(param1:int) : void
      {
         MathUtil.saveINT(this._mid,param1);
         MathUtil.saveINT(this._finish,0);
      }
      
      public function setFinish() : void
      {
         MathUtil.saveINT(this._finish,1);
      }
      
      public function get isFinish() : Boolean
      {
         return MathUtil.loadINT(this._finish) == 1;
      }
      
      public function get constVO() : MissionConstVO
      {
         return MissionConfig.instance().findConst(MathUtil.loadINT(this._mid));
      }
      
      public function get saveData() : Array
      {
         return [MathUtil.loadINT(this._mid),MathUtil.loadINT(this._finish)];
      }
      
      public function set loadData(param1:Array) : void
      {
         if(!param1)
         {
            return;
         }
         MathUtil.saveINT(this._mid,param1[0]);
         MathUtil.saveINT(this._finish,param1[1]);
      }
   }
}

