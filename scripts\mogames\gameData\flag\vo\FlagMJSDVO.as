package mogames.gameData.flag.vo
{
   import com.mogames.utils.MathUtil;
   import mogames.ConstData;
   import mogames.gameData.flag.base.FlagVO;
   import mogames.gameData.vip.VipProxy;
   
   public class FlagMJSDVO extends FlagVO
   {
      
      public function FlagMJSDVO(param1:int)
      {
         super(param1,2,false,true,true);
      }
      
      override public function get total() : int
      {
         var _loc1_:int = MathUtil.loadINT(_total);
         if(VipProxy.instance().hasFunc(120))
         {
            return _loc1_ + ConstData.INT1.v;
         }
         return _loc1_;
      }
   }
}

