package mogames.gameData.mission.extra
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ExtraWave2399
   {
      
      public function ExtraWave2399()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2399);
         _loc1_.limitBR = new WaveLimitVO(99999999,0.8,0.8);
         _loc1_.zhuBoss = new BossArgVO(783,51000000,128000,100000,80,80,300,90,new BossSkillData0(150,{
            "hurt":250000,
            "keepTime":3
         },5),1014,0);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(772,950000,105000,12000,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(772,950000,105000,12000,50,80,200,120,0)));
         _loc2_.addFu(new BossArgVO(783,8500000,80000,13000,50,80,150,80,new BossSkillData0(250,{
            "hurt":200000,
            "keepTime":3
         },3),1014,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(771,950000,105000,12000,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(771,950000,105000,12000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(772,950000,105000,12000,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(772,950000,105000,12000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(782,8500000,80000,13000,50,80,150,80,new BossSkillData1(10,{
            "hurt":200000,
            "keepTime":3
         },3),1007,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(771,950000,105000,12000,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(773,950000,105000,12000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(771,950000,105000,12000,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(773,950000,105000,12000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(781,8500000,80000,13000,50,80,150,80,new BossSkillData1(10,{"hurt":200000},3),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(771,950000,105000,12000,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(773,950000,105000,12000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(772,950000,105000,12000,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(773,950000,105000,12000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(781,8500000,80000,13000,50,80,150,80,new BossSkillData0(250,{"hurt":200000},3),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(771,950000,105000,12000,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(772,950000,105000,12000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(773,950000,105000,12000,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(773,950000,105000,12000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(782,8500000,80000,13000,50,80,150,80,new BossSkillData0(250,{"hurt":200000},3),1008,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

