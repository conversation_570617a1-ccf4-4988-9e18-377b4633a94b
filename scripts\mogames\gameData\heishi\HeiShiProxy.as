package mogames.gameData.heishi
{
   import file.HeiShiConfig;
   
   public class HeiShiProxy
   {
      
      private static var _instance:HeiShiProxy;
      
      private var _heishis:Array;
      
      public function HeiShiProxy()
      {
         var _loc3_:HeiShiBuyVO = null;
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this._heishis = [];
         var _loc1_:int = 0;
         var _loc2_:int = int(HeiShiConfig.instance().list.length);
         while(_loc1_ < _loc2_)
         {
            _loc3_ = new HeiShiBuyVO(_loc1_);
            this._heishis[_loc1_] = _loc3_;
            _loc1_++;
         }
      }
      
      public static function instance() : HeiShiProxy
      {
         if(!_instance)
         {
            _instance = new HeiShiProxy();
         }
         return _instance;
      }
      
      public function startNew() : void
      {
         var _loc1_:HeiShiBuyVO = null;
         for each(_loc1_ in this._heishis)
         {
            _loc1_.startNew();
         }
      }
      
      public function set loadData(param1:Array) : void
      {
         var _loc2_:HeiShiBuyVO = null;
         var _loc3_:Object = null;
         this.startNew();
         if(!param1)
         {
            return;
         }
         for each(_loc3_ in param1)
         {
            _loc2_ = this.findVO(_loc3_.id);
            if(_loc2_)
            {
               _loc2_.loadData = _loc3_;
            }
         }
      }
      
      public function get saveData() : Array
      {
         var _loc2_:HeiShiBuyVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._heishis)
         {
            _loc1_[_loc1_.length] = _loc2_.saveData;
         }
         return _loc1_;
      }
      
      public function dailyRefresh() : void
      {
         var _loc1_:HeiShiBuyVO = null;
         for each(_loc1_ in this._heishis)
         {
            _loc1_.startNew();
         }
      }
      
      public function findVO(param1:int) : HeiShiBuyVO
      {
         var _loc2_:HeiShiBuyVO = null;
         for each(_loc2_ in this._heishis)
         {
            if(_loc2_.index == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function openNext(param1:int) : void
      {
         if(param1 >= this._heishis.length)
         {
            return;
         }
         this._heishis[param1].handlerOpen();
      }
      
      public function get heishis() : Array
      {
         return this._heishis;
      }
   }
}

