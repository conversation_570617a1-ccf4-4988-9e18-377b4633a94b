package mogames.gameData.main.vo
{
   public class WalkerVO
   {
      
      private var _id:int;
      
      private var _width:int;
      
      private var _height:int;
      
      private var _words:Array;
      
      public function WalkerVO(param1:int, param2:Array)
      {
         super();
         this._id = param1;
         this._words = param2;
      }
      
      public function get width() : int
      {
         return this._width;
      }
      
      public function get height() : int
      {
         return this._height;
      }
      
      public function get randomWord() : String
      {
         return this._words[int(Math.random() * this._words.length)];
      }
      
      public function get id() : int
      {
         return this._id;
      }
   }
}

