package mogames.gameData.good
{
   import mogames.gameData.good.bag.ArmyBagVO;
   import mogames.gameData.good.bag.BeastBagVO;
   import mogames.gameData.good.bag.ChaoFengBagVO;
   import mogames.gameData.good.bag.LGJBagVO;
   import mogames.gameData.good.bag.ShunYiBagVO;
   import mogames.gameData.good.bag.ZanTingBagVO;
   import mogames.gameData.good.bag.base.GameBagVO;
   import mogames.gameData.good.bag.base.SkillBagVO;
   import mogames.gameData.good.base.ConstATTVO;
   import mogames.gameData.good.base.ConstBagVO;
   import mogames.gameData.good.base.ConstEquipVO;
   import mogames.gameData.good.base.ConstGoodVO;
   import mogames.gameData.good.equip.GameEquipVO;
   import mogames.gameData.good.equip.GameZhiBaoVO;
   import mogames.gameData.good.equip.HorseEquipVO;
   import mogames.gameData.good.equip.SkillEquipVO;
   import mogames.gameData.good.vo.GameATTVO;
   import mogames.gameData.good.vo.GameGoodVO;
   import mogames.gameData.good.zhibao.GameGuiGuZiVO;
   import mogames.gameData.good.zhibao.GameKMDVO;
   import mogames.gameData.good.zhibao.GameKTSFVO;
   import mogames.gameData.good.zhibao.GameQXBDVO;
   import mogames.gameData.good.zhibao.GameQXSVO;
   import mogames.gameData.good.zhibao.GameRWDVO;
   import mogames.gameData.good.zhibao.GameSSBZVO;
   import mogames.gameData.good.zhibao.GameTPTSVO;
   import mogames.gameData.good.zhibao.GameYuXiVO;
   import mogames.gameData.good.zhibao.GameZGCVO;
   import mogames.gameData.good.zhibao.Game_Feng_Lei_Ling_VO;
   
   public class GoodFactory
   {
      
      public function GoodFactory()
      {
         super();
      }
      
      public static function newEquipVO(param1:ConstEquipVO, param2:int) : GameEquipVO
      {
         var _loc3_:GameEquipVO = null;
         if((param1.bodyType == 0 || param1.bodyType == 3) && param1.hasSkill)
         {
            _loc3_ = new SkillEquipVO(param1);
         }
         else if(param1.bodyType == 5)
         {
            _loc3_ = new HorseEquipVO(param1);
         }
         else if(param1.bodyType == 6)
         {
            _loc3_ = newZhiBaoVO(param1);
         }
         else
         {
            _loc3_ = new GameEquipVO(param1);
         }
         _loc3_.createEquip(param2);
         return _loc3_;
      }
      
      public static function newZhiBaoVO(param1:ConstEquipVO) : GameZhiBaoVO
      {
         switch(param1.id)
         {
            case 30001:
            case 30101:
            case 30201:
            case 30352:
            case 30452:
            case 30052:
            case 30031:
            case 30097:
            case 30397:
            case 30069:
            case 31002:
            case 31102:
            case 31021:
            case 31121:
            case 31012:
            case 31019:
            case 31029:
            case 31042:
            case 30047:
            case 31015:
            case 31032:
            case 31037:
            case 31054:
            case 31057:
            case 31059:
            case 31068:
            case 31203:
            case 31206:
            case 31222:
            case 31234:
            case 31254:
               return new GameYuXiVO(param1);
            case 30003:
            case 30103:
            case 30203:
            case 30056:
            case 30356:
            case 30065:
            case 30090:
            case 30390:
            case 30095:
            case 30395:
            case 31001:
            case 31101:
            case 30070:
            case 30370:
            case 30375:
            case 31016:
            case 31027:
            case 31044:
            case 31039:
            case 31053:
            case 31048:
            case 31056:
            case 31074:
            case 31066:
            case 31201:
            case 31207:
            case 31218:
            case 31224:
               return new GameRWDVO(param1);
            case 30005:
            case 30105:
            case 30054:
            case 30080:
            case 30205:
            case 30092:
            case 30392:
            case 30083:
            case 30086:
            case 30386:
            case 31003:
            case 31103:
            case 31024:
            case 31124:
            case 31028:
            case 31036:
            case 31033:
            case 31043:
            case 31045:
            case 31060:
            case 30376:
            case 31078:
            case 31069:
            case 31190:
            case 31212:
            case 31204:
            case 31216:
            case 31226:
            case 31183:
            case 31236:
            case 31233:
            case 31257:
            case 31253:
            case 31262:
            case 31268:
            case 31274:
            case 31275:
            case 31276:
            case 32001:
            case 31286:
            case 31282:
            case 31094:
            case 31095:
            case 31284:
            case 31612:
               return new GameTPTSVO(param1);
            case 30006:
            case 30106:
            case 30053:
            case 30353:
            case 30059:
            case 30062:
            case 30074:
            case 30374:
            case 30078:
            case 30362:
            case 30462:
            case 30088:
            case 30388:
            case 30082:
            case 31011:
            case 31022:
            case 31122:
            case 31013:
            case 31026:
            case 31014:
            case 31030:
            case 31130:
            case 31131:
            case 31035:
            case 31132:
            case 31038:
            case 31046:
            case 31052:
            case 31072:
            case 31096:
            case 31097:
            case 31055:
            case 31058:
            case 31092:
            case 31067:
            case 31202:
            case 31191:
            case 31205:
            case 31182:
            case 31205:
            case 31221:
            case 31193:
            case 31198:
            case 31199:
            case 31231:
            case 31238:
            case 31256:
            case 31252:
            case 31271:
            case 31272:
            case 31273:
            case 31261:
            case 31267:
            case 31301:
            case 31311:
            case 31321:
            case 31287:
            case 31291:
            case 31297:
            case 31294:
            case 31303:
            case 31313:
            case 31323:
            case 31365:
            case 31362:
            case 31368:
            case 31304:
            case 31314:
            case 31324:
            case 31332:
            case 31342:
            case 31352:
            case 31372:
            case 31334:
            case 31344:
            case 31354:
            case 31388:
            case 31377:
            case 31382:
            case 31385:
            case 31395:
            case 31392:
            case 31397:
            case 31501:
            case 31511:
            case 31521:
            case 31456:
            case 31453:
            case 31458:
            case 31404:
            case 31414:
            case 31424:
            case 31466:
            case 31468:
            case 31701:
            case 31711:
            case 31721:
            case 31476:
            case 31504:
            case 31514:
            case 31524:
            case 31486:
            case 31483:
            case 31531:
            case 31541:
            case 31551:
            case 31495:
            case 31493:
            case 31704:
            case 31714:
            case 31724:
            case 31651:
            case 31603:
            case 31605:
            case 31608:
            case 31616:
            case 31618:
            case 31534:
            case 31544:
            case 31554:
            case 31626:
            case 31623:
            case 31803:
            case 31813:
            case 31823:
            case 31635:
            case 31633:
            case 31638:
            case 31562:
            case 31572:
            case 31582:
            case 31646:
            case 31642:
            case 31831:
            case 31841:
            case 31851:
               return new GameQXSVO(param1);
            case 31099:
            case 31093:
            case 31194:
            case 32101:
            case 32102:
            case 32103:
            case 32111:
            case 32112:
            case 32113:
            case 32121:
            case 32122:
            case 32123:
               return new Game_Feng_Lei_Ling_VO(param1);
            case 30112:
            case 30212:
            case 30081:
            case 30076:
            case 30068:
            case 30368:
            case 30087:
            case 30387:
            case 30089:
            case 31007:
            case 31040:
            case 31091:
            case 31076:
            case 31079:
            case 31080:
            case 31211:
            case 31215:
            case 31192:
            case 31196:
            case 31197:
            case 31208:
            case 31225:
            case 31223:
            case 30100:
            case 31235:
            case 31232:
            case 31240:
            case 31241:
            case 31242:
            case 31251:
            case 31258:
            case 31180:
            case 31185:
            case 31187:
            case 31266:
            case 31263:
            case 31269:
            case 31281:
            case 31288:
            case 31293:
            case 31299:
            case 31331:
            case 31341:
            case 31351:
            case 31366:
            case 31363:
            case 31373:
            case 31376:
            case 31378:
            case 31381:
            case 31401:
            case 31411:
            case 31421:
            case 31384:
            case 31387:
            case 32002:
            case 31396:
            case 31393:
            case 31398:
            case 31403:
            case 31413:
            case 31423:
            case 31455:
            case 31452:
            case 31454:
            case 31502:
            case 31512:
            case 31522:
            case 31465:
            case 31462:
            case 31464:
            case 31475:
            case 31477:
            case 31474:
            case 31485:
            case 31488:
            case 31482:
            case 31703:
            case 31713:
            case 31723:
            case 31496:
            case 31498:
            case 31532:
            case 31542:
            case 31552:
            case 31602:
            case 31607:
            case 31801:
            case 31811:
            case 31821:
            case 31613:
            case 31615:
            case 31625:
            case 31561:
            case 31571:
            case 31581:
            case 31628:
            case 31636:
            case 31645:
            case 31647:
            case 31563:
            case 31573:
            case 31583:
               return new GameQXBDVO(param1);
            case 30013:
            case 30113:
            case 30213:
               return new GameKMDVO(param1);
            case 30016:
            case 30116:
            case 30216:
            case 30057:
            case 30357:
            case 31005:
            case 31025:
            case 31041:
            case 31047:
            case 31071:
            case 31077:
            case 31213:
            case 31228:
            case 31237:
            case 31296:
            case 30249:
            case 30358:
            case 30359:
            case 31364:
            case 31333:
            case 31343:
            case 31353:
            case 31375:
            case 31386:
            case 31402:
            case 31412:
            case 31422:
            case 31457:
            case 31467:
            case 31503:
            case 31513:
            case 31523:
            case 31472:
            case 31702:
            case 31712:
            case 31722:
            case 31478:
            case 31487:
            case 31491:
            case 31497:
            case 31606:
            case 31533:
            case 31543:
            case 31553:
            case 31617:
            case 31802:
            case 31812:
            case 31822:
            case 31621:
            case 31627:
            case 31632:
            case 31637:
            case 31804:
            case 31814:
            case 31824:
            case 31642:
            case 31648:
               return new GameGuiGuZiVO(param1);
            case 30018:
            case 30118:
            case 30218:
            case 30058:
            case 30075:
            case 30084:
            case 30085:
            case 30385:
            case 31006:
            case 31031:
            case 31009:
            case 31020:
            case 31090:
            case 31073:
            case 31214:
            case 31217:
            case 31227:
            case 31181:
            case 31184:
            case 31186:
            case 31259:
            case 31264:
            case 31283:
            case 31289:
            case 31304:
            case 31302:
            case 31312:
            case 31312:
            case 31292:
            case 31298:
            case 31361:
            case 31367:
            case 31371:
            case 31374:
            case 31383:
            case 31391:
            case 31394:
            case 31451:
            case 31461:
            case 31463:
            case 31471:
            case 31481:
            case 31484:
            case 31473:
            case 31492:
            case 31494:
            case 31601:
            case 31604:
            case 31611:
            case 31614:
            case 31622:
            case 31624:
            case 31631:
            case 31634:
            case 31641:
            case 31644:
               return new GameZGCVO(param1);
            case 30025:
            case 30051:
            case 30351:
            case 30451:
            case 30071:
            case 30371:
               return new GameSSBZVO(param1);
            case 30026:
            case 30126:
            case 30226:
            case 30073:
            case 30373:
            case 30067:
            case 30367:
               return new GameKTSFVO(param1);
            default:
               return new GameZhiBaoVO(param1);
         }
      }
      
      public static function newBagVO(param1:ConstBagVO) : GameBagVO
      {
         switch(param1.id)
         {
            case 11001:
               return new SkillBagVO(param1,2001,{"hurt":40});
            case 11002:
               return new SkillBagVO(param1,2002,{"curePer":20});
            case 11003:
               return new SkillBagVO(param1,2002,{"curePer":30});
            case 11004:
               return new SkillBagVO(param1,2002,{"curePer":40});
            case 11005:
               return new SkillBagVO(param1,2003,{"curePer":15});
            case 11006:
               return new SkillBagVO(param1,2003,{"curePer":25});
            case 11007:
               return new SkillBagVO(param1,2003,{"curePer":35});
            case 11008:
               return new SkillBagVO(param1,2004,{
                  "spdPer":15,
                  "keepTime":5
               });
            case 11009:
               return new SkillBagVO(param1,2004,{
                  "spdPer":25,
                  "keepTime":8
               });
            case 11010:
               return new SkillBagVO(param1,2004,{
                  "spdPer":35,
                  "keepTime":12
               });
            case 11011:
               return new SkillBagVO(param1,2010,{
                  "atkPer":10,
                  "keepTime":15
               });
            case 11012:
               return new SkillBagVO(param1,2010,{
                  "atkPer":15,
                  "keepTime":15
               });
            case 11013:
               return new SkillBagVO(param1,2010,{
                  "atkPer":20,
                  "keepTime":15
               });
            case 11071:
               return new ChaoFengBagVO(param1);
            case 11072:
               return new ZanTingBagVO(param1);
            case 11073:
               return new ShunYiBagVO(param1);
            case 11101:
               return new BeastBagVO(param1,1);
            case 11102:
               return new BeastBagVO(param1,2);
            case 11103:
               return new BeastBagVO(param1,3);
            case 11104:
               return new ArmyBagVO(param1,5);
            case 11105:
               return new ArmyBagVO(param1,10);
            case 11106:
               return new ArmyBagVO(param1,15);
            case 11151:
               return new SkillBagVO(param1,2005,{"keepTime":5});
            case 11152:
               return new SkillBagVO(param1,2005,{"keepTime":8});
            case 11153:
               return new SkillBagVO(param1,2005,{"keepTime":11});
            case 11154:
               return new SkillBagVO(param1,2006,{
                  "hurt":80,
                  "keepTime":2
               });
            case 11155:
               return new SkillBagVO(param1,2006,{
                  "hurt":240,
                  "keepTime":4
               });
            case 11156:
               return new SkillBagVO(param1,2006,{
                  "hurt":550,
                  "keepTime":6
               });
            case 11157:
               return new SkillBagVO(param1,2007,{
                  "hp":10000,
                  "atk":400,
                  "keepTime":25
               });
            case 11158:
               return new SkillBagVO(param1,2007,{
                  "hp":15000,
                  "atk":700,
                  "keepTime":35
               });
            case 11159:
               return new SkillBagVO(param1,2007,{
                  "hp":20000,
                  "atk":1300,
                  "keepTime":45
               });
            case 11201:
               return new SkillBagVO(param1,2008,{"hurtPer":15});
            case 11202:
               return new SkillBagVO(param1,2008,{"hurtPer":20});
            case 11203:
               return new SkillBagVO(param1,2008,{"hurtPer":25});
            case 11204:
               return new SkillBagVO(param1,2009,{
                  "atk":410,
                  "keepTime":20
               });
            case 11205:
               return new SkillBagVO(param1,2009,{
                  "atk":720,
                  "keepTime":30
               });
            case 11206:
               return new SkillBagVO(param1,2009,{
                  "atk":1040,
                  "keepTime":40
               });
            case 11301:
               return new SkillBagVO(param1,2011,{
                  "hurt":330,
                  "roleNum":15
               });
            case 11302:
               return new SkillBagVO(param1,2011,{
                  "hurt":690,
                  "roleNum":15
               });
            case 11303:
               return new SkillBagVO(param1,2011,{
                  "hurt":980,
                  "roleNum":15
               });
            case 11304:
               return new SkillBagVO(param1,2012,{
                  "hurtPer":10,
                  "keepTime":6,
                  "roleNum":15
               });
            case 11305:
               return new SkillBagVO(param1,2012,{
                  "hurtPer":10,
                  "keepTime":7,
                  "roleNum":15
               });
            case 11306:
               return new SkillBagVO(param1,2012,{
                  "hurtPer":10,
                  "keepTime":8,
                  "roleNum":15
               });
            case 11401:
               return new LGJBagVO(param1);
            case 11501:
               return new SkillBagVO(param1,2014,{
                  "defValue":50,
                  "keepTime":10
               });
            case 11502:
               return new SkillBagVO(param1,2014,{
                  "defValue":150,
                  "keepTime":10
               });
            case 11503:
               return new SkillBagVO(param1,2014,{
                  "defValue":300,
                  "keepTime":10
               });
            case 11601:
               return new SkillBagVO(param1,2015,{"curePer":15});
            case 11602:
               return new SkillBagVO(param1,2015,{"curePer":25});
            default:
               return new GameBagVO(param1);
         }
      }
      
      public static function newGoodVO(param1:ConstGoodVO) : GameGoodVO
      {
         if(param1 is ConstATTVO)
         {
            return new GameATTVO(param1 as ConstATTVO);
         }
         return new GameGoodVO(param1);
      }
   }
}

