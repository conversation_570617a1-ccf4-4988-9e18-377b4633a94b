package mogames.gameData.mission.data
{
   import com.mogames.data.Oint;
   import com.mogames.event.GameSignal;
   import com.mogames.event.SignalManager;
   import com.mogames.utils.MathUtil;
   import mogames.gameData.mission.BattleProxy;
   import mogames.gameData.role.HeroProxy;
   import mogames.gameData.role.battle.BattleRoleVO;
   import mogames.gameData.role.hero.HeroChooseVO;
   import mogames.gameData.role.hero.HeroGameVO;
   import mogames.gameRole.base.IRole;
   
   public class BattleHeroData
   {
      
      public var chooseHeros:Array;
      
      public var battleHeros:Array;
      
      private var _huanNum:Oint = new Oint();
      
      public function BattleHeroData()
      {
         super();
         this._huanNum = new Oint();
      }
      
      public function init(param1:int) : void
      {
         var _loc3_:HeroGameVO = null;
         var _loc4_:HeroChooseVO = null;
         MathUtil.saveINT(this._huanNum,1);
         this.battleHeros = [];
         this.chooseHeros = [];
         var _loc2_:Array = HeroProxy.instance().ownerHeros;
         for each(_loc3_ in _loc2_)
         {
            _loc4_ = new HeroChooseVO(_loc3_);
            this.chooseHeros[this.chooseHeros.length] = _loc4_;
         }
         SignalManager.signalRole.addListener(this.listenHero);
         this.initChoose(param1);
      }
      
      private function initChoose(param1:int) : void
      {
         var _loc2_:HeroChooseVO = null;
         if(param1 == 0)
         {
            return;
         }
         for each(_loc2_ in this.chooseHeros)
         {
            if(this.checkRefuse(_loc2_.heroVO))
            {
               _loc2_.setStates(3);
            }
         }
      }
      
      public function addHuanNum() : void
      {
         MathUtil.saveINT(this._huanNum,this.huanNum + 1);
      }
      
      public function get huanNum() : int
      {
         return MathUtil.loadINT(this._huanNum);
      }
      
      private function listenHero(param1:Object) : void
      {
         if(param1.signal == GameSignal.HERO_DIE)
         {
            this.onHeroDie((param1.role as IRole).roleVO);
         }
         else if(param1.signal == GameSignal.ROLE_ADD && param1.type == "add_friend_hero")
         {
            this.onHeroAdd((param1.role as IRole).roleVO);
         }
      }
      
      private function onHeroAdd(param1:BattleRoleVO) : void
      {
         if(!param1.isHero)
         {
            return;
         }
         var _loc2_:HeroChooseVO = this.findChoose(param1.roleID);
         if(!_loc2_)
         {
            return;
         }
         _loc2_.setStates(1);
         if(this.battleHeros.indexOf(_loc2_) != -1)
         {
            return;
         }
         this.battleHeros[this.battleHeros.length] = _loc2_;
         if(BattleProxy.instance().battleType == 2)
         {
            _loc2_.heroVO.status = 1;
         }
      }
      
      private function onHeroDie(param1:BattleRoleVO) : void
      {
         if(!param1.isHero)
         {
            return;
         }
         var _loc2_:HeroChooseVO = this.findChoose(param1.roleID);
         if(!_loc2_)
         {
            return;
         }
         _loc2_.setStates(2);
      }
      
      public function restoreSecret() : void
      {
         var _loc1_:HeroChooseVO = null;
         if(BattleProxy.instance().battleType != 2)
         {
            return;
         }
         for each(_loc1_ in this.battleHeros)
         {
            if(_loc1_.states != 2)
            {
               _loc1_.heroVO.status = 0;
            }
         }
      }
      
      public function get heroAllDead() : Boolean
      {
         var _loc1_:HeroChooseVO = null;
         for each(_loc1_ in this.battleHeros)
         {
            if(_loc1_.states != 2)
            {
               return false;
            }
         }
         return true;
      }
      
      public function get heroDead() : Boolean
      {
         var _loc1_:HeroChooseVO = null;
         for each(_loc1_ in this.battleHeros)
         {
            if(_loc1_.states == 2)
            {
               return true;
            }
         }
         return false;
      }
      
      public function get hasHero() : Boolean
      {
         return this.battleHeros.length > 0;
      }
      
      public function get hasAliveHero() : Boolean
      {
         var _loc1_:HeroChooseVO = null;
         for each(_loc1_ in this.battleHeros)
         {
            if(_loc1_.states != 2)
            {
               return true;
            }
         }
         return false;
      }
      
      public function get curBR() : int
      {
         var _loc1_:int = 0;
         var _loc2_:HeroChooseVO = null;
         for each(_loc2_ in this.battleHeros)
         {
            if(!_loc2_.isFail)
            {
               _loc1_ += _loc2_.heroVO.battleRate;
            }
         }
         return _loc1_;
      }
      
      public function findChoose(param1:int) : HeroChooseVO
      {
         var _loc2_:HeroChooseVO = null;
         for each(_loc2_ in this.chooseHeros)
         {
            if(_loc2_.heroVO.heroID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      private function checkRefuse(param1:HeroGameVO) : Boolean
      {
         if(param1.curLoyal <= 50)
         {
            return MathUtil.checkOdds(300);
         }
         if(MathUtil.checkInRate(param1.curLoyal,51,69))
         {
            return MathUtil.checkOdds(200);
         }
         if(MathUtil.checkInRate(param1.curLoyal,70,79))
         {
            return MathUtil.checkOdds(100);
         }
         if(MathUtil.checkInRate(param1.curLoyal,80,89))
         {
            return MathUtil.checkOdds(30);
         }
         return false;
      }
      
      public function clean() : void
      {
         this.chooseHeros.length = 0;
         this.chooseHeros = null;
         this.battleHeros.length = 0;
         this.battleHeros = null;
      }
   }
}

