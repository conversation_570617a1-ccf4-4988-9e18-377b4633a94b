package mogames.gameData.mission.extra
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ExtraWave2342
   {
      
      public function ExtraWave2342()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2342);
         _loc1_.limitBR = new WaveLimitVO(99999999,0.8,0.8);
         _loc1_.zhuBoss = new BossArgVO(841,9000000,70000,20000,80,80,300,90,new BossSkillData0(200,{
            "hurt":195000,
            "roleNum":5
         },7),1016,0);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(772,440000,34000,2000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(772,440000,34000,2000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(772,440000,34000,2000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(771,440000,34000,2000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(761,2700000,32000,3000,50,80,150,80,new BossSkillData1(10,{"hurt":90000},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(772,440000,34000,2000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(761,2700000,32000,3000,50,80,150,80,new BossSkillData1(12,{"hurt":90000},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(772,440000,34000,2000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(771,440000,34000,2000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(772,440000,34000,2000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(771,440000,34000,2000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(761,2700000,32000,3000,50,80,150,80,new BossSkillData1(11,{"hurt":90000},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(772,440000,34000,2000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(771,440000,34000,2000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(772,440000,34000,2000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(761,2700000,32000,3000,50,80,150,80,new BossSkillData1(11,{"hurt":90000},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(772,440000,34000,2000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(771,440000,34000,2000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(772,440000,34000,2000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(771,440000,34000,2000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(772,440000,34000,2000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(761,2700000,32000,3000,50,80,150,80,new BossSkillData1(12,{"hurt":90000},2),1011,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

