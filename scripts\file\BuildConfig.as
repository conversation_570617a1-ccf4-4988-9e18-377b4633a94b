package file
{
   import mogames.gameData.base.vo.NeedVO;
   import mogames.gameData.build.vo.ConstBuildVO;
   import mogames.gameData.build.vo.GameBuildVO;
   
   public class BuildConfig
   {
      
      private static var _instance:BuildConfig;
      
      private var _list:Vector.<ConstBuildVO>;
      
      public function BuildConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : BuildConfig
      {
         if(!_instance)
         {
            _instance = new BuildConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = new Vector.<ConstBuildVO>();
         this._list[this._list.length] = new ConstBuildVO(100,[[new NeedVO(10003,88),new NeedVO(10004,88),new NeedVO(10005,88),new NeedVO(10000,50000),new NeedVO(10001,250)],[new NeedVO(10003,400),new NeedVO(10004,300),new NeedVO(10005,300),new NeedVO(10000,100000),new NeedVO(10001,500)],[new NeedVO(10003,1000),new NeedVO(10004,600),new NeedVO(10005,600),new NeedVO(10000,200000),new NeedVO(10001,1100)]],"酒馆","在酒馆中可以招募到武将，酒馆等级越高，出现高星级武将几率越高。");
         this._list[this._list.length] = new ConstBuildVO(101,[[new NeedVO(10003,100),new NeedVO(10004,100),new NeedVO(10005,100),new NeedVO(10000,50000),new NeedVO(10001,300)],[new NeedVO(10003,500),new NeedVO(10004,400),new NeedVO(10005,400),new NeedVO(10000,100000),new NeedVO(10001,600)],[new NeedVO(10003,1000),new NeedVO(10004,800),new NeedVO(10005,800),new NeedVO(10000,200000),new NeedVO(10001,1200)]],"地牢","用于关押关卡中的俘虏，提升地牢等级可以解锁牢房。");
         this._list[this._list.length] = new ConstBuildVO(102,[[new NeedVO(10003,100),new NeedVO(10004,50),new NeedVO(10005,50),new NeedVO(10000,20000),new NeedVO(10001,200)],[new NeedVO(10003,300),new NeedVO(10004,100),new NeedVO(10005,100),new NeedVO(10000,30000),new NeedVO(10001,400)],[new NeedVO(10003,600),new NeedVO(10004,150),new NeedVO(10005,150),new NeedVO(10000,40000),new NeedVO(10001,600)]],"招魂幡","用于复活游戏中阵亡的武将，提升招魂幡等级可以复活更高星级的武将。");
      }
      
      public function newBuildList() : Array
      {
         var _loc2_:ConstBuildVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._list)
         {
            _loc1_[_loc1_.length] = new GameBuildVO(_loc2_);
         }
         return _loc1_;
      }
   }
}

