package mogames.gameData.mission.base
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import mogames.gameData.require.ReqFactory;
   import mogames.gameData.require.base.BaseReq;
   import mogames.gameData.require.base.ReqData;
   
   public class MissionStarVO
   {
      
      private var _mid:Oint = new Oint();
      
      private var _reqList:Array;
      
      public function MissionStarVO(param1:int, param2:Array)
      {
         super();
         MathUtil.saveINT(this._mid,param1);
         this._reqList = param2;
      }
      
      public function get mid() : int
      {
         return MathUtil.loadINT(this._mid);
      }
      
      public function get reqList() : Array
      {
         return this._reqList;
      }
      
      public function get reqInfor() : String
      {
         var _loc2_:BaseReq = null;
         var _loc3_:ReqData = null;
         var _loc1_:Array = [];
         for each(_loc3_ in this._reqList)
         {
            _loc2_ = ReqFactory.newReq(_loc3_.rid);
            _loc2_.baseArg = _loc3_.arg;
            _loc1_[_loc1_.length] = _loc2_.tipInfor;
            _loc2_.destroy();
         }
         return _loc1_.join("<br>");
      }
   }
}

