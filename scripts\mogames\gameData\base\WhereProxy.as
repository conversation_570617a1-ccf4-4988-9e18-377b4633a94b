package mogames.gameData.base
{
   import file.HeroConfig;
   import file.RoleConfig;
   import mogames.gameData.role.DealProxy;
   import mogames.gameData.role.HeroProxy;
   import mogames.gameData.role.hero.HeroDealVO;
   import mogames.gameData.role.hero.HeroGameVO;
   import mogames.gameData.role.hero.HeroInfoVO;
   import mogames.gameData.role.hero.HeroOtherVO;
   
   public class WhereProxy
   {
      
      private static var _instance:WhereProxy;
      
      public function WhereProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
      }
      
      public static function instance() : WhereProxy
      {
         if(!_instance)
         {
            _instance = new WhereProxy();
         }
         return _instance;
      }
      
      public function findWhere(param1:int) : Array
      {
         var _loc3_:HeroDealVO = null;
         var _loc4_:HeroOtherVO = null;
         var _loc2_:Array = [];
         for each(_loc3_ in DealProxy.instance().dealHeros)
         {
            if(_loc3_.where == param1)
            {
               if(!(_loc2_.indexOf(_loc3_.heroID) != -1 || HeroProxy.instance().hasHero(_loc3_.heroID)))
               {
                  _loc2_[_loc2_.length] = _loc3_.heroID;
               }
            }
         }
         for each(_loc4_ in HeroConfig.instance().otherList)
         {
            if(DealProxy.instance().findDeal(_loc4_.heroID) == null)
            {
               if(!HeroProxy.instance().hasHero(_loc4_.heroID))
               {
                  if(_loc4_.where == param1)
                  {
                     if(_loc2_.indexOf(_loc4_.heroID) == -1)
                     {
                        _loc2_[_loc2_.length] = _loc4_.heroID;
                     }
                  }
               }
            }
         }
         return _loc2_;
      }
      
      public function findHero(param1:int) : HeroGameVO
      {
         var _loc2_:HeroGameVO = new HeroGameVO(param1);
         var _loc3_:HeroDealVO = DealProxy.instance().findDeal(param1);
         if(_loc3_)
         {
            _loc2_.setLevel(_loc3_.heroLv);
            _loc2_.mainSkill.setLevel(_loc3_.skillv);
         }
         return _loc2_;
      }
      
      public function checkDeadQuality(param1:int) : Boolean
      {
         var _loc3_:HeroInfoVO = null;
         var _loc4_:int = 0;
         var _loc2_:Array = this.findWhere(0);
         if(_loc2_.length <= 0)
         {
            return false;
         }
         for each(_loc4_ in _loc2_)
         {
            _loc3_ = RoleConfig.instance().findInfo(_loc4_) as HeroInfoVO;
            if(_loc3_.quality == param1)
            {
               return true;
            }
         }
         return false;
      }
   }
}

