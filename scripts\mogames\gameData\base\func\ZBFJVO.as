package mogames.gameData.base.func
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import mogames.gameData.base.RewardProxy;
   import mogames.gameData.game.LackVO;
   import mogames.gameData.master.MasterProxy;
   
   public class ZBFJVO
   {
      
      private var _level:Oint = new Oint();
      
      private var _gold:Oint = new Oint();
      
      private var _yin:Oint = new Oint();
      
      private var _rewards:Array;
      
      public function ZBFJVO(param1:int, param2:int, param3:int, param4:Array)
      {
         super();
         MathUtil.saveINT(this._level,param1);
         MathUtil.saveINT(this._gold,param2);
         MathUtil.saveINT(this._yin,param3);
         this._rewards = param4;
      }
      
      public function handlerFenJie() : void
      {
         MasterProxy.instance().changeValue(10000,-this.gold);
         MasterProxy.instance().changeValue(10006,-this.yin);
         RewardProxy.instance().addGiftReward(this._rewards);
      }
      
      public function checkLack() : LackVO
      {
         var _loc1_:LackVO = null;
         _loc1_ = MasterProxy.instance().checkValue(10000,this.gold);
         if(_loc1_ != null)
         {
            return _loc1_;
         }
         _loc1_ = MasterProxy.instance().checkValue(10006,this.yin);
         if(_loc1_ != null)
         {
            return _loc1_;
         }
         return null;
      }
      
      public function get level() : int
      {
         return MathUtil.loadINT(this._level);
      }
      
      public function get gold() : int
      {
         return MathUtil.loadINT(this._gold);
      }
      
      public function get yin() : int
      {
         return MathUtil.loadINT(this._yin);
      }
      
      public function get rewards() : Array
      {
         return this._rewards;
      }
   }
}

