package mogames.gameData.base.vo
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import file.GoodConfig;
   import mogames.gameData.good.equip.GamePetEquipVO;
   import mogames.gameData.good.vo.GameGoodVO;
   
   public class PEquipRewardVO extends BaseRewardVO
   {
      
      protected var _equipVO:GamePetEquipVO;
      
      protected var _hole:Oint = new Oint();
      
      public function PEquipRewardVO(param1:int, param2:int)
      {
         MathUtil.saveINT(this._hole,param2);
         super(param1,1);
      }
      
      override protected function createGood(param1:int, param2:int) : void
      {
         _goodVO = GoodConfig.instance().newPetEquip(param1,this.curHole);
         this._equipVO = _goodVO as GamePetEquipVO;
      }
      
      override public function newGood(param1:int = 1) : GameGoodVO
      {
         return GoodConfig.instance().newPetEquip(constGood.id,this.curHole);
      }
      
      override public function get tipID() : int
      {
         if(this.curHole != 0)
         {
            return 115;
         }
         return 114;
      }
      
      override public function get tipGood() : *
      {
         if(this.curHole != 0)
         {
            return _goodVO;
         }
         return this._equipVO.constEquip;
      }
      
      private function get curHole() : int
      {
         return MathUtil.loadINT(this._hole);
      }
   }
}

