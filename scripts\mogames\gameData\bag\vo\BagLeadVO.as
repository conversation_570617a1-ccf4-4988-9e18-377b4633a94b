package mogames.gameData.bag.vo
{
   import mogames.gameData.bag.base.BagLockVO;
   import mogames.gameData.vip.VipProxy;
   
   public class BagLeadVO extends BagLockVO
   {
      
      public function BagLeadVO(param1:int, param2:String = "")
      {
         super(param1,param2);
      }
      
      override public function get isOpen() : Boolean
      {
         return VipProxy.instance().hasFunc(111);
      }
   }
}

