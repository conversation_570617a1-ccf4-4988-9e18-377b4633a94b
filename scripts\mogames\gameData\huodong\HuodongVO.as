package mogames.gameData.huodong
{
   import com.mogames.data.TimeVO;
   import com.mogames.utils.TxtUtil;
   import mogames.gameData.ServerProxy;
   import mogames.gameData.base.RewardProxy;
   
   public class HuodongVO
   {
      
      private var _type:String;
      
      private var _name:String;
      
      private var _timeVO:TimeVO;
      
      private var _rewards:Array;
      
      private var _url:String;
      
      public function HuodongVO(param1:String, param2:String, param3:TimeVO, param4:Array, param5:String)
      {
         super();
         this._type = param1;
         this._name = param2;
         this._timeVO = param3;
         this._rewards = param4;
         this._url = param5;
      }
      
      public function get type() : String
      {
         return this._type;
      }
      
      public function get name() : String
      {
         return this._name;
      }
      
      public function get openTime() : TimeVO
      {
         return this._timeVO;
      }
      
      public function get rewards() : Array
      {
         return this._rewards;
      }
      
      public function get isOpen() : Boolean
      {
         if(!this._timeVO)
         {
            return true;
         }
         return !ServerProxy.instance().isOverDay(this._timeVO);
      }
      
      public function get picURL() : String
      {
         return this._url;
      }
      
      public function get tip() : String
      {
         var _loc1_:String = "礼包内容：" + TxtUtil.setColor(RewardProxy.instance().parseName1(this._rewards,false),"FFFF00");
         if(!this.isOpen)
         {
            _loc1_ += "<br><br>" + TxtUtil.setColor("活动将于" + this._timeVO.monthAndDay + "开启，敬请期待！","CCCCCC");
         }
         return _loc1_;
      }
   }
}

