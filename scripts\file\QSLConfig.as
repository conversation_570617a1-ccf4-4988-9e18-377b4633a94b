package file
{
   import mogames.ConstData;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.fuben.vo.FubenVO;
   import mogames.gameData.good.GoodFactory;
   import mogames.gameData.good.bag.base.GameBagVO;
   import mogames.gameData.good.base.ConstBagVO;
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.RoleArgVO;
   
   public class QSLConfig
   {
      
      private static var _instance:QSLConfig;
      
      public var danRoleArg:RoleArgVO;
      
      public var argVO:RoleArgVO;
      
      public var waveData:WaveDataVO;
      
      public var interval:Number;
      
      public var breakTime:Number;
      
      public var deadTime:Number;
      
      public var fuTime:Number;
      
      public var maxDan:int;
      
      private var _bags:Array;
      
      public function QSLConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : QSLConfig
      {
         if(!_instance)
         {
            _instance = new QSLConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         var _loc1_:OneWaveVO = null;
         this.interval = 3;
         this.breakTime = 2;
         this.deadTime = 15;
         this.fuTime = 20;
         this.maxDan = 6;
         this.argVO = new RoleArgVO(166,4200,500,200,30,10,250,130,null);
         this.danRoleArg = new RoleArgVO(267,12000,230,100,30,25,150,150,null);
         this._bags = [11005,11008,11072,11151,11157,11158,11201,11204,11205,11302,11005];
         this.waveData = new WaveDataVO(0);
         this.waveData.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(268,1800,202,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(20);
         _loc1_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(268,1800,202,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(268,1800,202,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(20);
         _loc1_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(268,1800,202,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(268,1800,202,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(20);
         _loc1_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(268,1800,202,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(268,1800,202,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(20);
         _loc1_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(268,1800,202,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(268,1800,202,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(20);
         _loc1_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(268,1800,202,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
      }
      
      public function get randBagVO() : GameBagVO
      {
         var _loc1_:int = int(this._bags[int(Math.random() * this._bags.length)]);
         return GoodFactory.newBagVO(GoodConfig.instance().findConstGood(_loc1_) as ConstBagVO);
      }
      
      public function newReward(param1:Boolean) : Array
      {
         if(!param1)
         {
            return this.loseReward;
         }
         return this.winReward;
      }
      
      private function get winReward() : Array
      {
         var _loc1_:int = 0;
         var _loc6_:int = 0;
         var _loc2_:int = Math.random() * 100 + 1;
         if(_loc2_ <= 20)
         {
            _loc1_ = ConstData.INT1.v;
         }
         else if(_loc2_ <= 65)
         {
            _loc1_ = ConstData.INT2.v;
         }
         else
         {
            _loc1_ = ConstData.INT3.v;
         }
         var _loc3_:Array = [];
         var _loc4_:FubenVO = FubenConfig.instance().findFuben(404);
         var _loc5_:Array = _loc4_.drops.slice(1);
         var _loc7_:int = 0;
         while(_loc7_ < _loc1_)
         {
            _loc6_ = Math.random() * _loc5_.length;
            _loc3_[_loc3_.length] = _loc5_[_loc6_];
            _loc5_.splice(_loc6_,1);
            _loc7_++;
         }
         return _loc3_;
      }
      
      private function get loseReward() : Array
      {
         return [new BaseRewardVO(10000,35000)];
      }
   }
}

