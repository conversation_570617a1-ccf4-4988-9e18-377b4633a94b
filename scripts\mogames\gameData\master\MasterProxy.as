package mogames.gameData.master
{
   import com.mogames.data.Oint;
   import com.mogames.event.GameSignal;
   import com.mogames.event.SignalManager;
   import com.mogames.utils.MathUtil;
   import file.GoodConfig;
   import mogames.gameData.achieve.AchieveProxy;
   import mogames.gameData.depot.DepotProxy;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.game.LackVO;
   import mogames.gameData.good.base.ConstVirtualVO;
   import mogames.gameData.good.vo.GameGoodVO;
   import mogames.gameData.master.vo.GatherVO;
   import mogames.gameData.master.vo.MasterVO;
   import mogames.gameData.role.HeroProxy;
   
   public class MasterProxy
   {
      
      private static var _instance:MasterProxy;
      
      private var _maxLiang:Oint = new Oint();
      
      private var _maxTi:Oint = new Oint();
      
      private var _maxMa:Oint = new Oint();
      
      private var _gatherVO:GatherVO;
      
      private var _masterVO:MasterVO;
      
      private var _list:Array;
      
      public function MasterProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this._gatherVO = new GatherVO();
         MathUtil.saveINT(this._maxLiang,200);
         MathUtil.saveINT(this._maxTi,100);
         MathUtil.saveINT(this._maxMa,100);
      }
      
      public static function instance() : MasterProxy
      {
         if(!_instance)
         {
            _instance = new MasterProxy();
         }
         return _instance;
      }
      
      public function startNew() : void
      {
         this._list = [];
         this._list[this._list.length] = GoodConfig.instance().newVirtual(10000,0);
         this._list[this._list.length] = GoodConfig.instance().newVirtual(10001,100);
         this._list[this._list.length] = GoodConfig.instance().newVirtual(10002,100);
         this._list[this._list.length] = GoodConfig.instance().newVirtual(10003,0);
         this._list[this._list.length] = GoodConfig.instance().newVirtual(10004,0);
         this._list[this._list.length] = GoodConfig.instance().newVirtual(10005,0);
         this._list[this._list.length] = GoodConfig.instance().newVirtual(10006,0);
         this._list[this._list.length] = GoodConfig.instance().newVirtual(10007,0);
         this._list[this._list.length] = GoodConfig.instance().newVirtual(10008,200);
         this._list[this._list.length] = GoodConfig.instance().newVirtual(10009,1);
         this._list[this._list.length] = GoodConfig.instance().newVirtual(10010,0);
         this._list[this._list.length] = GoodConfig.instance().newVirtual(10011,0);
         this._list[this._list.length] = GoodConfig.instance().newVirtual(10012,0);
         this._list[this._list.length] = GoodConfig.instance().newVirtual(10013,0);
         this._list[this._list.length] = GoodConfig.instance().newVirtual(10014,100);
         this._masterVO = new MasterVO();
      }
      
      public function set loadData(param1:Object) : void
      {
         this.startNew();
         if(!param1)
         {
            return;
         }
         this._masterVO.loadData = param1.master;
         this.parseValue(param1.values.split("H"));
      }
      
      public function get savData() : Object
      {
         return {
            "master":this._masterVO.saveData,
            "values":this.collectValue()
         };
      }
      
      public function dailyRefresh() : void
      {
         this.setValue(10008,MathUtil.loadINT(this._maxLiang));
         this.setValue(10014,MathUtil.loadINT(this._maxMa));
      }
      
      public function minRefresh(param1:int) : void
      {
         this.changeValue(10008,param1 * 0.2);
      }
      
      public function refreshTili() : void
      {
         this.setValue(10002,MathUtil.loadINT(this._maxTi));
      }
      
      public function addExp(param1:int) : void
      {
         this._masterVO.curExp += param1;
         if(this._masterVO.curExp >= this.totalExp && this._masterVO.isMaxLevel)
         {
            this._masterVO.curExp = this.totalExp;
            return;
         }
         while(this._masterVO.curExp >= this.totalExp)
         {
            this._masterVO.curExp -= this.totalExp;
            this.levelUP(1);
         }
      }
      
      public function get totalExp() : int
      {
         return this._masterVO.level * this._masterVO.level * 70 + 200;
      }
      
      public function get needExp() : int
      {
         return Math.max(0,this.totalExp - this._masterVO.curExp);
      }
      
      public function levelUP(param1:int) : void
      {
         this._masterVO.level += param1;
         if(this._masterVO.level <= 70)
         {
            this.changeValue(10009,param1);
         }
      }
      
      public function setValue(param1:int, param2:int) : void
      {
         var _loc3_:GameGoodVO = this.findVO(param1);
         _loc3_.amount = param2;
         if(_loc3_.amount <= 0)
         {
            _loc3_.amount = 0;
         }
         SignalManager.signalMaster.dispatchEvent({"signal":GameSignal.REFRESH_VALUE});
         if(param1 == 10008 && param2 < 0)
         {
            FlagProxy.instance().numFlag.changeValue(602,Math.abs(param2));
         }
      }
      
      public function changeValue(param1:int, param2:int) : void
      {
         var _loc3_:GameGoodVO = this.findVO(param1);
         if(!_loc3_)
         {
            return;
         }
         _loc3_.amount += param2;
         if(_loc3_.amount <= 0)
         {
            _loc3_.amount = 0;
         }
         if(param1 == 10002)
         {
            this.updateTi();
         }
         else if(param1 == 10008)
         {
            this.updateLiang();
         }
         SignalManager.signalMaster.dispatchEvent({"signal":GameSignal.REFRESH_VALUE});
         if(param1 == 10000 && param2 > 0)
         {
            AchieveProxy.instance().checkOpen(414,param2);
         }
      }
      
      public function checkValue(param1:int, param2:int) : LackVO
      {
         var _loc3_:GameGoodVO = this.findVO(param1);
         if(_loc3_.amount < param2)
         {
            if(_loc3_.constGood is ConstVirtualVO)
            {
               return new LackVO(_loc3_.constGood.name + "不足！");
            }
            return new LackVO(_loc3_.constGood.name + "不足，目前拥有数为：" + _loc3_.amount);
         }
         return null;
      }
      
      public function findValue(param1:int) : int
      {
         return this.findVO(param1).amount;
      }
      
      public function get masterVO() : MasterVO
      {
         return this._masterVO;
      }
      
      public function get gatherVO() : GatherVO
      {
         return this._gatherVO;
      }
      
      public function get isMaxLiang() : Boolean
      {
         return this.findValue(10008) >= MathUtil.loadINT(this._maxLiang);
      }
      
      public function get isMaxLiao() : Boolean
      {
         return this.findValue(10014) >= MathUtil.loadINT(this._maxMa);
      }
      
      public function hasOneEquip(param1:int) : Boolean
      {
         return HeroProxy.instance().hasEquip(param1) || DepotProxy.instance().findNum(param1) > 0;
      }
      
      private function updateTi() : void
      {
         var _loc1_:GameGoodVO = this.findVO(10002);
         var _loc2_:int = MathUtil.loadINT(this._maxTi);
         if(_loc1_.amount > _loc2_)
         {
            _loc1_.amount = _loc2_;
         }
      }
      
      private function updateLiang() : void
      {
         var _loc1_:GameGoodVO = this.findVO(10008);
         var _loc2_:int = MathUtil.loadINT(this._maxLiang);
         if(_loc1_.amount > _loc2_)
         {
            _loc1_.amount = _loc2_;
         }
      }
      
      private function findVO(param1:int) : GameGoodVO
      {
         var _loc2_:GameGoodVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.constGood.id == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      private function parseValue(param1:Array) : void
      {
         var _loc2_:int = 0;
         var _loc3_:int = int(param1.length);
         while(_loc2_ < _loc3_)
         {
            this._list[_loc2_].amount = int(param1[_loc2_]);
            _loc2_++;
         }
      }
      
      private function collectValue() : String
      {
         var _loc2_:GameGoodVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._list)
         {
            _loc1_[_loc1_.length] = _loc2_.amount;
         }
         return _loc1_.join("H");
      }
   }
}

