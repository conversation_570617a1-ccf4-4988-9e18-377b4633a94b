package mogames.gameData.good.zhibao
{
   import com.mogames.system.GameTimer;
   import mogames.gameBuff.BuffProxy;
   import mogames.gameData.buff.base.BuffVO;
   import mogames.gameData.good.base.ConstEquipVO;
   import mogames.gameData.good.equip.GameZhiBaoVO;
   import mogames.gameData.role.battle.HurtData;
   import mogames.gameEffect.EffectManager;
   import mogames.gameRole.base.IRole;
   import mogames.gameRole.view.HeroBaseView;
   
   public class GameTPTSVO extends GameZhiBaoVO
   {
      
      private var _timer:GameTimer;
      
      public function GameTPTSVO(param1:ConstEquipVO)
      {
         super(param1);
      }
      
      override public function handlerBuff(param1:IRole, param2:HurtData) : void
      {
         var _loc4_:BuffVO = null;
         if(!this._timer)
         {
            this._timer = new GameTimer();
         }
         if(this._timer.running)
         {
            super.handlerBuff(param1,param2);
            return;
         }
         this._timer.setTimeOut(10);
         var _loc3_:Boolean = false;
         for each(_loc4_ in param2.buffList)
         {
            if(_loc4_.isDebuff)
            {
               _loc3_ = true;
            }
            else
            {
               BuffProxy.instance().addRoleBuff(_loc4_,param1);
            }
         }
         if(_loc3_)
         {
            EffectManager.addHeadWord("法宝抵消",param1.x,param1.y - param1.height);
            (param1.view as HeroBaseView).startZBEffect();
         }
      }
      
      override public function clean() : void
      {
         if(this._timer)
         {
            this._timer.destroy();
         }
         this._timer = null;
      }
   }
}

