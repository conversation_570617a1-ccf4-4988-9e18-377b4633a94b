package mogames.gameData.good.equip
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import mogames.gameData.good.base.ConstEquipVO;
   
   public class SkillEquipVO extends GameEquipVO
   {
      
      private var _skillID:Oint = new Oint();
      
      public function SkillEquipVO(param1:ConstEquipVO)
      {
         super(param1);
         MathUtil.saveINT(this._skillID,0);
      }
      
      public function get isSkill() : <PERSON><PERSON><PERSON>
      {
         return this.skillID == 0;
      }
      
      public function get skillID() : int
      {
         return MathUtil.loadINT(this._skillID);
      }
      
      override public function get saveData() : Object
      {
         return {
            "id":_constEquip.id,
            "base":[level,MathUtil.loadINT(_lock),this.skillID].join("H"),
            "cards":saveCards
         };
      }
      
      override public function set loadData(param1:Object) : void
      {
         var _loc2_:Array = param1.base.split("H");
         MathUtil.saveINT(_level,int(_loc2_[0]));
         MathUtil.saveINT(_lock,int(_loc2_[1]));
         MathUtil.saveINT(this._skillID,int(_loc2_[2]));
         loadCards = param1.cards;
      }
   }
}

