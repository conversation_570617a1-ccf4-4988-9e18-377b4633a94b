package mogames.gameData.base.func
{
   import file.HuoYueConfig;
   import mogames.gameData.base.UseProxy;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.base.vo.NeedVO;
   import mogames.gameData.depot.DepotProxy;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.game.LackVO;
   import mogames.gameEffect.EffectManager;
   
   public class CharmVO
   {
      
      private var _rewardVO:BaseRewardVO;
      
      private var _needList:Array;
      
      public function CharmVO(param1:BaseRewardVO, param2:Array)
      {
         super();
         this._rewardVO = param1;
         this._needList = param2;
      }
      
      public function checkLack() : Bo<PERSON>an
      {
         var _loc1_:LackVO = UseProxy.instance().checkLack(this._needList);
         if(_loc1_)
         {
            EffectManager.addPureText(_loc1_.str);
            return true;
         }
         return false;
      }
      
      public function countMax() : int
      {
         var _loc2_:int = 0;
         var _loc3_:NeedVO = null;
         var _loc1_:int = 99;
         for each(_loc3_ in this._needList)
         {
            _loc2_ = DepotProxy.instance().findNum(_loc3_.needID) / _loc3_.needNum;
            if(_loc2_ < _loc1_)
            {
               _loc1_ = _loc2_;
            }
         }
         return _loc1_;
      }
      
      public function handlerUse(param1:int) : void
      {
         var _loc2_:int = 0;
         while(_loc2_ < param1)
         {
            UseProxy.instance().useStuff(this._needList,null);
            _loc2_++;
         }
         FlagProxy.instance().numFlag.changeValue(603,param1);
         HuoYueConfig.instance().addHY(309);
      }
      
      public function get rewardVO() : BaseRewardVO
      {
         return this._rewardVO;
      }
      
      public function get needList() : Array
      {
         return this._needList;
      }
   }
}

