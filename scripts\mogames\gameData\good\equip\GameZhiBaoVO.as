package mogames.gameData.good.equip
{
   import mogames.gameBuff.BuffProxy;
   import mogames.gameData.buff.base.BuffVO;
   import mogames.gameData.good.base.ConstEquipVO;
   import mogames.gameData.role.battle.HurtData;
   import mogames.gameRole.base.IRole;
   
   public class GameZhiBaoVO extends GameEquipVO
   {
      
      public function GameZhiBaoVO(param1:ConstEquipVO)
      {
         super(param1);
      }
      
      public function hurtBefore(param1:IRole, param2:HurtData) : void
      {
         param2.hurtValue *= 0.9;
      }
      
      public function hurtFinish() : void
      {
      }
      
      public function handlerATK(param1:IRole) : void
      {
      }
      
      public function handlerATKEnd(param1:*) : void
      {
      }
      
      public function handlerBuff(param1:IRole, param2:HurtData) : void
      {
         var _loc3_:BuffVO = null;
         for each(_loc3_ in param2.buffList)
         {
            BuffProxy.instance().addRoleBuff(_loc3_,param1);
         }
      }
      
      public function clean() : void
      {
      }
   }
}

