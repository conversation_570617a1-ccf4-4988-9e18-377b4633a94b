package mogames.gameData.mache
{
   import com.mogames.utils.MathUtil;
   import mogames.ConstData;
   import mogames.gameData.mission.MissionProxy;
   import mogames.gameData.vip.VipProxy;
   import mogames.gameNet.SaveManager;
   
   public class MacheProxy
   {
      
      private static var _instance:MacheProxy;
      
      private var _list:Array;
      
      public function MacheProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : MacheProxy
      {
         if(!_instance)
         {
            _instance = new MacheProxy();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = [];
         var _loc1_:int = 0;
         while(_loc1_ < 10)
         {
            this._list[_loc1_] = new MacheDataVO();
            _loc1_++;
         }
      }
      
      public function startNew() : void
      {
         var _loc1_:int = 0;
         while(_loc1_ < 10)
         {
            this._list[_loc1_].setNew(0);
            _loc1_++;
         }
      }
      
      public function set loadData(param1:Array) : void
      {
         this.startNew();
         if(!param1 || param1.length <= 0)
         {
            return;
         }
         var _loc2_:int = 0;
         var _loc3_:int = int(param1.length);
         while(_loc2_ < _loc3_)
         {
            this._list[_loc2_].loadData = param1[_loc2_];
            _loc2_++;
         }
      }
      
      public function get saveData() : Array
      {
         if(!MissionProxy.instance().allFinish(204))
         {
            return [];
         }
         var _loc1_:Array = [];
         var _loc2_:int = 0;
         var _loc3_:int = int(this._list.length);
         while(_loc2_ < _loc3_)
         {
            _loc1_[_loc2_] = this._list[_loc2_].saveData;
            _loc2_++;
         }
         return _loc1_;
      }
      
      public function get macheDatas() : Array
      {
         var _loc1_:MacheDataVO = null;
         for each(_loc1_ in this._list)
         {
            if(_loc1_.constVO != null)
            {
               return this._list;
            }
         }
         this.dailyRefresh();
         SaveManager.instance().saveAuto();
         return this._list;
      }
      
      public function dailyRefresh() : void
      {
         var _loc5_:int = 0;
         var _loc6_:int = 0;
         if(!MissionProxy.instance().allFinish(204))
         {
            return;
         }
         var _loc1_:Array = MissionProxy.instance().allFinishJunCheng;
         var _loc2_:Array = MissionProxy.instance().allFinishXianCheng;
         var _loc3_:int = MathUtil.randomNum(1,3);
         if(VipProxy.instance().hasFunc(122))
         {
            _loc3_ += ConstData.INT2.v;
         }
         _loc3_ = Math.min(_loc3_,_loc1_.length);
         var _loc4_:Array = [];
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc6_ = Math.random() * _loc1_.length;
            _loc4_[_loc4_.length] = _loc1_[_loc6_];
            _loc1_.splice(_loc6_,1);
            _loc5_++;
         }
         _loc3_ = Math.min(10 - _loc3_,_loc2_.length);
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            _loc6_ = Math.random() * _loc2_.length;
            _loc4_[_loc4_.length] = _loc2_[_loc6_];
            _loc2_.splice(_loc6_,1);
            _loc5_++;
         }
         _loc3_ = int(_loc4_.length);
         _loc5_ = 0;
         while(_loc5_ < _loc3_)
         {
            this._list[_loc5_].setNew(_loc4_[_loc5_]);
            _loc5_++;
         }
      }
   }
}

