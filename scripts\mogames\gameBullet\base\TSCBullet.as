package mogames.gameBullet.base
{
   import mogames.Layers;
   import mogames.gameEffect.EffectManager;
   import mogames.gameRole.TargetUtil;
   import mogames.gameRole.base.IRole;
   
   public class TSCBullet extends PaoBullet
   {
      
      private var _area:int = 90;
      
      public function TSCBullet()
      {
         super();
      }
      
      override protected function dispatchATK() : void
      {
         var _loc2_:IRole = null;
         if(!targetEnabled)
         {
            return;
         }
         var _loc1_:Array = TargetUtil.rectTargets(_owner.camp,_target.location,this._area,0);
         for each(_loc2_ in _loc1_)
         {
            _loc2_.setHurt(_hurtData,"SEQ_MINI_BOOM_CLIP");
         }
         EffectManager.addBMC(_constVO.hurtSkin,Layers.backLayer,_bmc.x,_bmc.y,false,_constVO.hurtSound);
      }
   }
}

