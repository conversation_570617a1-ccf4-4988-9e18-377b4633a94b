package mogames.gameAsset.co
{
   import flash.display.BitmapData;
   import flash.utils.Dictionary;
   
   public class AssetTempDic
   {
      
      private var _assetDic:Dictionary;
      
      public function AssetTempDic()
      {
         super();
         this._assetDic = new Dictionary();
      }
      
      public function saveClass(param1:String, param2:Object) : void
      {
         if(this._assetDic[param1])
         {
            return;
         }
         this._assetDic[param1] = param2;
      }
      
      public function findAsset(param1:String) : *
      {
         var _loc2_:Class = this._assetDic[param1] as Class;
         if(!_loc2_)
         {
            throw new Error("未找到加载资源：" + param1);
         }
         return new _loc2_();
      }
      
      public function destroy() : void
      {
         var _loc1_:String = null;
         for(_loc1_ in this._assetDic)
         {
            if(this._assetDic[_loc1_] is BitmapData)
            {
               this._assetDic[_loc1_].dispose();
            }
            delete this._assetDic[_loc1_];
         }
      }
   }
}

