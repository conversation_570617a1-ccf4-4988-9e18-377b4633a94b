package file
{
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.base.vo.EquipRewardVO;
   import mogames.gameData.mission.base.MissionBattleVO;
   import mogames.gameData.mission.normal.*;
   import mogames.gameData.mission.wave.WaveDataVO;
   
   public class WaveNormalConfig
   {
      
      private static var _instance:WaveNormalConfig;
      
      private var _dic:Vector.<WaveDataVO>;
      
      private var _battles:Vector.<MissionBattleVO>;
      
      public function WaveNormalConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.initWave();
         this.initBattle();
      }
      
      public static function instance() : WaveNormalConfig
      {
         if(!_instance)
         {
            _instance = new WaveNormalConfig();
         }
         return _instance;
      }
      
      private function initBattle() : void
      {
         this._battles = new Vector.<MissionBattleVO>();
         this._battles[this._battles.length] = new MissionBattleVO(1000,84,109,[new BaseRewardVO(10000,5000),new BaseRewardVO(10101,2),new BaseRewardVO(10250,3)],[],"scene/new-110.json","大都争夺战");
         this._battles[this._battles.length] = new MissionBattleVO(1001,98,147,[new BaseRewardVO(10000,1000),new BaseRewardVO(10901,1),new EquipRewardVO(20001,0)],[],"scene/yz101.json");
         this._battles[this._battles.length] = new MissionBattleVO(1002,141,195,[new BaseRewardVO(10000,1500),new EquipRewardVO(20401,0),new EquipRewardVO(20101,0)],[],"scene/yz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(1003,203,267,[new BaseRewardVO(10000,2000),new EquipRewardVO(20201,0),new EquipRewardVO(20301,0)],[],"scene/yz103.json");
         this._battles[this._battles.length] = new MissionBattleVO(1004,281,378,[new BaseRewardVO(10000,2500),new BaseRewardVO(10102,1),new BaseRewardVO(10004,50)],[],"scene/yz104.json","保护平民");
         this._battles[this._battles.length] = new MissionBattleVO(1005,378,483,[new BaseRewardVO(10000,3000),new BaseRewardVO(10901,2),new EquipRewardVO(23001,0)],[],"scene/yz105.json");
         this._battles[this._battles.length] = new MissionBattleVO(1006,441,602,[new BaseRewardVO(10000,3500),new EquipRewardVO(23101,0),new EquipRewardVO(23201,0)],[],"scene/yz106.json");
         this._battles[this._battles.length] = new MissionBattleVO(1007,573,735,[new BaseRewardVO(10000,4000),new EquipRewardVO(23301,0),new EquipRewardVO(23401,0)],[],"scene/yz107.json");
         this._battles[this._battles.length] = new MissionBattleVO(1008,721,882,[new BaseRewardVO(10000,4500),new BaseRewardVO(10102,1),new EquipRewardVO(25001,0)],[],"scene/yz108.json","保护于吉");
         this._battles[this._battles.length] = new MissionBattleVO(1009,888,1043,[new BaseRewardVO(10000,5000),new EquipRewardVO(21001,0),new EquipRewardVO(25101,0)],[],"scene/yz109.json");
         this._battles[this._battles.length] = new MissionBattleVO(1010,1071,1218,[new BaseRewardVO(10000,5500),new EquipRewardVO(21101,0),new EquipRewardVO(21201,0)],[],"scene/yz110.json");
         this._battles[this._battles.length] = new MissionBattleVO(1011,1198,1407,[new BaseRewardVO(10000,6000),new EquipRewardVO(21301,0),new EquipRewardVO(21401,0)],[],"scene/yz111.json");
         this._battles[this._battles.length] = new MissionBattleVO(1012,1326,1610,[new BaseRewardVO(10000,6500),new BaseRewardVO(10102,2),new EquipRewardVO(25201,0)],[],"scene/yz112.json","首战曹操");
         this._battles[this._battles.length] = new MissionBattleVO(1013,1455,1827,[new BaseRewardVO(10000,7000),new EquipRewardVO(22001,0),new EquipRewardVO(25301,0)],[],"scene/yz113.json");
         this._battles[this._battles.length] = new MissionBattleVO(1014,1585,2058,[new BaseRewardVO(10000,7500),new EquipRewardVO(25401,0),new EquipRewardVO(22201,0)],[],"scene/yz114.json");
         this._battles[this._battles.length] = new MissionBattleVO(1015,1716,2303,[new BaseRewardVO(10000,8000),new EquipRewardVO(22301,0),new EquipRewardVO(22401,0)],[],"scene/yz115.json");
         this._battles[this._battles.length] = new MissionBattleVO(1016,1848,2562,[new BaseRewardVO(10000,8500),new EquipRewardVO(24001,0),new EquipRewardVO(22101,0)],[],"scene/yz116.json");
         this._battles[this._battles.length] = new MissionBattleVO(1017,1981,2835,[new BaseRewardVO(10000,9000),new EquipRewardVO(24101,0),new EquipRewardVO(24201,0)],[],"scene/yz117.json");
         this._battles[this._battles.length] = new MissionBattleVO(1018,2114,3122,[new BaseRewardVO(10000,9500),new EquipRewardVO(24301,0),new EquipRewardVO(24401,0)],[],"scene/yz118.json");
         this._battles[this._battles.length] = new MissionBattleVO(1019,2248,3283,[new BaseRewardVO(10000,10000),new BaseRewardVO(10003,50),new BaseRewardVO(10005,50)],[],"scene/yz119.json","首战郭嘉");
         this._battles[this._battles.length] = new MissionBattleVO(1020,2382,3408,[new BaseRewardVO(10000,10500),new BaseRewardVO(11002,2),new BaseRewardVO(10003,10)],[],"scene/yanz101.json");
         this._battles[this._battles.length] = new MissionBattleVO(1021,2517,3654,[new BaseRewardVO(10000,11000),new BaseRewardVO(11002,2),new BaseRewardVO(11101,2)],[],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(1022,2652,3876,[new BaseRewardVO(10000,11500),new BaseRewardVO(10272,1),new BaseRewardVO(10102,2)],[],"scene/yanz103.json");
         this._battles[this._battles.length] = new MissionBattleVO(1023,2788,4129,[new BaseRewardVO(10000,12000),new BaseRewardVO(11002,2),new BaseRewardVO(10004,10)],[],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(1024,2923,4385,[new BaseRewardVO(10000,12500),new BaseRewardVO(11002,2),new BaseRewardVO(11002,2)],[],"scene/yanz105.json");
         this._battles[this._battles.length] = new MissionBattleVO(1025,3059,4644,[new BaseRewardVO(10000,13000),new BaseRewardVO(10272,1),new BaseRewardVO(10005,10)],[],"scene/yanz106.json");
         this._battles[this._battles.length] = new MissionBattleVO(1026,3196,4906,[new BaseRewardVO(10000,13500),new BaseRewardVO(11002,2),new BaseRewardVO(10611,2)],[],"scene/yanz107.json");
         this._battles[this._battles.length] = new MissionBattleVO(1027,3332,5171,[new BaseRewardVO(10000,14000),new BaseRewardVO(11002,2),new BaseRewardVO(11104,2)],[],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(1028,3469,5439,[new BaseRewardVO(10000,14500),new BaseRewardVO(10272,1),new BaseRewardVO(10006,10)],[],"scene/yanz109.json");
         this._battles[this._battles.length] = new MissionBattleVO(1029,3606,5709,[new BaseRewardVO(10000,15000),new BaseRewardVO(11002,2),new BaseRewardVO(11071,5)],[],"scene/yanz110.json");
         this._battles[this._battles.length] = new MissionBattleVO(1030,3743,5981,[new BaseRewardVO(10000,15500),new BaseRewardVO(11002,2),new BaseRewardVO(10103,1)],[],"scene/yanz111.json");
         this._battles[this._battles.length] = new MissionBattleVO(1031,3880,6256,[new BaseRewardVO(10000,16000),new BaseRewardVO(10272,1),new BaseRewardVO(11011,2)],[],"scene/yanz112.json");
         this._battles[this._battles.length] = new MissionBattleVO(1032,4017,6533,[new BaseRewardVO(10000,16500),new BaseRewardVO(11002,2),new BaseRewardVO(11073,2)],[],"scene/yanz113.json");
         this._battles[this._battles.length] = new MissionBattleVO(1033,4155,6812,[new BaseRewardVO(10000,17000),new BaseRewardVO(11002,2),new BaseRewardVO(11008,2)],[],"scene/yanz114.json");
         this._battles[this._battles.length] = new MissionBattleVO(1034,4293,7092,[new BaseRewardVO(10000,17500),new BaseRewardVO(10272,1),new BaseRewardVO(11104,2)],[],"scene/yanz115-430.json");
         this._battles[this._battles.length] = new MissionBattleVO(1035,4430,7375,[new BaseRewardVO(10000,18000),new BaseRewardVO(11002,2),new BaseRewardVO(11073,2)],[],"scene/yanz116.json");
         this._battles[this._battles.length] = new MissionBattleVO(1036,4568,7659,[new BaseRewardVO(10000,18500),new BaseRewardVO(11002,2),new BaseRewardVO(10302,2)],[],"scene/yanz117.json");
         this._battles[this._battles.length] = new MissionBattleVO(1037,4706,7945,[new BaseRewardVO(10000,19000),new BaseRewardVO(11002,2),new BaseRewardVO(10250,5)],[],"scene/yanz118.json");
         this._battles[this._battles.length] = new MissionBattleVO(1038,4957,8058,[new BaseRewardVO(10000,21000),new BaseRewardVO(11002,2),new BaseRewardVO(10405,1)],[],"scene/qinz101-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1039,5214,8258,[new BaseRewardVO(10000,21100),new BaseRewardVO(11002,2),new BaseRewardVO(10406,1)],[],"scene/qinz102-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1040,5478,8457,[new BaseRewardVO(10000,21200),new BaseRewardVO(10273,1),new BaseRewardVO(10407,1)],[],"scene/qinz103-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1041,5748,8657,[new BaseRewardVO(10000,21300),new BaseRewardVO(11002,2),new BaseRewardVO(10408,1)],[],"scene/qinz104-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1042,6025,8856,[new BaseRewardVO(10000,21400),new BaseRewardVO(11002,2),new BaseRewardVO(10409,1)],[],"scene/qinz105-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1043,6308,9056,[new BaseRewardVO(10000,21500),new BaseRewardVO(10273,1),new BaseRewardVO(10410,1)],[],"scene/qinz106-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1044,6598,9256,[new BaseRewardVO(10000,21600),new BaseRewardVO(11002,2),new BaseRewardVO(10603,1)],[],"scene/qinz107-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1045,6894,9455,[new BaseRewardVO(10000,21700),new BaseRewardVO(11002,2),new BaseRewardVO(10604,1)],[],"scene/qinz108-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1046,7197,9655,[new BaseRewardVO(10000,21800),new BaseRewardVO(10273,1),new BaseRewardVO(10605,1)],[],"scene/qinz109-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1047,7506,9855,[new BaseRewardVO(10000,21900),new BaseRewardVO(11002,2),new BaseRewardVO(10606,1)],[],"scene/qinz110-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1048,7822,10054,[new BaseRewardVO(10000,22000),new BaseRewardVO(11002,2),new BaseRewardVO(10607,1)],[],"scene/qinz111-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1049,8144,10254,[new BaseRewardVO(10000,22100),new BaseRewardVO(10273,1),new BaseRewardVO(10608,1)],[],"scene/qinz112-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1050,8473,10454,[new BaseRewardVO(10000,22200),new BaseRewardVO(11002,2),new BaseRewardVO(10609,1)],[],"scene/qinz113-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1051,8808,10654,[new BaseRewardVO(10000,22300),new BaseRewardVO(11002,2),new BaseRewardVO(10613,1)],[],"scene/qinz114-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1052,9150,10853,[new BaseRewardVO(10000,22400),new BaseRewardVO(10273,1),new BaseRewardVO(10614,1)],[],"scene/qinz115-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1053,9499,11053,[new BaseRewardVO(10000,22500),new BaseRewardVO(11002,2),new BaseRewardVO(10615,1)],[],"scene/qinz116-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1054,9853,11253,[new BaseRewardVO(10000,22600),new BaseRewardVO(11002,2),new BaseRewardVO(10608,1)],[],"scene/qinz117-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1055,10215,11453,[new BaseRewardVO(10000,22700),new BaseRewardVO(10273,1),new BaseRewardVO(10609,1)],[],"scene/qinz118-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1056,10583,11652,[new BaseRewardVO(10000,22800),new BaseRewardVO(11002,2),new BaseRewardVO(10613,1)],[],"scene/qinz119-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1057,10957,11852,[new BaseRewardVO(10000,22900),new BaseRewardVO(11002,2),new BaseRewardVO(10614,1)],[],"scene/qinz120-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1058,11331,12052,[new BaseRewardVO(10000,23000),new BaseRewardVO(11002,2),new BaseRewardVO(10405,2)],[],"scene/yanz101.json");
         this._battles[this._battles.length] = new MissionBattleVO(1059,11505,12253,[new BaseRewardVO(10000,23100),new BaseRewardVO(11002,2),new BaseRewardVO(10406,2)],[],"scene/qinz102-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1060,11679,12454,[new BaseRewardVO(10000,23200),new BaseRewardVO(10273,1),new BaseRewardVO(10407,2)],[],"scene/yanz103.json");
         this._battles[this._battles.length] = new MissionBattleVO(1061,11853,12655,[new BaseRewardVO(10000,23300),new BaseRewardVO(11002,2),new BaseRewardVO(10408,2)],[],"scene/qinz104-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1062,12027,12856,[new BaseRewardVO(10000,23400),new BaseRewardVO(11002,2),new BaseRewardVO(10409,2)],[],"scene/yanz105.json");
         this._battles[this._battles.length] = new MissionBattleVO(1063,12201,13057,[new BaseRewardVO(10000,23500),new BaseRewardVO(10273,1),new BaseRewardVO(10410,2)],[],"scene/qinz106-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1064,12375,13258,[new BaseRewardVO(10000,23600),new BaseRewardVO(11002,2),new BaseRewardVO(10603,2)],[],"scene/yanz107.json");
         this._battles[this._battles.length] = new MissionBattleVO(1065,12549,13459,[new BaseRewardVO(10000,23700),new BaseRewardVO(11002,2),new BaseRewardVO(10604,2)],[],"scene/qinz108-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1066,12723,13660,[new BaseRewardVO(10000,23800),new BaseRewardVO(10273,1),new BaseRewardVO(10605,2)],[],"scene/yanz109.json");
         this._battles[this._battles.length] = new MissionBattleVO(1067,12897,13861,[new BaseRewardVO(10000,23900),new BaseRewardVO(11002,2),new BaseRewardVO(10606,2)],[],"scene/qinz110-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1068,13071,14062,[new BaseRewardVO(10000,24000),new BaseRewardVO(11002,2),new BaseRewardVO(10607,2)],[],"scene/yanz111.json");
         this._battles[this._battles.length] = new MissionBattleVO(1069,13245,14263,[new BaseRewardVO(10000,24100),new BaseRewardVO(10273,1),new BaseRewardVO(10608,2)],[],"scene/qinz112-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1070,13419,14464,[new BaseRewardVO(10000,24200),new BaseRewardVO(11002,2),new BaseRewardVO(10609,2)],[],"scene/yanz113.json");
         this._battles[this._battles.length] = new MissionBattleVO(1071,13593,14665,[new BaseRewardVO(10000,24300),new BaseRewardVO(11002,2),new BaseRewardVO(10613,2)],[],"scene/qinz114-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1072,13767,14866,[new BaseRewardVO(10000,24400),new BaseRewardVO(10273,1),new BaseRewardVO(10614,2)],[],"scene/qinz115-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1073,13941,15067,[new BaseRewardVO(10000,24500),new BaseRewardVO(11002,2),new BaseRewardVO(10615,2)],[],"scene/qinz116-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1074,14115,15268,[new BaseRewardVO(10000,24600),new BaseRewardVO(11002,2),new BaseRewardVO(10608,2)],[],"scene/yanz117.json");
         this._battles[this._battles.length] = new MissionBattleVO(1075,14289,15469,[new BaseRewardVO(10000,24700),new BaseRewardVO(10273,1),new BaseRewardVO(10609,2)],[],"scene/qinz118-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1076,14463,15670,[new BaseRewardVO(10000,24800),new BaseRewardVO(11002,2),new BaseRewardVO(10613,2)],[],"scene/yanz118.json");
         this._battles[this._battles.length] = new MissionBattleVO(1077,14637,15871,[new BaseRewardVO(10000,24900),new BaseRewardVO(11002,2),new BaseRewardVO(10614,2)],[],"scene/qinz120-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1078,14811,16072,[new BaseRewardVO(10000,25000),new BaseRewardVO(11002,2),new BaseRewardVO(10405,1)],[],"scene/yangz101-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1079,14985,16275,[new BaseRewardVO(10000,25100),new BaseRewardVO(11002,2),new BaseRewardVO(10406,1)],[],"scene/yangz102-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1080,15159,16478,[new BaseRewardVO(10000,25200),new BaseRewardVO(11002,2),new BaseRewardVO(10407,1)],[],"scene/yangz103-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1081,15333,16681,[new BaseRewardVO(10000,25300),new BaseRewardVO(11002,2),new BaseRewardVO(10408,1)],[],"scene/yangz104-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1082,15507,16884,[new BaseRewardVO(10000,25400),new BaseRewardVO(11002,2),new BaseRewardVO(10409,1)],[],"scene/yangz105-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1083,15681,17087,[new BaseRewardVO(10000,25500),new BaseRewardVO(11002,2),new BaseRewardVO(10410,1)],[],"scene/yangz106-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1084,15855,17290,[new BaseRewardVO(10000,25600),new BaseRewardVO(11002,2),new BaseRewardVO(10603,1)],[],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1085,16029,17493,[new BaseRewardVO(10000,25700),new BaseRewardVO(11002,2),new BaseRewardVO(10604,1)],[],"scene/yangz108-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1086,16203,17696,[new BaseRewardVO(10000,25800),new BaseRewardVO(11002,2),new BaseRewardVO(10605,1)],[],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1087,16377,17899,[new BaseRewardVO(10000,25900),new BaseRewardVO(11002,2),new BaseRewardVO(10606,1)],[],"scene/yangz110-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1088,16551,18102,[new BaseRewardVO(10000,25000),new BaseRewardVO(11002,2),new BaseRewardVO(10607,1)],[],"scene/yangz111-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1089,16725,18305,[new BaseRewardVO(10000,25100),new BaseRewardVO(11002,2),new BaseRewardVO(10608,1)],[],"scene/yangz112-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1090,16899,18508,[new BaseRewardVO(10000,25200),new BaseRewardVO(11002,2),new BaseRewardVO(10609,1)],[],"scene/yangz113-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1091,17073,18711,[new BaseRewardVO(10000,25300),new BaseRewardVO(11002,2),new BaseRewardVO(10613,1)],[],"scene/yangz114-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1092,17247,18914,[new BaseRewardVO(10000,25400),new BaseRewardVO(11002,2),new BaseRewardVO(10614,1)],[],"scene/yangz115-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1093,17421,19117,[new BaseRewardVO(10000,25500),new BaseRewardVO(11002,2),new BaseRewardVO(10615,1)],[],"scene/yangz116-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1094,17595,19320,[new BaseRewardVO(10000,25600),new BaseRewardVO(11002,2),new BaseRewardVO(10608,1)],[],"scene/yangz117-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1095,17769,19523,[new BaseRewardVO(10000,25700),new BaseRewardVO(11002,2),new BaseRewardVO(10609,1)],[],"scene/yangz118-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1096,17943,19726,[new BaseRewardVO(10000,25800),new BaseRewardVO(11002,2),new BaseRewardVO(10613,1)],[],"scene/yangz119-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1097,18117,19929,[new BaseRewardVO(10000,25900),new BaseRewardVO(11002,2),new BaseRewardVO(10614,1)],[],"scene/yangz120-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1098,18291,20132,[new BaseRewardVO(10000,26000),new BaseRewardVO(11002,2),new BaseRewardVO(10405,1)],[],"scene/qinz101-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1099,18465,20335,[new BaseRewardVO(10000,26100),new BaseRewardVO(11002,2),new BaseRewardVO(10406,1)],[],"scene/yangz102-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1100,18639,20538,[new BaseRewardVO(10000,26200),new BaseRewardVO(11002,2),new BaseRewardVO(10407,1)],[],"scene/qinz103-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1101,18813,20741,[new BaseRewardVO(10000,26300),new BaseRewardVO(11002,2),new BaseRewardVO(10408,1)],[],"scene/yangz104-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1102,18987,20944,[new BaseRewardVO(10000,26400),new BaseRewardVO(11002,2),new BaseRewardVO(10409,1)],[],"scene/qinz105-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1103,19161,21147,[new BaseRewardVO(10000,26500),new BaseRewardVO(11002,2),new BaseRewardVO(10410,1)],[],"scene/yangz106-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1104,19335,21350,[new BaseRewardVO(10000,26600),new BaseRewardVO(11002,2),new BaseRewardVO(10603,1)],[],"scene/qinz107-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1105,19509,21553,[new BaseRewardVO(10000,26700),new BaseRewardVO(11002,2),new BaseRewardVO(10604,1)],[],"scene/yangz108-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1106,19683,21756,[new BaseRewardVO(10000,26800),new BaseRewardVO(11002,2),new BaseRewardVO(10605,1)],[],"scene/qinz109-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1107,19857,21959,[new BaseRewardVO(10000,26900),new BaseRewardVO(11002,2),new BaseRewardVO(10606,1)],[],"scene/yangz110-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1108,20031,22162,[new BaseRewardVO(10000,26000),new BaseRewardVO(11002,2),new BaseRewardVO(10607,1)],[],"scene/qinz111-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1109,20205,22365,[new BaseRewardVO(10000,26100),new BaseRewardVO(11002,2),new BaseRewardVO(10608,1)],[],"scene/yangz112-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1110,20379,22568,[new BaseRewardVO(10000,26200),new BaseRewardVO(11002,2),new BaseRewardVO(10609,1)],[],"scene/qinz113-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1111,20553,22771,[new BaseRewardVO(10000,26300),new BaseRewardVO(11002,2),new BaseRewardVO(10613,1)],[],"scene/yangz114-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1112,20727,22974,[new BaseRewardVO(10000,26400),new BaseRewardVO(11002,2),new BaseRewardVO(10614,1)],[],"scene/qinz115-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1113,20901,23177,[new BaseRewardVO(10000,26500),new BaseRewardVO(11002,2),new BaseRewardVO(10615,1)],[],"scene/yangz116-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1114,21075,23380,[new BaseRewardVO(10000,26600),new BaseRewardVO(11002,2),new BaseRewardVO(10608,1)],[],"scene/qinz117-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1115,21249,23583,[new BaseRewardVO(10000,26700),new BaseRewardVO(11002,2),new BaseRewardVO(10609,1)],[],"scene/yangz118-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1116,21423,23786,[new BaseRewardVO(10000,26800),new BaseRewardVO(11002,2),new BaseRewardVO(10613,1)],[],"scene/qinz119-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1117,21597,23989,[new BaseRewardVO(10000,26900),new BaseRewardVO(11002,2),new BaseRewardVO(10614,1)],[],"scene/yangz120-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1118,21971,24192,[new BaseRewardVO(10000,27000),new BaseRewardVO(11002,2),new BaseRewardVO(11005,1)],[],"scene/yangz101-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1119,22645,24395,[new BaseRewardVO(10000,27100),new BaseRewardVO(11002,2),new BaseRewardVO(11005,1)],[],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(1120,23319,24598,[new BaseRewardVO(10000,27200),new BaseRewardVO(11002,2),new BaseRewardVO(11005,1)],[],"scene/yangz103-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1121,23993,24801,[new BaseRewardVO(10000,27300),new BaseRewardVO(11002,2),new BaseRewardVO(11005,1)],[],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(1122,24667,25004,[new BaseRewardVO(10000,27400),new BaseRewardVO(11002,2),new BaseRewardVO(11005,1)],[],"scene/yangz105-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1123,25341,25207,[new BaseRewardVO(10000,27500),new BaseRewardVO(11002,2),new BaseRewardVO(11005,1)],[],"scene/yanz106.json");
         this._battles[this._battles.length] = new MissionBattleVO(1124,26015,25410,[new BaseRewardVO(10000,27600),new BaseRewardVO(11002,2),new BaseRewardVO(11005,1)],[],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1125,26689,25613,[new BaseRewardVO(10000,27700),new BaseRewardVO(11002,2),new BaseRewardVO(11005,1)],[],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(1126,27363,25816,[new BaseRewardVO(10000,27800),new BaseRewardVO(11002,2),new BaseRewardVO(11005,1)],[],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1127,28037,26019,[new BaseRewardVO(10000,27900),new BaseRewardVO(11002,2),new BaseRewardVO(11005,1)],[],"scene/yanz110.json");
         this._battles[this._battles.length] = new MissionBattleVO(1128,28711,26222,[new BaseRewardVO(10000,27000),new BaseRewardVO(11002,2),new BaseRewardVO(11005,1)],[],"scene/yangz111-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1129,29385,26425,[new BaseRewardVO(10000,27100),new BaseRewardVO(11002,2),new BaseRewardVO(11005,1)],[],"scene/yanz112.json");
         this._battles[this._battles.length] = new MissionBattleVO(1130,30059,26628,[new BaseRewardVO(10000,27200),new BaseRewardVO(11002,2),new BaseRewardVO(11005,1)],[],"scene/yangz113-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1131,30733,26831,[new BaseRewardVO(10000,27300),new BaseRewardVO(11002,2),new BaseRewardVO(11005,1)],[],"scene/yanz114.json");
         this._battles[this._battles.length] = new MissionBattleVO(1132,31407,27034,[new BaseRewardVO(10000,27400),new BaseRewardVO(11002,2),new BaseRewardVO(11005,1)],[],"scene/yangz115-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1133,32081,27237,[new BaseRewardVO(10000,27500),new BaseRewardVO(11002,2),new BaseRewardVO(11005,1)],[],"scene/yanz116.json");
         this._battles[this._battles.length] = new MissionBattleVO(1134,32755,27440,[new BaseRewardVO(10000,27600),new BaseRewardVO(11002,2),new BaseRewardVO(11005,1)],[],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1135,33429,27643,[new BaseRewardVO(10000,27700),new BaseRewardVO(11002,2),new BaseRewardVO(11005,1)],[],"scene/yanz118.json");
         this._battles[this._battles.length] = new MissionBattleVO(1136,34103,27846,[new BaseRewardVO(10000,27800),new BaseRewardVO(11002,2),new BaseRewardVO(11005,1)],[],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1137,34777,28049,[new BaseRewardVO(10000,27900),new BaseRewardVO(11002,2),new BaseRewardVO(11005,1)],[],"scene/jingz120.json");
         this._battles[this._battles.length] = new MissionBattleVO(1138,35851,28252,[new BaseRewardVO(10000,28000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz101.json");
         this._battles[this._battles.length] = new MissionBattleVO(1139,36725,28455,[new BaseRewardVO(10000,28100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz102-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1140,37599,28658,[new BaseRewardVO(10000,28200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz103.json");
         this._battles[this._battles.length] = new MissionBattleVO(1141,38473,28861,[new BaseRewardVO(10000,28300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz104-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1142,39347,29064,[new BaseRewardVO(10000,28400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz105.json");
         this._battles[this._battles.length] = new MissionBattleVO(1143,40221,29267,[new BaseRewardVO(10000,28500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz106-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1144,41095,29318,[new BaseRewardVO(10000,28600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz107.json");
         this._battles[this._battles.length] = new MissionBattleVO(1145,41969,29470,[new BaseRewardVO(10000,28700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz108-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1146,42843,29673,[new BaseRewardVO(10000,28800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz109.json");
         this._battles[this._battles.length] = new MissionBattleVO(1147,43717,29876,[new BaseRewardVO(10000,28900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz110-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1148,44591,30079,[new BaseRewardVO(10000,29000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz111.json");
         this._battles[this._battles.length] = new MissionBattleVO(1149,45465,30282,[new BaseRewardVO(10000,29100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz112-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1150,46339,30485,[new BaseRewardVO(10000,29200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz113.json");
         this._battles[this._battles.length] = new MissionBattleVO(1151,47213,30688,[new BaseRewardVO(10000,29300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz114-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1152,48087,30891,[new BaseRewardVO(10000,29400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz115-430.json");
         this._battles[this._battles.length] = new MissionBattleVO(1153,48961,31094,[new BaseRewardVO(10000,29500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz116-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1154,49835,31297,[new BaseRewardVO(10000,29600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz117.json");
         this._battles[this._battles.length] = new MissionBattleVO(1155,50709,31500,[new BaseRewardVO(10000,29700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz118-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1156,51583,31703,[new BaseRewardVO(10000,29800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz118.json");
         this._battles[this._battles.length] = new MissionBattleVO(1157,52457,31906,[new BaseRewardVO(10000,29900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz106-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1158,53331,32109,[new BaseRewardVO(10000,30000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(1159,54205,32312,[new BaseRewardVO(10000,30100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz101-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1160,55079,32515,[new BaseRewardVO(10000,30200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(1161,55953,32718,[new BaseRewardVO(10000,30300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz103-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1162,56827,32921,[new BaseRewardVO(10000,30400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz106.json");
         this._battles[this._battles.length] = new MissionBattleVO(1163,57701,33124,[new BaseRewardVO(10000,30500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz105-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1164,58575,33327,[new BaseRewardVO(10000,30600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz105-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1165,59449,33530,[new BaseRewardVO(10000,30700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1166,60323,33733,[new BaseRewardVO(10000,30800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz110.json");
         this._battles[this._battles.length] = new MissionBattleVO(1167,61197,33936,[new BaseRewardVO(10000,30900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1168,62071,34139,[new BaseRewardVO(10000,31000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz112.json");
         this._battles[this._battles.length] = new MissionBattleVO(1169,62945,34342,[new BaseRewardVO(10000,31100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz111-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1170,63819,34545,[new BaseRewardVO(10000,31200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz114.json");
         this._battles[this._battles.length] = new MissionBattleVO(1171,64693,34748,[new BaseRewardVO(10000,31300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz113-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1172,65567,34951,[new BaseRewardVO(10000,31400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz116.json");
         this._battles[this._battles.length] = new MissionBattleVO(1173,66441,35154,[new BaseRewardVO(10000,31500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz115-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1174,67315,35357,[new BaseRewardVO(10000,31600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz118.json");
         this._battles[this._battles.length] = new MissionBattleVO(1175,68189,35560,[new BaseRewardVO(10000,31700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz117-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1176,69063,35763,[new BaseRewardVO(10000,31800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz105-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1177,69937,35966,[new BaseRewardVO(10000,31900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz104-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1178,70811,36169,[new BaseRewardVO(10000,32000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz101-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1179,71685,36372,[new BaseRewardVO(10000,32100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz102-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1180,72559,36575,[new BaseRewardVO(10000,32200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz103-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1181,73433,36778,[new BaseRewardVO(10000,32300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz104-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1182,74307,36981,[new BaseRewardVO(10000,32400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz105-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1183,75181,37184,[new BaseRewardVO(10000,32500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz106-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1184,76055,37387,[new BaseRewardVO(10000,32600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz107-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1185,76929,37590,[new BaseRewardVO(10000,32700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz108-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1186,77803,37793,[new BaseRewardVO(10000,32800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz109-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1187,78677,37996,[new BaseRewardVO(10000,32900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz110-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1188,79551,38199,[new BaseRewardVO(10000,33000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz111-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1189,80425,38402,[new BaseRewardVO(10000,33100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz112-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1190,81299,38605,[new BaseRewardVO(10000,33200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz113-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1191,82173,38808,[new BaseRewardVO(10000,33300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz114-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1192,83047,39011,[new BaseRewardVO(10000,33400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz115-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1193,83921,39214,[new BaseRewardVO(10000,33500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz116-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1194,84795,39417,[new BaseRewardVO(10000,33600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz117-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1195,85669,39620,[new BaseRewardVO(10000,33700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz118-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1196,86543,39823,[new BaseRewardVO(10000,33800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz119-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1197,87417,40026,[new BaseRewardVO(10000,33900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz120-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1198,88291,40229,[new BaseRewardVO(10000,34000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz101-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1199,89165,40432,[new BaseRewardVO(10000,34100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz102-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1200,90039,40635,[new BaseRewardVO(10000,34200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz103-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1201,90913,40838,[new BaseRewardVO(10000,34300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz104-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1202,91787,41041,[new BaseRewardVO(10000,34400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz105-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1203,92661,41244,[new BaseRewardVO(10000,34500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz106-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1204,93535,41447,[new BaseRewardVO(10000,34600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz107-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1205,94409,41650,[new BaseRewardVO(10000,34700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz108-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1206,95283,41853,[new BaseRewardVO(10000,34800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz109-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1207,96157,42056,[new BaseRewardVO(10000,34900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz110-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1208,97031,42259,[new BaseRewardVO(10000,35000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz111-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1209,97905,42462,[new BaseRewardVO(10000,35100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz112-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1210,98779,42665,[new BaseRewardVO(10000,35200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz113-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1211,99653,42868,[new BaseRewardVO(10000,35300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz114-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1212,100527,43071,[new BaseRewardVO(10000,35400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz115-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1213,101401,43274,[new BaseRewardVO(10000,35500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz116-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1214,102275,43477,[new BaseRewardVO(10000,35600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz117-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1215,103149,43680,[new BaseRewardVO(10000,35700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz118-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1216,104023,43883,[new BaseRewardVO(10000,35800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz119-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1217,104897,44086,[new BaseRewardVO(10000,35900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz120-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1218,105771,44289,[new BaseRewardVO(10000,36000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz101-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1219,106645,44492,[new BaseRewardVO(10000,36100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz102-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1220,107519,44695,[new BaseRewardVO(10000,36200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz103-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1221,108393,44898,[new BaseRewardVO(10000,36300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz104-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1222,109267,45101,[new BaseRewardVO(10000,36400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz105-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1223,110141,45304,[new BaseRewardVO(10000,36500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz106-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1224,111015,45507,[new BaseRewardVO(10000,36600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1225,111889,45710,[new BaseRewardVO(10000,36700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz108-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1226,112763,45913,[new BaseRewardVO(10000,36800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1227,113637,46116,[new BaseRewardVO(10000,36900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz110-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1228,114511,46319,[new BaseRewardVO(10000,37000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz111-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1229,115385,46522,[new BaseRewardVO(10000,37100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz112-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1230,116259,46725,[new BaseRewardVO(10000,37200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz113-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1231,117133,46928,[new BaseRewardVO(10000,37300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz114-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1232,118007,47131,[new BaseRewardVO(10000,37400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz115-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1233,118881,47334,[new BaseRewardVO(10000,37500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz116-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1234,119755,47537,[new BaseRewardVO(10000,37600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz117-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1235,120629,47740,[new BaseRewardVO(10000,37700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz118-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1236,121503,47943,[new BaseRewardVO(10000,37800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz119-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1237,122377,48146,[new BaseRewardVO(10000,37900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz120-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1238,123251,48349,[new BaseRewardVO(10000,38000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz101.json");
         this._battles[this._battles.length] = new MissionBattleVO(1239,124125,48552,[new BaseRewardVO(10000,38100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(1240,124999,48755,[new BaseRewardVO(10000,38200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz103.json");
         this._battles[this._battles.length] = new MissionBattleVO(1241,125873,48958,[new BaseRewardVO(10000,38300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(1242,126747,49161,[new BaseRewardVO(10000,38400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz105.json");
         this._battles[this._battles.length] = new MissionBattleVO(1243,127621,49364,[new BaseRewardVO(10000,38500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz106.json");
         this._battles[this._battles.length] = new MissionBattleVO(1244,128495,49567,[new BaseRewardVO(10000,38600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz107.json");
         this._battles[this._battles.length] = new MissionBattleVO(1245,129369,49770,[new BaseRewardVO(10000,38700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(1246,130243,49973,[new BaseRewardVO(10000,38800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz109.json");
         this._battles[this._battles.length] = new MissionBattleVO(1247,131117,50176,[new BaseRewardVO(10000,38900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz110.json");
         this._battles[this._battles.length] = new MissionBattleVO(1248,131991,50379,[new BaseRewardVO(10000,39000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz111.json");
         this._battles[this._battles.length] = new MissionBattleVO(1249,132865,50582,[new BaseRewardVO(10000,39100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz112.json");
         this._battles[this._battles.length] = new MissionBattleVO(1250,133739,50785,[new BaseRewardVO(10000,39200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz113.json");
         this._battles[this._battles.length] = new MissionBattleVO(1251,134613,50988,[new BaseRewardVO(10000,39300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz114.json");
         this._battles[this._battles.length] = new MissionBattleVO(1252,135487,51191,[new BaseRewardVO(10000,39400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz115-430.json");
         this._battles[this._battles.length] = new MissionBattleVO(1253,136361,51394,[new BaseRewardVO(10000,39500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz116.json");
         this._battles[this._battles.length] = new MissionBattleVO(1254,137235,51597,[new BaseRewardVO(10000,39600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz117.json");
         this._battles[this._battles.length] = new MissionBattleVO(1255,138109,51800,[new BaseRewardVO(10000,39700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz118.json");
         this._battles[this._battles.length] = new MissionBattleVO(1256,138983,52003,[new BaseRewardVO(10000,39800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz119-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1257,139857,52206,[new BaseRewardVO(10000,39900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz120-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2001,140731,48349,[new BaseRewardVO(10000,40000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH001-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2002,141605,48552,[new BaseRewardVO(10000,40100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH002-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2003,142479,48755,[new BaseRewardVO(10000,40200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH003-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2004,143353,48958,[new BaseRewardVO(10000,40300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH004-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2005,144227,49161,[new BaseRewardVO(10000,40400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH005-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2006,145101,49364,[new BaseRewardVO(10000,40500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH006-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2007,145975,49567,[new BaseRewardVO(10000,40600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH007-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2008,146849,49770,[new BaseRewardVO(10000,40700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH008-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2009,147723,49973,[new BaseRewardVO(10000,40800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH009-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2010,148597,50176,[new BaseRewardVO(10000,40900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH010-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2011,149471,50379,[new BaseRewardVO(10000,41000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH011-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2012,150345,50582,[new BaseRewardVO(10000,41100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH012-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2013,151219,50785,[new BaseRewardVO(10000,41200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH013-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2014,152093,50988,[new BaseRewardVO(10000,41300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH014-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2015,152967,51191,[new BaseRewardVO(10000,41400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH015-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2016,153841,51394,[new BaseRewardVO(10000,41500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH016-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2017,154715,51597,[new BaseRewardVO(10000,41600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH017-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2018,155589,51800,[new BaseRewardVO(10000,41700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH018-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2019,156463,52003,[new BaseRewardVO(10000,41800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH019-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2020,157337,52206,[new BaseRewardVO(10000,41900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH015-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2021,158211,52409,[new BaseRewardVO(10000,42000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH005-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2022,159085,52612,[new BaseRewardVO(10000,42100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH014-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2023,159959,52815,[new BaseRewardVO(10000,42200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH008-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2024,160833,53018,[new BaseRewardVO(10000,42300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH011-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2025,161707,53221,[new BaseRewardVO(10000,42400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH006-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2026,162581,53424,[new BaseRewardVO(10000,43500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH013-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2027,163455,53627,[new BaseRewardVO(10000,42600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH007-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2028,164329,53830,[new BaseRewardVO(10000,42700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH012-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2029,165203,54033,[new BaseRewardVO(10000,42800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH009-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2030,166077,54236,[new BaseRewardVO(10000,42900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH010-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2031,166951,54439,[new BaseRewardVO(10000,43000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH018-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2032,167825,54642,[new BaseRewardVO(10000,43100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH002-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2033,168699,54845,[new BaseRewardVO(10000,43200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH017-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2034,169573,55048,[new BaseRewardVO(10000,42300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH003-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2035,170447,55251,[new BaseRewardVO(10000,43400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH016-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2036,171321,55454,[new BaseRewardVO(10000,43500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH001-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2037,172195,55657,[new BaseRewardVO(10000,43600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH004-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2038,173036,55860,[new BaseRewardVO(10000,43600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2039,173943,56063,[new BaseRewardVO(10000,43700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2040,174850,56266,[new BaseRewardVO(10000,43800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH022-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2041,175757,56469,[new BaseRewardVO(10000,43900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH023-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2042,176664,56672,[new BaseRewardVO(10000,44000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH024-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2043,177571,56875,[new BaseRewardVO(10000,44100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH025-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2044,178478,57078,[new BaseRewardVO(10000,44200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH026-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2045,179385,57281,[new BaseRewardVO(10000,44300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH027-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2046,180292,57484,[new BaseRewardVO(10000,44400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH028-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2047,181199,57687,[new BaseRewardVO(10000,44500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH029-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2048,182106,57890,[new BaseRewardVO(10000,44600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH030-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2049,183013,58093,[new BaseRewardVO(10000,44700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH031-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2050,183920,58296,[new BaseRewardVO(10000,44800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH032-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2051,184827,58499,[new BaseRewardVO(10000,44900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH033-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2052,185734,58702,[new BaseRewardVO(10000,45000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH034-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2053,186641,58905,[new BaseRewardVO(10000,45100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH035-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2054,187548,59108,[new BaseRewardVO(10000,45200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH036-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2055,188455,59311,[new BaseRewardVO(10000,45300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH037-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2056,189362,59514,[new BaseRewardVO(10000,45400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH038-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2057,190269,59717,[new BaseRewardVO(10000,45400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH001-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2058,191176,59920,[new BaseRewardVO(10000,45500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2059,192083,60123,[new BaseRewardVO(10000,45600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH002-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2060,192990,60326,[new BaseRewardVO(10000,45700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH023-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2061,193897,60529,[new BaseRewardVO(10000,45800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH003-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2062,194804,60732,[new BaseRewardVO(10000,45900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH025-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2063,195711,60935,[new BaseRewardVO(10000,46000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH004-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2064,196618,61138,[new BaseRewardVO(10000,46100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH027-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2065,197525,61341,[new BaseRewardVO(10000,46200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH005-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2066,198432,61544,[new BaseRewardVO(10000,46300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH029-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2067,199339,61747,[new BaseRewardVO(10000,46400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH006-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2068,200246,61950,[new BaseRewardVO(10000,46500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH031-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2069,201153,62153,[new BaseRewardVO(10000,46600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH007-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2070,202060,62356,[new BaseRewardVO(10000,46700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH033-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2071,202967,62559,[new BaseRewardVO(10000,46800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH008-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2072,203874,62762,[new BaseRewardVO(10000,46900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH035-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2073,204781,62965,[new BaseRewardVO(10000,47000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH009-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2074,205688,63168,[new BaseRewardVO(10000,47100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH037-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2075,206595,63371,[new BaseRewardVO(10000,47200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2076,207502,63574,[new BaseRewardVO(10000,47300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH011-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2077,208409,63777,[new BaseRewardVO(10000,47400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH022-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2078,209316,63980,[new BaseRewardVO(10000,47500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH012-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2079,210223,64183,[new BaseRewardVO(10000,47600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH024-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2080,211130,64386,[new BaseRewardVO(10000,47700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH013-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2081,212037,64589,[new BaseRewardVO(10000,47800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH026-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2082,212944,64792,[new BaseRewardVO(10000,47900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH014-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2083,213851,64995,[new BaseRewardVO(10000,48000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH028-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2084,214758,65198,[new BaseRewardVO(10000,48100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH015-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2085,215665,65401,[new BaseRewardVO(10000,48200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH030-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2086,216572,65604,[new BaseRewardVO(10000,48300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH016-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2087,217479,65807,[new BaseRewardVO(10000,48400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH032-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2088,218386,66010,[new BaseRewardVO(10000,48500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH017-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2089,219293,66213,[new BaseRewardVO(10000,48600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH034-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2090,220200,66416,[new BaseRewardVO(10000,48700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH018-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2091,221107,66619,[new BaseRewardVO(10000,48800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH036-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2092,222014,66822,[new BaseRewardVO(10000,48900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH019-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2093,222921,67025,[new BaseRewardVO(10000,49000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH002-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2094,223828,67228,[new BaseRewardVO(10000,49100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH001-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2095,224735,67431,[new BaseRewardVO(10000,49200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH002-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2096,225642,67634,[new BaseRewardVO(10000,49300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH003-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2097,226549,67837,[new BaseRewardVO(10000,49400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH004-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2098,227456,68040,[new BaseRewardVO(10000,49500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH005-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2099,228363,68243,[new BaseRewardVO(10000,49600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH006-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2100,229270,68446,[new BaseRewardVO(10000,49700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH007-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2101,230177,68649,[new BaseRewardVO(10000,49800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH008-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2102,231084,68852,[new BaseRewardVO(10000,49900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH009-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2103,231991,69055,[new BaseRewardVO(10000,50000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2104,232898,69258,[new BaseRewardVO(10000,50100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2105,233805,69461,[new BaseRewardVO(10000,50200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH022-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2106,234712,69664,[new BaseRewardVO(10000,50300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH023-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2107,235619,69867,[new BaseRewardVO(10000,50400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH024-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2108,236526,70070,[new BaseRewardVO(10000,50500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH025-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2109,237433,70273,[new BaseRewardVO(10000,50600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH026-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2110,238340,70476,[new BaseRewardVO(10000,50700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH027-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2111,239247,70679,[new BaseRewardVO(10000,50800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH028-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2112,240154,70882,[new BaseRewardVO(10000,50900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz101.json");
         this._battles[this._battles.length] = new MissionBattleVO(2113,241061,71085,[new BaseRewardVO(10000,51000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(2114,241968,71288,[new BaseRewardVO(10000,51100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz103.json");
         this._battles[this._battles.length] = new MissionBattleVO(2115,242875,71491,[new BaseRewardVO(10000,51200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(2116,243782,71694,[new BaseRewardVO(10000,51300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz105.json");
         this._battles[this._battles.length] = new MissionBattleVO(2117,244689,71897,[new BaseRewardVO(10000,51400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz106.json");
         this._battles[this._battles.length] = new MissionBattleVO(2118,245596,72100,[new BaseRewardVO(10000,51500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz107.json");
         this._battles[this._battles.length] = new MissionBattleVO(2119,246503,72303,[new BaseRewardVO(10000,51600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(2120,247410,72506,[new BaseRewardVO(10000,51700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz109.json");
         this._battles[this._battles.length] = new MissionBattleVO(2121,248317,72709,[new BaseRewardVO(10000,51800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz110.json");
         this._battles[this._battles.length] = new MissionBattleVO(2122,249224,72912,[new BaseRewardVO(10000,51900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz111.json");
         this._battles[this._battles.length] = new MissionBattleVO(2123,250131,73115,[new BaseRewardVO(10000,52000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz112.json");
         this._battles[this._battles.length] = new MissionBattleVO(2124,251038,73318,[new BaseRewardVO(10000,52100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz113.json");
         this._battles[this._battles.length] = new MissionBattleVO(2125,251945,73521,[new BaseRewardVO(10000,52200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz114.json");
         this._battles[this._battles.length] = new MissionBattleVO(2126,252852,73724,[new BaseRewardVO(10000,52300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz115-430.json");
         this._battles[this._battles.length] = new MissionBattleVO(2127,253759,73927,[new BaseRewardVO(10000,52400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz116.json");
         this._battles[this._battles.length] = new MissionBattleVO(2128,254666,74130,[new BaseRewardVO(10000,52500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz117.json");
         this._battles[this._battles.length] = new MissionBattleVO(2129,255573,74333,[new BaseRewardVO(10000,52600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz118.json");
         this._battles[this._battles.length] = new MissionBattleVO(2130,256480,74536,[new BaseRewardVO(10000,52700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz119-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2131,257387,74739,[new BaseRewardVO(10000,52800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz101-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2132,258294,74942,[new BaseRewardVO(10000,52900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz102-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2133,259201,75145,[new BaseRewardVO(10000,53000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz103-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(2134,260108,75348,[new BaseRewardVO(10000,53100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz104-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2135,261015,75551,[new BaseRewardVO(10000,53200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz105-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2136,261922,75754,[new BaseRewardVO(10000,53300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz106-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(2137,262829,75957,[new BaseRewardVO(10000,53400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz107-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2138,263736,76160,[new BaseRewardVO(10000,53500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz108-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2139,264643,76363,[new BaseRewardVO(10000,53600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz109-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2140,265550,76566,[new BaseRewardVO(10000,53700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz110-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(2141,266457,76769,[new BaseRewardVO(10000,53800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz111-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2142,267364,76972,[new BaseRewardVO(10000,53900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz112-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(2143,268271,77175,[new BaseRewardVO(10000,54000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz113-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2144,269178,77378,[new BaseRewardVO(10000,54100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz114-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(2145,270085,77581,[new BaseRewardVO(10000,54200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz115-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2146,270992,77784,[new BaseRewardVO(10000,54300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz116-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(2147,271899,77987,[new BaseRewardVO(10000,54400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz117-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2148,272806,78190,[new BaseRewardVO(10000,54500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz118-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2149,273713,78393,[new BaseRewardVO(10000,54600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2150,274620,78596,[new BaseRewardVO(10000,54700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2151,275527,78799,[new BaseRewardVO(10000,54800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH022-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2152,276434,79002,[new BaseRewardVO(10000,54900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH023-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2153,277341,79205,[new BaseRewardVO(10000,55000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH024-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2154,278248,79408,[new BaseRewardVO(10000,55100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH025-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2155,279155,79611,[new BaseRewardVO(10000,55200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH026-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2156,280062,79814,[new BaseRewardVO(10000,55300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH027-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2157,280969,80017,[new BaseRewardVO(10000,55400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH028-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2158,281876,80220,[new BaseRewardVO(10000,55500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH029-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2159,282783,80423,[new BaseRewardVO(10000,55600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH030-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2160,283690,80626,[new BaseRewardVO(10000,55700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH031-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2161,284597,80829,[new BaseRewardVO(10000,55800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH032-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2162,285504,81032,[new BaseRewardVO(10000,55900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH033-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2163,286411,81235,[new BaseRewardVO(10000,56000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH034-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2164,287318,81438,[new BaseRewardVO(10000,56100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH035-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2165,288225,81641,[new BaseRewardVO(10000,56200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH036-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2166,289132,81844,[new BaseRewardVO(10000,56300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH037-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2167,290039,82047,[new BaseRewardVO(10000,56400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH038-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2168,290946,82250,[new BaseRewardVO(10000,56500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH032-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2169,291853,82453,[new BaseRewardVO(10000,56600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH017-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2170,292760,82656,[new BaseRewardVO(10000,56700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH034-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2171,293667,82859,[new BaseRewardVO(10000,56800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH018-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2172,294574,83062,[new BaseRewardVO(10000,56900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH036-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2173,295481,83265,[new BaseRewardVO(10000,57000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH019-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2174,296388,83468,[new BaseRewardVO(10000,57100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH002-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2175,297295,83671,[new BaseRewardVO(10000,57200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH001-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2176,298202,83874,[new BaseRewardVO(10000,57300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH002-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2177,299109,84077,[new BaseRewardVO(10000,57400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH003-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2178,300016,84280,[new BaseRewardVO(10000,57500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH004-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2179,300923,84483,[new BaseRewardVO(10000,57600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH005-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2180,301830,84686,[new BaseRewardVO(10000,57700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH006-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2181,302737,84889,[new BaseRewardVO(10000,57800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH007-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2182,303644,85092,[new BaseRewardVO(10000,57900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH008-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2183,304551,85295,[new BaseRewardVO(10000,58000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH009-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2184,305458,85498,[new BaseRewardVO(10000,58100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2185,306365,85701,[new BaseRewardVO(10000,58200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2186,307272,85904,[new BaseRewardVO(10000,58300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz101-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2187,308179,86107,[new BaseRewardVO(10000,58400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(2188,309086,86310,[new BaseRewardVO(10000,58500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz103-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2189,309993,86513,[new BaseRewardVO(10000,58600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(2190,310900,86716,[new BaseRewardVO(10000,58700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz105-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2191,311807,86919,[new BaseRewardVO(10000,58800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz106.json");
         this._battles[this._battles.length] = new MissionBattleVO(2192,312714,87122,[new BaseRewardVO(10000,58900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2193,313621,87325,[new BaseRewardVO(10000,59000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(2194,314528,87528,[new BaseRewardVO(10000,59100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2195,315435,87731,[new BaseRewardVO(10000,59200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz110.json");
         this._battles[this._battles.length] = new MissionBattleVO(2196,316342,87934,[new BaseRewardVO(10000,59300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz111-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2197,317249,88137,[new BaseRewardVO(10000,59400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz112.json");
         this._battles[this._battles.length] = new MissionBattleVO(2198,318156,88340,[new BaseRewardVO(10000,59500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz113-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2199,319063,88543,[new BaseRewardVO(10000,59600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz114.json");
         this._battles[this._battles.length] = new MissionBattleVO(2200,319970,88746,[new BaseRewardVO(10000,59700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz115-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2201,320877,88949,[new BaseRewardVO(10000,59800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz116.json");
         this._battles[this._battles.length] = new MissionBattleVO(2202,321784,89152,[new BaseRewardVO(10000,59900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2203,322691,89355,[new BaseRewardVO(10000,60000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz118.json");
         this._battles[this._battles.length] = new MissionBattleVO(2204,323598,89558,[new BaseRewardVO(10000,60100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2205,324000,89761,[new BaseRewardVO(10000,60200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2206,324000,89964,[new BaseRewardVO(10000,60300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2207,324000,90167,[new BaseRewardVO(10000,60400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH022-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2208,324000,90370,[new BaseRewardVO(10000,60500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH023-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2209,324000,90573,[new BaseRewardVO(10000,60600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH024-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2210,324000,90776,[new BaseRewardVO(10000,60700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH025-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2211,324000,90979,[new BaseRewardVO(10000,60800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH026-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2212,324000,91182,[new BaseRewardVO(10000,60900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH027-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2213,324000,91385,[new BaseRewardVO(10000,61000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH028-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2214,324000,91588,[new BaseRewardVO(10000,61100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH029-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2215,324000,91791,[new BaseRewardVO(10000,61200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH030-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2216,324000,91994,[new BaseRewardVO(10000,61300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH031-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2217,324000,92197,[new BaseRewardVO(10000,61400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH032-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2218,324000,92400,[new BaseRewardVO(10000,61500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH033-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2219,324000,92603,[new BaseRewardVO(10000,61600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH034-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2220,324000,92806,[new BaseRewardVO(10000,61700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH035-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2221,324000,93009,[new BaseRewardVO(10000,61800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH036-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2222,324000,93212,[new BaseRewardVO(10000,61900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH037-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2223,324000,93415,[new BaseRewardVO(10000,62000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2224,324000,93618,[new BaseRewardVO(10000,62100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2225,324000,93821,[new BaseRewardVO(10000,62200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH022-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2226,324000,94024,[new BaseRewardVO(10000,62300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH023-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2227,324000,94227,[new BaseRewardVO(10000,62400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH024-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2228,324000,94430,[new BaseRewardVO(10000,62500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH025-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2229,324000,94633,[new BaseRewardVO(10000,62600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH026-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2230,324000,94836,[new BaseRewardVO(10000,62700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH027-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2231,324000,95039,[new BaseRewardVO(10000,62800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH028-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2232,324000,95242,[new BaseRewardVO(10000,62900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH029-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2233,324000,95445,[new BaseRewardVO(10000,63000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH030-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2234,324000,95648,[new BaseRewardVO(10000,63100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH031-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2235,324000,95851,[new BaseRewardVO(10000,63200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH032-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2236,324000,96054,[new BaseRewardVO(10000,63300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH033-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2237,324000,96257,[new BaseRewardVO(10000,63400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH034-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2238,324000,96460,[new BaseRewardVO(10000,63500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz101.json");
         this._battles[this._battles.length] = new MissionBattleVO(2239,324000,96663,[new BaseRewardVO(10000,63600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(2240,324000,96866,[new BaseRewardVO(10000,63700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz103.json");
         this._battles[this._battles.length] = new MissionBattleVO(2241,324000,97069,[new BaseRewardVO(10000,63800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(2242,324000,97272,[new BaseRewardVO(10000,63900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz105.json");
         this._battles[this._battles.length] = new MissionBattleVO(2243,324000,97475,[new BaseRewardVO(10000,64000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz106.json");
         this._battles[this._battles.length] = new MissionBattleVO(2244,324000,97678,[new BaseRewardVO(10000,64100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz107.json");
         this._battles[this._battles.length] = new MissionBattleVO(2245,324000,97881,[new BaseRewardVO(10000,64200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(2246,324000,98084,[new BaseRewardVO(10000,64300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz109.json");
         this._battles[this._battles.length] = new MissionBattleVO(2247,324000,98287,[new BaseRewardVO(10000,64400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz110.json");
         this._battles[this._battles.length] = new MissionBattleVO(2248,324000,98490,[new BaseRewardVO(10000,64500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz111.json");
         this._battles[this._battles.length] = new MissionBattleVO(2249,324000,98693,[new BaseRewardVO(10000,64600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz112.json");
         this._battles[this._battles.length] = new MissionBattleVO(2250,324000,98896,[new BaseRewardVO(10000,64700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz113.json");
         this._battles[this._battles.length] = new MissionBattleVO(2251,324000,99099,[new BaseRewardVO(10000,64800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz114.json");
         this._battles[this._battles.length] = new MissionBattleVO(2252,324000,99302,[new BaseRewardVO(10000,64900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz115-430.json");
         this._battles[this._battles.length] = new MissionBattleVO(2253,324000,99505,[new BaseRewardVO(10000,65000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz119-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2254,324000,99708,[new BaseRewardVO(10000,65100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz101-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2255,324000,99911,[new BaseRewardVO(10000,65200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz102-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2256,324000,100114,[new BaseRewardVO(10000,65300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz103-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(2257,324000,100317,[new BaseRewardVO(10000,65400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz104-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2258,324000,100520,[new BaseRewardVO(10000,65500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz105-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2259,324000,100723,[new BaseRewardVO(10000,65600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz106-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(2260,324000,100926,[new BaseRewardVO(10000,65700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz107-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2261,324000,101129,[new BaseRewardVO(10000,65800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz108-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2262,324000,101332,[new BaseRewardVO(10000,65900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz109-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2263,324000,101535,[new BaseRewardVO(10000,66000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz110-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(2264,324000,101738,[new BaseRewardVO(10000,66100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz111-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2265,324000,101941,[new BaseRewardVO(10000,66200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz112-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(2266,324000,102144,[new BaseRewardVO(10000,66300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz113-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2267,324000,102347,[new BaseRewardVO(10000,66400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/qinz114-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(2268,324000,102550,[new BaseRewardVO(10000,66500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH036-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2269,324000,102753,[new BaseRewardVO(10000,66600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH037-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2270,324000,102956,[new BaseRewardVO(10000,66700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2271,324000,103159,[new BaseRewardVO(10000,66800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH011-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2272,324000,103362,[new BaseRewardVO(10000,66900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH022-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2273,324000,103565,[new BaseRewardVO(10000,67000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH012-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2274,324000,103768,[new BaseRewardVO(10000,67100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH024-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2275,324000,103971,[new BaseRewardVO(10000,67200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH013-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2276,324000,104174,[new BaseRewardVO(10000,67300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH026-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2277,324000,104377,[new BaseRewardVO(10000,67400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH014-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2278,324000,104580,[new BaseRewardVO(10000,67500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH028-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2279,324000,104783,[new BaseRewardVO(10000,67600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH015-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2280,324000,104986,[new BaseRewardVO(10000,67700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH030-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2281,324000,105189,[new BaseRewardVO(10000,67800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH016-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2282,324000,105392,[new BaseRewardVO(10000,67900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH032-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2283,324000,105595,[new BaseRewardVO(10000,68000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH003-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2284,324000,105798,[new BaseRewardVO(10000,68100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH004-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2285,324000,106001,[new BaseRewardVO(10000,68200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH005-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2286,324000,106204,[new BaseRewardVO(10000,68300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH006-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2287,324000,106407,[new BaseRewardVO(10000,68400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH007-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2288,324000,106610,[new BaseRewardVO(10000,68500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH008-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2289,324000,106813,[new BaseRewardVO(10000,68600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH009-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2290,324000,107016,[new BaseRewardVO(10000,68700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2291,324000,107219,[new BaseRewardVO(10000,68800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2292,324000,107422,[new BaseRewardVO(10000,68900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz101-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2293,324000,107625,[new BaseRewardVO(10000,69000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(2294,324000,107828,[new BaseRewardVO(10000,69100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz103-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2295,324000,108031,[new BaseRewardVO(10000,69200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(2296,324000,108234,[new BaseRewardVO(10000,69300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz113-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2297,324000,108437,[new BaseRewardVO(10000,69400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2298,324000,108640,[new BaseRewardVO(10000,69500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH033-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2299,324000,108843,[new BaseRewardVO(10000,69600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH034-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2300,324000,109046,[new BaseRewardVO(10000,69700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH035-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2301,324000,109249,[new BaseRewardVO(10000,69800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH036-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2302,324000,109452,[new BaseRewardVO(10000,69900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH037-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2303,324000,109655,[new BaseRewardVO(10000,70000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2304,324000,109858,[new BaseRewardVO(10000,70100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2305,324000,110061,[new BaseRewardVO(10000,70200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH022-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2306,324000,110264,[new BaseRewardVO(10000,70300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH023-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2307,324000,110467,[new BaseRewardVO(10000,70400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH024-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2308,324000,110670,[new BaseRewardVO(10000,70500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH025-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2309,324000,110873,[new BaseRewardVO(10000,70600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH026-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2310,324000,111076,[new BaseRewardVO(10000,70700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH027-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2311,324000,111279,[new BaseRewardVO(10000,70800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH028-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2312,324000,111482,[new BaseRewardVO(10000,70900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH029-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2313,324000,111685,[new BaseRewardVO(10000,71000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2314,324000,111888,[new BaseRewardVO(10000,71100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(2315,324000,112091,[new BaseRewardVO(10000,71200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2316,324000,112294,[new BaseRewardVO(10000,71300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz110.json");
         this._battles[this._battles.length] = new MissionBattleVO(2317,324000,112497,[new BaseRewardVO(10000,71400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz111-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2318,324000,112700,[new BaseRewardVO(10000,71500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz112.json");
         this._battles[this._battles.length] = new MissionBattleVO(2319,324000,112903,[new BaseRewardVO(10000,71600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz113-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2320,324000,113106,[new BaseRewardVO(10000,71700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz114.json");
         this._battles[this._battles.length] = new MissionBattleVO(2321,324000,113309,[new BaseRewardVO(10000,71800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz115-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2322,324000,113512,[new BaseRewardVO(10000,71900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz116.json");
         this._battles[this._battles.length] = new MissionBattleVO(2323,324000,113715,[new BaseRewardVO(10000,72000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2324,324000,113918,[new BaseRewardVO(10000,72100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz118.json");
         this._battles[this._battles.length] = new MissionBattleVO(2325,324000,114121,[new BaseRewardVO(10000,72200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2326,324000,114324,[new BaseRewardVO(10000,72300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2327,324000,114527,[new BaseRewardVO(10000,72400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2328,324000,114730,[new BaseRewardVO(10000,72500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH008-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2329,324000,114933,[new BaseRewardVO(10000,72600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH009-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2330,324000,115136,[new BaseRewardVO(10000,72700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2331,324000,115339,[new BaseRewardVO(10000,72800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2332,324000,115542,[new BaseRewardVO(10000,72900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH022-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2333,324000,115745,[new BaseRewardVO(10000,73000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH023-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2334,324000,115948,[new BaseRewardVO(10000,73100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH024-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2335,324000,116151,[new BaseRewardVO(10000,73200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH025-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2336,324000,116354,[new BaseRewardVO(10000,73300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH026-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2337,324000,116557,[new BaseRewardVO(10000,73400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH027-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2338,324000,116760,[new BaseRewardVO(10000,73500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH028-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2339,324000,116963,[new BaseRewardVO(10000,73600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz101.json");
         this._battles[this._battles.length] = new MissionBattleVO(2340,324000,117166,[new BaseRewardVO(10000,73700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(2341,324000,117369,[new BaseRewardVO(10000,73800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz103.json");
         this._battles[this._battles.length] = new MissionBattleVO(2342,324000,117572,[new BaseRewardVO(10000,73900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(2343,324000,117775,[new BaseRewardVO(10000,74000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH031-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2344,324000,117978,[new BaseRewardVO(10000,74100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH032-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2345,324000,118181,[new BaseRewardVO(10000,74200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH033-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2346,324000,118384,[new BaseRewardVO(10000,74300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH034-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2347,324000,118587,[new BaseRewardVO(10000,74400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH035-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2348,324000,118790,[new BaseRewardVO(10000,74500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH036-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2349,324000,118993,[new BaseRewardVO(10000,74600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH037-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2350,324000,119196,[new BaseRewardVO(10000,74700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2351,324000,119399,[new BaseRewardVO(10000,74800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH011-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2352,324000,119602,[new BaseRewardVO(10000,74900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH022-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2353,324000,119805,[new BaseRewardVO(10000,75000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH012-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2354,324000,120008,[new BaseRewardVO(10000,75100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH024-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2355,324000,120211,[new BaseRewardVO(10000,75200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH013-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2356,324000,120414,[new BaseRewardVO(10000,75300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH026-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2357,324000,120617,[new BaseRewardVO(10000,75400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH014-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2358,324000,120820,[new BaseRewardVO(10000,75500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH026-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2359,324000,121023,[new BaseRewardVO(10000,75600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH027-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2360,324000,121226,[new BaseRewardVO(10000,75700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH028-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2361,324000,121429,[new BaseRewardVO(10000,75800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH029-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2362,324000,121632,[new BaseRewardVO(10000,75900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH030-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2363,324000,121835,[new BaseRewardVO(10000,76000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH031-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2364,324000,122038,[new BaseRewardVO(10000,76100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH032-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2365,324000,122241,[new BaseRewardVO(10000,76200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH033-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2366,324000,122444,[new BaseRewardVO(10000,76300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH034-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2367,324000,122647,[new BaseRewardVO(10000,76400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2368,324000,122850,[new BaseRewardVO(10000,76500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(2369,324000,123053,[new BaseRewardVO(10000,76600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2370,324000,123256,[new BaseRewardVO(10000,76700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz110.json");
         this._battles[this._battles.length] = new MissionBattleVO(2371,324000,123459,[new BaseRewardVO(10000,76800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz111-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2372,324000,123662,[new BaseRewardVO(10000,76900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz112.json");
         this._battles[this._battles.length] = new MissionBattleVO(2373,324000,123865,[new BaseRewardVO(10000,77000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH007-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2374,324000,124068,[new BaseRewardVO(10000,77100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH008-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2375,324000,124271,[new BaseRewardVO(10000,77200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH009-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2376,324000,124474,[new BaseRewardVO(10000,77300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2377,324000,124677,[new BaseRewardVO(10000,77400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2378,324000,124880,[new BaseRewardVO(10000,77500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz101-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2379,324000,125083,[new BaseRewardVO(10000,77600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(2380,324000,125286,[new BaseRewardVO(10000,77700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz103-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2381,324000,125489,[new BaseRewardVO(10000,77800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(2382,324000,125692,[new BaseRewardVO(10000,77900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz105-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2383,324000,125895,[new BaseRewardVO(10000,78000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz106.json");
         this._battles[this._battles.length] = new MissionBattleVO(2384,324000,126098,[new BaseRewardVO(10000,78100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2385,324000,126301,[new BaseRewardVO(10000,78200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(2386,324000,126504,[new BaseRewardVO(10000,78300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2387,324000,126707,[new BaseRewardVO(10000,78400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz106.json");
         this._battles[this._battles.length] = new MissionBattleVO(2388,324000,126910,[new BaseRewardVO(10000,78500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(2389,324000,127113,[new BaseRewardVO(10000,78600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz103.json");
         this._battles[this._battles.length] = new MissionBattleVO(2390,324000,127316,[new BaseRewardVO(10000,78700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(2391,324000,127519,[new BaseRewardVO(10000,78800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH031-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2392,324000,127722,[new BaseRewardVO(10000,78900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH032-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2393,324000,127925,[new BaseRewardVO(10000,79000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH024-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2394,324000,128128,[new BaseRewardVO(10000,79100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH013-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2395,324000,128331,[new BaseRewardVO(10000,79200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2396,324000,128534,[new BaseRewardVO(10000,79300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(2397,324000,128737,[new BaseRewardVO(10000,79400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2398,324000,128940,[new BaseRewardVO(10000,79500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz110.json");
         this._battles[this._battles.length] = new MissionBattleVO(2399,324000,129143,[new BaseRewardVO(10000,79600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz111-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2400,324000,129346,[new BaseRewardVO(10000,79700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz112.json");
         this._battles[this._battles.length] = new MissionBattleVO(2401,324000,129549,[new BaseRewardVO(10000,79800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz113-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2402,324000,129752,[new BaseRewardVO(10000,79900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz114.json");
         this._battles[this._battles.length] = new MissionBattleVO(2403,324000,129955,[new BaseRewardVO(10000,80000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH027-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2404,324000,130158,[new BaseRewardVO(10000,80100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH028-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2405,324000,130361,[new BaseRewardVO(10000,80200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH029-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2406,324000,130564,[new BaseRewardVO(10000,80300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH030-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2407,324000,130767,[new BaseRewardVO(10000,80400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH031-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2408,324000,130970,[new BaseRewardVO(10000,80500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH032-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2409,324000,131173,[new BaseRewardVO(10000,80600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH033-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2410,324000,131376,[new BaseRewardVO(10000,80700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH034-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2411,324000,131579,[new BaseRewardVO(10000,80800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz101.json");
         this._battles[this._battles.length] = new MissionBattleVO(2412,324000,131782,[new BaseRewardVO(10000,80900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(2413,324000,131985,[new BaseRewardVO(10000,81000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz103.json");
         this._battles[this._battles.length] = new MissionBattleVO(2414,324000,132188,[new BaseRewardVO(10000,81100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(2415,324000,132391,[new BaseRewardVO(10000,81200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz105.json");
         this._battles[this._battles.length] = new MissionBattleVO(2416,324000,132594,[new BaseRewardVO(10000,81300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH003-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2417,324000,132797,[new BaseRewardVO(10000,81400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz103-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2418,324000,133000,[new BaseRewardVO(10000,81500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH029-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2419,324000,133203,[new BaseRewardVO(10000,81600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH030-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2420,324000,133406,[new BaseRewardVO(10000,81700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH031-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2421,324000,133609,[new BaseRewardVO(10000,81800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH032-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2422,324000,133812,[new BaseRewardVO(10000,81900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH033-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2423,324000,134015,[new BaseRewardVO(10000,82000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH034-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2424,324000,134218,[new BaseRewardVO(10000,82100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2425,324000,134421,[new BaseRewardVO(10000,82200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(2426,324000,134624,[new BaseRewardVO(10000,82300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2427,324000,134827,[new BaseRewardVO(10000,82400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz110.json");
         this._battles[this._battles.length] = new MissionBattleVO(2428,324000,135030,[new BaseRewardVO(10000,82500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz111-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2429,324000,135233,[new BaseRewardVO(10000,82600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz112.json");
         this._battles[this._battles.length] = new MissionBattleVO(2430,324000,135436,[new BaseRewardVO(10000,82700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz101.json");
         this._battles[this._battles.length] = new MissionBattleVO(2431,324000,135639,[new BaseRewardVO(10000,82800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(2432,324000,135842,[new BaseRewardVO(10000,82900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz103.json");
         this._battles[this._battles.length] = new MissionBattleVO(2433,324000,136045,[new BaseRewardVO(10000,83000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH008-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2434,324000,136248,[new BaseRewardVO(10000,83100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH009-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2435,324000,136451,[new BaseRewardVO(10000,83200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2436,324000,136654,[new BaseRewardVO(10000,83300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2437,324000,136857,[new BaseRewardVO(10000,83400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz101-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2438,324000,137060,[new BaseRewardVO(10000,83500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(2439,324000,137263,[new BaseRewardVO(10000,83600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz103-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2440,324000,137466,[new BaseRewardVO(10000,83700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(2441,324000,137669,[new BaseRewardVO(10000,83800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz113-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2442,324000,137872,[new BaseRewardVO(10000,83900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2443,324000,138075,[new BaseRewardVO(10000,84000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH033-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2444,324000,138278,[new BaseRewardVO(10000,84100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH034-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2445,324000,138481,[new BaseRewardVO(10000,84200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH035-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2446,324000,138684,[new BaseRewardVO(10000,84300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH036-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2447,324000,138887,[new BaseRewardVO(10000,84400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH011-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2448,324000,139090,[new BaseRewardVO(10000,84500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz105-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2449,324000,139293,[new BaseRewardVO(10000,84600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz106.json");
         this._battles[this._battles.length] = new MissionBattleVO(2450,324000,139496,[new BaseRewardVO(10000,84700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2451,324000,139699,[new BaseRewardVO(10000,84800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(2452,324000,139902,[new BaseRewardVO(10000,84900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2453,324000,140105,[new BaseRewardVO(10000,85000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz106.json");
         this._battles[this._battles.length] = new MissionBattleVO(2454,324000,140308,[new BaseRewardVO(10000,85100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(2455,324000,140511,[new BaseRewardVO(10000,85200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz103.json");
         this._battles[this._battles.length] = new MissionBattleVO(2456,324000,140714,[new BaseRewardVO(10000,85300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(2457,324000,140917,[new BaseRewardVO(10000,85400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH031-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2458,324000,141120,[new BaseRewardVO(10000,85500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH032-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2459,324000,141323,[new BaseRewardVO(10000,85600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH024-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2460,324000,141526,[new BaseRewardVO(10000,85700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH025-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2461,324000,141729,[new BaseRewardVO(10000,85800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH026-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2462,324000,141932,[new BaseRewardVO(10000,85900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH027-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2463,324000,142135,[new BaseRewardVO(10000,86000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH028-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2464,324000,142338,[new BaseRewardVO(10000,86100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH029-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2465,324000,142541,[new BaseRewardVO(10000,86200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2466,324000,142744,[new BaseRewardVO(10000,86300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(2467,324000,142947,[new BaseRewardVO(10000,86400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2468,324000,143150,[new BaseRewardVO(10000,86500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz110.json");
         this._battles[this._battles.length] = new MissionBattleVO(2469,324000,143353,[new BaseRewardVO(10000,86600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz111-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2470,324000,143556,[new BaseRewardVO(10000,86700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz112.json");
         this._battles[this._battles.length] = new MissionBattleVO(2471,324000,143759,[new BaseRewardVO(10000,86800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz113-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2472,324000,143962,[new BaseRewardVO(10000,86900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH006-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2473,324000,144165,[new BaseRewardVO(10000,87000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH007-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2474,324000,144368,[new BaseRewardVO(10000,87100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH008-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2475,324000,144571,[new BaseRewardVO(10000,87200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH009-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2476,324000,144774,[new BaseRewardVO(10000,87300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2477,324000,144977,[new BaseRewardVO(10000,87400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2478,324000,145180,[new BaseRewardVO(10000,87500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz101-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2479,324000,145383,[new BaseRewardVO(10000,87600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(2480,324000,145586,[new BaseRewardVO(10000,87700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz103-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2481,324000,145789,[new BaseRewardVO(10000,87800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(2482,324000,145992,[new BaseRewardVO(10000,87900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz113-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2483,324000,146195,[new BaseRewardVO(10000,88000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2484,324000,146398,[new BaseRewardVO(10000,88100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2485,324000,146601,[new BaseRewardVO(10000,88200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(2486,324000,146804,[new BaseRewardVO(10000,88300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2487,324000,147007,[new BaseRewardVO(10000,88400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz106.json");
         this._battles[this._battles.length] = new MissionBattleVO(2488,324000,147210,[new BaseRewardVO(10000,88500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(2489,324000,147413,[new BaseRewardVO(10000,88600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz103.json");
         this._battles[this._battles.length] = new MissionBattleVO(2490,324000,147616,[new BaseRewardVO(10000,88700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(2491,324000,147819,[new BaseRewardVO(10000,88800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH031-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2492,324000,148022,[new BaseRewardVO(10000,88900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH032-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2493,324000,148225,[new BaseRewardVO(10000,89000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH024-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2494,324000,148428,[new BaseRewardVO(10000,89100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH013-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2495,324000,148631,[new BaseRewardVO(10000,89200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2496,324000,148834,[new BaseRewardVO(10000,89300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH032-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2497,324000,149037,[new BaseRewardVO(10000,89400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH033-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2498,324000,149240,[new BaseRewardVO(10000,89500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH034-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2499,324000,149443,[new BaseRewardVO(10000,89600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2500,324000,149646,[new BaseRewardVO(10000,89700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(2501,324000,149849,[new BaseRewardVO(10000,89800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2502,324000,150052,[new BaseRewardVO(10000,89900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz110.json");
         this._battles[this._battles.length] = new MissionBattleVO(2503,324000,150255,[new BaseRewardVO(10000,90000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz111-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2504,324000,150458,[new BaseRewardVO(10000,90100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz112.json");
         this._battles[this._battles.length] = new MissionBattleVO(2505,324000,150661,[new BaseRewardVO(10000,90200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH007-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2506,324000,150864,[new BaseRewardVO(10000,90300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH008-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2507,324000,151067,[new BaseRewardVO(10000,90400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH009-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2508,324000,151270,[new BaseRewardVO(10000,90500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2509,324000,151473,[new BaseRewardVO(10000,90600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2510,324000,151676,[new BaseRewardVO(10000,90700),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz101-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2511,324000,151879,[new BaseRewardVO(10000,90800),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(2512,324000,152082,[new BaseRewardVO(10000,90900),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz103-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2513,324000,152285,[new BaseRewardVO(10000,91000),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(2514,324000,152488,[new BaseRewardVO(10000,91100),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz113-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2515,324000,152691,[new BaseRewardVO(10000,91200),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2516,324000,152894,[new BaseRewardVO(10000,91300),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH033-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2517,324000,153097,[new BaseRewardVO(10000,91400),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH034-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2518,324000,153300,[new BaseRewardVO(10000,91500),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH035-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2519,324000,153503,[new BaseRewardVO(10000,91600),new BaseRewardVO(11002,1),new BaseRewardVO(11005,1)],[],"scene/DH036-580.json");
      }
      
      private function initWave() : void
      {
         this._dic = new Vector.<WaveDataVO>();
         this._dic[this._dic.length] = new NormalWave1000().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1001().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1002().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1003().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1004().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1005().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1006().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1007().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1008().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1009().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1010().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1011().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1012().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1013().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1014().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1015().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1016().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1017().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1018().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1019().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1020().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1021().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1022().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1023().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1024().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1025().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1026().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1027().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1028().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1029().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1030().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1031().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1032().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1033().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1034().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1035().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1036().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1037().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1038().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1039().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1040().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1041().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1042().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1043().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1044().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1045().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1046().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1047().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1048().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1049().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1050().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1051().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1052().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1053().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1054().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1055().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1056().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1057().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1058().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1059().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1060().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1061().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1062().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1063().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1064().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1065().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1066().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1067().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1068().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1069().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1070().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1071().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1072().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1073().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1074().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1075().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1076().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1077().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1078().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1079().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1080().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1081().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1082().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1083().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1084().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1085().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1086().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1087().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1088().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1089().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1090().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1091().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1092().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1093().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1094().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1095().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1096().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1097().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1098().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1099().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1100().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1101().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1102().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1103().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1104().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1105().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1106().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1107().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1108().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1109().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1110().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1111().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1112().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1113().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1114().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1115().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1116().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1117().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1118().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1119().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1120().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1121().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1122().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1123().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1124().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1125().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1126().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1127().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1128().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1129().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1130().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1131().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1132().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1133().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1134().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1135().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1136().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1137().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1138().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1139().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1140().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1141().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1142().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1143().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1144().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1145().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1146().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1147().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1148().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1149().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1150().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1151().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1152().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1153().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1154().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1155().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1156().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1157().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1158().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1159().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1160().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1161().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1162().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1163().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1164().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1165().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1166().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1167().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1168().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1169().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1170().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1171().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1172().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1173().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1174().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1175().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1176().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1177().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1178().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1179().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1180().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1181().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1182().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1183().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1184().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1185().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1186().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1187().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1188().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1189().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1190().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1191().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1192().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1193().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1194().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1195().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1196().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1197().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1198().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1199().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1200().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1201().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1202().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1203().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1204().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1205().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1206().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1207().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1208().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1209().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1210().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1211().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1212().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1213().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1214().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1215().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1216().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1217().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1218().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1219().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1220().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1221().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1222().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1223().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1224().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1225().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1226().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1227().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1228().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1229().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1230().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1231().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1232().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1233().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1234().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1235().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1236().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1237().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1238().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1239().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1240().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1241().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1242().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1243().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1244().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1245().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1246().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1247().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1248().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1249().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1250().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1251().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1252().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1253().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1254().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1255().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1256().waveDataVO;
         this._dic[this._dic.length] = new NormalWave1257().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2001().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2002().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2003().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2004().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2005().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2006().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2007().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2008().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2009().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2010().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2011().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2012().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2013().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2014().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2015().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2016().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2017().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2018().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2019().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2020().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2021().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2022().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2023().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2024().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2025().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2026().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2027().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2028().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2029().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2030().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2031().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2032().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2033().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2034().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2035().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2036().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2037().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2038().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2039().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2040().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2041().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2042().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2043().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2044().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2045().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2046().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2047().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2048().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2049().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2050().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2051().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2052().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2053().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2054().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2055().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2056().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2057().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2058().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2059().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2060().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2061().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2062().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2063().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2064().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2065().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2066().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2067().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2068().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2069().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2070().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2071().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2072().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2073().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2074().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2075().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2076().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2077().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2078().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2079().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2080().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2081().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2082().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2083().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2084().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2085().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2086().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2087().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2088().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2089().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2090().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2091().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2092().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2093().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2094().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2095().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2096().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2097().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2098().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2099().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2100().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2101().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2102().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2103().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2104().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2105().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2106().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2107().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2108().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2109().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2110().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2111().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2112().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2113().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2114().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2115().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2116().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2117().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2118().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2119().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2120().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2121().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2122().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2123().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2124().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2125().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2126().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2127().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2128().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2129().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2130().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2131().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2132().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2133().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2134().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2135().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2136().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2137().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2138().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2139().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2140().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2141().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2142().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2143().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2144().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2145().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2146().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2147().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2148().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2149().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2150().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2151().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2152().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2153().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2154().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2155().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2156().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2157().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2158().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2159().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2160().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2161().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2162().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2163().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2164().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2165().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2166().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2167().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2168().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2169().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2170().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2171().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2172().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2173().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2174().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2175().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2176().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2177().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2178().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2179().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2180().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2181().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2182().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2183().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2184().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2185().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2186().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2187().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2188().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2189().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2190().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2191().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2192().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2193().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2194().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2195().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2196().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2197().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2198().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2199().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2200().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2201().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2202().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2203().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2204().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2205().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2206().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2207().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2208().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2209().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2210().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2211().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2212().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2213().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2214().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2215().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2216().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2217().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2218().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2219().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2220().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2221().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2222().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2223().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2224().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2225().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2226().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2227().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2228().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2229().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2230().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2231().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2232().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2233().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2234().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2235().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2236().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2237().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2238().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2239().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2240().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2241().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2242().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2243().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2244().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2245().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2246().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2247().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2248().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2249().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2250().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2251().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2252().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2253().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2254().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2255().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2256().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2257().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2258().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2259().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2260().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2261().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2262().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2263().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2264().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2265().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2266().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2267().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2268().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2269().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2270().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2271().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2272().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2273().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2274().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2275().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2276().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2277().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2278().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2279().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2280().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2281().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2282().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2283().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2284().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2285().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2286().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2287().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2288().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2289().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2290().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2291().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2292().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2293().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2294().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2295().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2296().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2297().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2298().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2299().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2300().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2301().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2302().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2303().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2304().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2305().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2306().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2307().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2308().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2309().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2310().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2311().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2312().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2313().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2314().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2315().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2316().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2317().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2318().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2319().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2320().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2321().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2322().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2323().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2324().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2325().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2326().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2327().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2328().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2329().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2330().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2331().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2332().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2333().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2334().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2335().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2336().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2337().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2338().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2339().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2340().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2341().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2342().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2343().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2344().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2345().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2346().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2347().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2348().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2349().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2350().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2351().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2352().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2353().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2354().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2355().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2356().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2357().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2358().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2359().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2360().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2361().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2362().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2363().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2364().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2365().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2366().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2367().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2368().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2369().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2370().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2371().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2372().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2373().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2374().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2375().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2376().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2377().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2378().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2379().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2380().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2381().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2382().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2383().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2384().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2385().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2386().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2387().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2388().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2389().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2390().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2391().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2392().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2393().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2394().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2395().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2396().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2397().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2398().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2399().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2400().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2401().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2402().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2403().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2404().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2405().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2406().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2407().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2408().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2409().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2410().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2411().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2412().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2413().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2414().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2415().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2416().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2417().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2418().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2419().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2420().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2421().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2422().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2423().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2424().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2425().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2426().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2427().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2428().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2429().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2430().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2431().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2432().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2433().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2434().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2435().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2436().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2437().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2438().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2439().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2440().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2441().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2442().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2443().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2444().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2445().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2446().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2447().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2448().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2449().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2450().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2451().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2452().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2453().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2454().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2455().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2456().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2457().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2458().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2459().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2460().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2461().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2462().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2463().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2464().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2465().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2466().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2467().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2468().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2469().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2470().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2471().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2472().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2473().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2474().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2475().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2476().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2477().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2478().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2479().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2480().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2481().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2482().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2483().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2484().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2485().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2486().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2487().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2488().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2489().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2490().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2491().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2492().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2493().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2494().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2495().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2496().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2497().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2498().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2499().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2500().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2501().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2502().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2503().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2504().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2505().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2506().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2507().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2508().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2509().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2510().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2511().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2512().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2513().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2514().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2515().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2516().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2517().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2518().waveDataVO;
         this._dic[this._dic.length] = new NormalWave2519().waveDataVO;
      }
      
      public function findBattle(param1:int) : MissionBattleVO
      {
         var _loc2_:MissionBattleVO = null;
         for each(_loc2_ in this._battles)
         {
            if(_loc2_.mid == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function findWave(param1:int) : WaveDataVO
      {
         var _loc2_:WaveDataVO = null;
         for each(_loc2_ in this._dic)
         {
            if(_loc2_.dataID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

