package mogames.gameData.mission.extra
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ExtraWave2436
   {
      
      public function ExtraWave2436()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2436);
         _loc1_.limitBR = new WaveLimitVO(99999999,0.8,0.8);
         _loc1_.zhuBoss = new BossArgVO(781,91000000,310000,200000,80,80,300,90,new BossSkillData0(150,{
            "hurt":250000,
            "roleNum":30
         },10),1005,0);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(772,1650000,160000,50000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(772,1650000,160000,50000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(772,1650000,160000,50000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(782,15500000,150000,50000,50,80,150,80,new BossSkillData0(150,{
            "hurt":250000,
            "keepTime":3
         },3),1014,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(772,1650000,160000,50000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(772,1650000,160000,50000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(772,1650000,160000,50000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(772,1650000,160000,50000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(772,1650000,160000,50000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(782,15500000,150000,50000,50,80,150,80,new BossSkillData1(12,{
            "hurt":250000,
            "hurtCount":5
         },3),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(772,1650000,160000,50000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(772,1650000,160000,50000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(772,1650000,160000,50000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(782,15500000,150000,50000,50,80,150,80,new BossSkillData0(150,{"hurt":200000},3),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(772,1650000,160000,50000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(772,1650000,160000,50000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(772,1650000,160000,50000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(772,1650000,160000,50000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(772,1650000,160000,50000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(782,15500000,150000,50000,50,80,150,80,new BossSkillData0(150,{
            "hurt":250000,
            "keepTime":3
         },3),1014,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

