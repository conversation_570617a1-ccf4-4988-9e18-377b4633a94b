package mogames.gameData.good.base
{
   import com.mogames.data.Oint;
   import com.mogames.data.ValueVO;
   import com.mogames.utils.MathUtil;
   import com.mogames.utils.TxtUtil;
   import mogames.ConstData;
   
   public class ConstPetEquipVO extends ConstGoodVO
   {
      
      protected var _body:Oint = new Oint();
      
      protected var _suitID:Oint = new Oint();
      
      protected var _role:Oint = new Oint();
      
      protected var _level:Oint = new Oint();
      
      protected var _equipATK:ValueVO;
      
      protected var _equipWIT:ValueVO;
      
      protected var _equipHP:ValueVO;
      
      protected var _equipDEF:ValueVO;
      
      protected var _equipMISS:ValueVO;
      
      protected var _equipCRIT:ValueVO;
      
      protected var _equipBEI:ValueVO;
      
      protected var _equipSPD:ValueVO;
      
      protected var _fakeATK:Oint = new Oint();
      
      protected var _fakeWIT:Oint = new Oint();
      
      protected var _fakeHP:Oint = new Oint();
      
      protected var _fakeDEF:Oint = new Oint();
      
      protected var _fakeMISS:Oint = new Oint();
      
      protected var _fakeCRIT:Oint = new Oint();
      
      protected var _fakeBEI:Oint = new Oint();
      
      protected var _fakeSPD:Oint = new Oint();
      
      public function ConstPetEquipVO(param1:int, param2:int, param3:int, param4:int, param5:int, param6:int, param7:ValueVO, param8:ValueVO, param9:ValueVO, param10:ValueVO, param11:ValueVO, param12:ValueVO, param13:ValueVO, param14:ValueVO, param15:int, param16:String, param17:String, param18:String, param19:String)
      {
         super(param1,param2,0,param15,0,param16,param17,param18,param19);
         MathUtil.saveINT(this._role,param5);
         MathUtil.saveINT(this._level,param6);
         MathUtil.saveINT(this._body,param3);
         MathUtil.saveINT(this._suitID,param4);
         this._equipATK = param7;
         this._equipWIT = param8;
         this._equipHP = param9;
         this._equipDEF = param10;
         this._equipMISS = param11;
         this._equipCRIT = param12;
         this._equipBEI = param13;
         this._equipSPD = param14;
         if(param7)
         {
            MathUtil.saveINT(this._fakeATK,param7.value);
         }
         if(param8)
         {
            MathUtil.saveINT(this._fakeWIT,param8.value);
         }
         if(param9)
         {
            MathUtil.saveINT(this._fakeHP,param9.value);
         }
         if(param10)
         {
            MathUtil.saveINT(this._fakeDEF,param10.value);
         }
         if(param11)
         {
            MathUtil.saveINT(this._fakeMISS,param11.value);
         }
         if(param12)
         {
            MathUtil.saveINT(this._fakeCRIT,param12.value);
         }
         if(param13)
         {
            MathUtil.saveINT(this._fakeBEI,param13.value);
         }
         if(param14)
         {
            MathUtil.saveINT(this._fakeSPD,param14.value);
         }
      }
      
      public function get roleLimit() : int
      {
         return MathUtil.loadINT(this._role);
      }
      
      public function get lvLimit() : int
      {
         return MathUtil.loadINT(this._level);
      }
      
      public function get bodyType() : int
      {
         return MathUtil.loadINT(this._body);
      }
      
      public function get suitID() : int
      {
         return MathUtil.loadINT(this._suitID);
      }
      
      public function get equipATK() : ValueVO
      {
         return this._equipATK;
      }
      
      public function get equipWIT() : ValueVO
      {
         return this._equipWIT;
      }
      
      public function get equipHP() : ValueVO
      {
         return this._equipHP;
      }
      
      public function get equipDEF() : ValueVO
      {
         return this._equipDEF;
      }
      
      public function get equipMISS() : ValueVO
      {
         return this._equipMISS;
      }
      
      public function get equipCRIT() : ValueVO
      {
         return this._equipCRIT;
      }
      
      public function get equipBEI() : ValueVO
      {
         return this._equipBEI;
      }
      
      public function get equipSPD() : ValueVO
      {
         return this._equipSPD;
      }
      
      override public function get type() : String
      {
         return ConstData.EQUIP_PET_TYPE[this.bodyType];
      }
      
      public function get prefixName() : String
      {
         return ConstData.QUALITY_NAME[quality] + "的" + _name;
      }
      
      public function get normalName() : String
      {
         return _name;
      }
      
      public function get hasHole() : Boolean
      {
         return true;
      }
      
      override public function get name() : String
      {
         return ConstData.QUALITY_NAME[quality] + "的" + _name;
      }
      
      override public function get qualityName() : String
      {
         return TxtUtil.setColor(this.prefixName,ConstData.GOOD_COLOR1[quality]);
      }
   }
}

