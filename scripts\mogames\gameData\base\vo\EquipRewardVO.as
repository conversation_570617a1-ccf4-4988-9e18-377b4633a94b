package mogames.gameData.base.vo
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import file.GoodConfig;
   import mogames.gameData.good.equip.GameEquipVO;
   import mogames.gameData.good.vo.GameGoodVO;
   
   public class EquipRewardVO extends BaseRewardVO
   {
      
      protected var _equipVO:GameEquipVO;
      
      protected var _hole:Oint = new Oint();
      
      public function EquipRewardVO(param1:int, param2:int)
      {
         MathUtil.saveINT(this._hole,param2);
         super(param1,1);
      }
      
      override protected function createGood(param1:int, param2:int) : void
      {
         _goodVO = GoodConfig.instance().newGameEquip(param1,this.curHole);
         this._equipVO = _goodVO as GameEquipVO;
      }
      
      public function get isZhiBao() : Boolean
      {
         return this._equipVO.constEquip.bodyType == 6;
      }
      
      override public function newGood(param1:int = 1) : GameGoodVO
      {
         return GoodConfig.instance().newGameEquip(constGood.id,this.curHole);
      }
      
      override public function get tipID() : int
      {
         if(!this._equipVO.constEquip.hasHole || this.curHole != 0)
         {
            return 106;
         }
         return 110;
      }
      
      override public function get tipGood() : *
      {
         if(!this._equipVO.constEquip.hasHole || this.curHole != 0)
         {
            return _goodVO;
         }
         return this._equipVO.constEquip;
      }
      
      private function get curHole() : int
      {
         return MathUtil.loadINT(this._hole);
      }
   }
}

