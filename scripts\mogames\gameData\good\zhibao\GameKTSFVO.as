package mogames.gameData.good.zhibao
{
   import com.mogames.event.SignalManager;
   import com.mogames.utils.MathUtil;
   import mogames.ConstData;
   import mogames.Layers;
   import mogames.gameData.good.base.ConstEquipVO;
   import mogames.gameData.good.equip.GameZhiBaoVO;
   import mogames.gameData.skill.SkillFactory;
   import mogames.gameData.skill.base.GameSkillVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameRole.base.IRole;
   
   public class GameKTSFVO extends GameZhiBaoVO
   {
      
      protected var _skillVO:GameSkillVO;
      
      public function GameKTSFVO(param1:ConstEquipVO)
      {
         super(param1);
         this._skillVO = SkillFactory.newNormalSkillVO(5003,{"hurt":5000});
      }
      
      override public function handlerATK(param1:IRole) : void
      {
         if(!MathUtil.checkOdds(60))
         {
            return;
         }
         this._skillVO.skillARG["hurt"] = param1.roleVO.totalATK * 5;
         SignalManager.signalSkill.dispatchEvent({
            "from":ConstData.FROM_FRIEND,
            "role":param1,
            "skill":this._skillVO.constSkill,
            "arg":this._skillVO.skillARG
         });
         EffectManager.addSkillName(this._skillVO.constSkill.name,Layers.frontLayer,param1.x,param1.y - param1.height * 2);
      }
   }
}

