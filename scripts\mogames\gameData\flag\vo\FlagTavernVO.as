package mogames.gameData.flag.vo
{
   import com.mogames.utils.MathUtil;
   import mogames.ConstData;
   import mogames.gameData.flag.base.FlagVO;
   import mogames.gameData.vip.VipProxy;
   
   public class FlagTavernVO extends FlagVO
   {
      
      public function FlagTavernVO(param1:int)
      {
         super(param1,1,false,true,true);
      }
      
      override public function get total() : int
      {
         var _loc1_:int = MathUtil.loadINT(_total);
         if(VipProxy.instance().hasFunc(201))
         {
            _loc1_++;
         }
         if(VipProxy.instance().hasFunc(205))
         {
            _loc1_ += ConstData.INT2.v;
         }
         return _loc1_;
      }
   }
}

