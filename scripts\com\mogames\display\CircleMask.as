package com.mogames.display
{
   import com.mogames.utils.MethodUtil;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   
   public class CircleMask extends BaseSprite
   {
      
      private var _mask:Sprite;
      
      private var _child:DisplayObject;
      
      public function CircleMask(param1:DisplayObject, param2:int)
      {
         super();
         this._child = param1;
         addChild(this._child);
         this._mask = new Sprite();
         addChild(this._mask);
         MethodUtil.setMousable(this,false);
         this.setRadius(param2);
      }
      
      public function setRadius(param1:int) : void
      {
         this._mask.graphics.beginFill(0);
         this._mask.graphics.drawCircle(0,0,param1);
         this._mask.graphics.endFill();
         this._child.mask = this._mask;
      }
      
      public function get child() : DisplayObject
      {
         return this._child;
      }
      
      override public function destroy() : void
      {
         super.destroy();
         this._mask = null;
         this._child = null;
      }
   }
}

