package mogames.gameData.heishi
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import file.HeiShiConfig;
   import mogames.ConstData;
   import mogames.gameData.base.func.HeiShiVO;
   import mogames.gameData.mall.vo.MallBaseVO;
   
   public class HeiShiBuyVO
   {
      
      private var _index:Oint = new Oint();
      
      private var _open:Oint = new Oint();
      
      private var _constVO:HeiShiVO;
      
      private var _mallInfo:Object;
      
      public function HeiShiBuyVO(param1:int)
      {
         super();
         MathUtil.saveINT(this._index,param1);
         MathUtil.saveINT(this._open,0);
         this._constVO = HeiShiConfig.instance().list[param1];
      }
      
      public function handlerOpen() : void
      {
         MathUtil.saveINT(this._open,1);
      }
      
      public function get isOpen() : Boolean
      {
         if(this.index == 0)
         {
            return true;
         }
         return Boolean(HeiShiConfig.instance().list[this.index - 1].hasBuy) || MathUtil.loadINT(this._open) == ConstData.INT1.v;
      }
      
      public function get index() : int
      {
         return MathUtil.loadINT(this._index);
      }
      
      public function get mallGood() : MallBaseVO
      {
         if(!this._mallInfo)
         {
            this._mallInfo = this._constVO.randomGood;
         }
         if(this._mallInfo.type == 1)
         {
            return this._constVO.golds[Math.min(this._mallInfo.index,this._constVO.golds.length - 1)];
         }
         return this._constVO.moneys[Math.min(this._mallInfo.index,this._constVO.moneys.length - 1)];
      }
      
      public function get constVO() : HeiShiVO
      {
         return this._constVO;
      }
      
      public function startNew() : void
      {
         this._mallInfo = this._constVO.randomGood;
         MathUtil.saveINT(this._open,0);
      }
      
      public function set loadData(param1:Object) : void
      {
         this.startNew();
         if(!param1)
         {
            return;
         }
         this._mallInfo = param1.info;
         MathUtil.saveINT(this._open,param1.open);
      }
      
      public function get saveData() : Object
      {
         return {
            "id":this.index,
            "open":MathUtil.loadINT(this._open),
            "info":this._mallInfo
         };
      }
   }
}

