package mogames.gameData.flag.base
{
   import file.RoleConfig;
   import file.TuJianConfig;
   import mogames.gameData.role.base.RoleInfoVO;
   
   public class RoleFlag
   {
      
      private var _list:Array;
      
      public function RoleFlag()
      {
         var _loc2_:int = 0;
         super();
         this._list = [];
         var _loc1_:Array = TuJianConfig.instance().tuRoles;
         for each(_loc2_ in _loc1_)
         {
            this._list[this._list.length] = new RoleFlagVO(_loc2_,_loc2_ >= 200);
         }
      }
      
      public function startNew() : void
      {
         var _loc1_:RoleFlagVO = null;
         for each(_loc1_ in this._list)
         {
            if(_loc1_.isSave)
            {
               _loc1_.isOpen = 0;
            }
         }
      }
      
      public function set loadData(param1:Array) : void
      {
         var _loc2_:Array = null;
         var _loc3_:RoleFlagVO = null;
         var _loc4_:String = null;
         for each(_loc4_ in param1)
         {
            _loc2_ = _loc4_.split("H");
            _loc3_ = this.findFlag(int(_loc2_[0]));
            if(_loc3_)
            {
               _loc3_.isOpen = int(_loc2_[1]);
            }
         }
      }
      
      public function get saveData() : Array
      {
         var _loc2_:RoleFlagVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._list)
         {
            if(_loc2_.isSave)
            {
               _loc1_[_loc1_.length] = _loc2_.roleID + "H" + _loc2_.isOpen;
            }
         }
         return _loc1_;
      }
      
      public function isOpen(param1:int) : Boolean
      {
         var _loc2_:RoleFlagVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.roleID == param1)
            {
               return _loc2_.isOpen == 1;
            }
         }
         return true;
      }
      
      public function setOpen(param1:int) : void
      {
         var _loc2_:RoleFlagVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.roleID == param1)
            {
               _loc2_.isOpen = 1;
               return;
            }
         }
      }
      
      public function findRoles(param1:int) : Array
      {
         var _loc3_:RoleFlagVO = null;
         var _loc4_:RoleInfoVO = null;
         var _loc2_:Array = [];
         for each(_loc3_ in this._list)
         {
            _loc4_ = RoleConfig.instance().findInfo(_loc3_.roleID);
            if(_loc4_.roleType == param1)
            {
               _loc2_[_loc2_.length] = _loc4_;
            }
         }
         return _loc2_;
      }
      
      private function findFlag(param1:int) : RoleFlagVO
      {
         var _loc2_:RoleFlagVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.roleID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

