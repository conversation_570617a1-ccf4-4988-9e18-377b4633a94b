package mogames.gameData.good.base
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   
   public class ConstLoyalVO extends ConstGoodVO
   {
      
      private var _loyal:Oint = new Oint();
      
      public function ConstLoyalVO(param1:int, param2:int, param3:int, param4:int, param5:String, param6:String, param7:String, param8:String)
      {
         super(param1,param3,1,param4,0,param5,param6,param7,param8);
         MathUtil.saveINT(this._loyal,param2);
      }
      
      public function get loyal() : int
      {
         return MathUtil.loadINT(this._loyal);
      }
   }
}

