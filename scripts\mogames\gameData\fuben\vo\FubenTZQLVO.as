package mogames.gameData.fuben.vo
{
   import file.MoneyFuncConfig;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mall.vo.MallFuncVO;
   
   public class FubenTZQLVO extends FubenNormalVO
   {
      
      public var isGuwu:Boolean;
      
      public var rankID:int = 1985;
      
      public function FubenTZQLVO(param1:int, param2:Object)
      {
         super(param1,0,param2,[],"");
      }
      
      public function get score() : int
      {
         return FlagProxy.instance().numFlag.findFlag(611).cur;
      }
      
      public function set score(param1:int) : void
      {
         if(this.score > param1)
         {
            return;
         }
         FlagProxy.instance().numFlag.setValue(611,param1);
      }
      
      override protected function get buyVO() : MallFuncVO
      {
         return MoneyFuncConfig.instance().buyTZQL;
      }
   }
}

