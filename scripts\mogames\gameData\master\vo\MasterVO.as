package mogames.gameData.master.vo
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import mogames.ConstData;
   import mogames.gameData.vip.VipProxy;
   
   public class MasterVO
   {
      
      private var _level:Oint = new Oint();
      
      private var _curExp:Oint = new Oint();
      
      private var _heroNum:Oint = new Oint();
      
      private var _levelNum:Oint = new Oint();
      
      public function MasterVO()
      {
         super();
         MathUtil.saveINT(this._level,1);
         MathUtil.saveINT(this._curExp,0);
         MathUtil.saveINT(this._heroNum,50);
         MathUtil.saveINT(this._levelNum,210);
      }
      
      public function set level(param1:int) : void
      {
         MathUtil.saveINT(this._level,param1);
      }
      
      public function get level() : int
      {
         return MathUtil.loadINT(this._level);
      }
      
      public function set curExp(param1:int) : void
      {
         MathUtil.saveINT(this._curExp,param1);
      }
      
      public function get curExp() : int
      {
         return MathUtil.loadINT(this._curExp);
      }
      
      public function get heroNum() : int
      {
         var _loc1_:int = MathUtil.loadINT(this._heroNum);
         if(VipProxy.instance().hasFunc(113))
         {
            _loc1_ += ConstData.INT10.v;
         }
         if(VipProxy.instance().hasFunc(114))
         {
            _loc1_ += ConstData.INT10.v;
         }
         if(VipProxy.instance().hasFunc(119))
         {
            _loc1_ += ConstData.INT5.v;
         }
         return _loc1_;
      }
      
      public function get isMaxLevel() : Boolean
      {
         return this.level >= this.maxLevel;
      }
      
      public function get maxLevel() : int
      {
         return MathUtil.loadINT(this._levelNum);
      }
      
      public function get saveData() : String
      {
         return [this.level,this.curExp].join("H");
      }
      
      public function set loadData(param1:String) : void
      {
         var _loc2_:Array = MathUtil.arrStringToNum(param1.split("H"));
         this.level = int(_loc2_[0]);
         this.curExp = int(_loc2_[1]);
      }
   }
}

