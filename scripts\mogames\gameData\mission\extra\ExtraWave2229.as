package mogames.gameData.mission.extra
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ExtraWave2229
   {
      
      public function ExtraWave2229()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2229);
         _loc1_.limitBR = new WaveLimitVO(99999999,0.8,0.8);
         _loc1_.zhuBoss = new BossArgVO(762,2900000,12500,1800,80,80,300,90,new BossSkillData0(150,{"hurt":50000},5),1008,0);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(753,80000,4200,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(751,80000,4200,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(750,80000,4200,110,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(763,650000,5600,450,50,80,150,80,new BossSkillData1(12,{
            "hurt":8500,
            "hurtCount":5
         },1),1003,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(753,80000,4200,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(754,80000,4200,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(751,80000,4200,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(750,80000,4200,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(750,80000,4200,110,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(764,650000,5600,450,50,80,150,80,new BossSkillData0(250,{"hurt":8500},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(753,80000,4200,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(750,80000,4200,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(753,80000,4200,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(754,80000,4200,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(754,80000,4200,110,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(765,150000,3700,450,50,80,150,80,new BossSkillData0(220,{
            "hurt":7500,
            "hurtCount":4
         },2),1017,0));
         _loc2_.addFu(new BossArgVO(760,150000,3700,450,50,30,150,80,new BossSkillData1(10,{"hurt":7000},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(753,80000,4200,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(751,80000,4200,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(753,80000,4200,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(754,80000,4200,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

