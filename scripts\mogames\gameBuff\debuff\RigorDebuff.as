package mogames.gameBuff.debuff
{
   import mogames.gameBuff.base.TimeRoleBuff;
   import mogames.gameData.buff.ConstBuffVO;
   
   public class RigorDebuff extends TimeRoleBuff
   {
      
      public function RigorDebuff(param1:ConstBuffVO)
      {
         super(param1);
      }
      
      override protected function createData() : void
      {
         _owner.addRigor(_buffVO.args.rigor);
      }
      
      override protected function onCleanRole() : void
      {
         _owner.removeRigor();
      }
      
      override protected function createSkin() : void
      {
      }
   }
}

