package mogames.gameData.cheat
{
   import file.PayRewardConfig;
   import file.PetConfig;
   import mogames.gameData.ServerProxy;
   import mogames.gameData.boon.PayRewardVO;
   import mogames.gameData.depot.DepotProxy;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.master.MasterProxy;
   import mogames.gameData.mission.MissionProxy;
   import mogames.gameData.pet.PetRandVO;
   import mogames.gameData.rank.RankCheatProxy;
   import mogames.gameData.role.HeroProxy;
   import mogames.gameData.role.PetProxy;
   import mogames.gameData.role.hero.HeroGameVO;
   import mogames.gameData.role.pet.PetGameVO;
   import mogames.gameNet.MoneyProxy;
   import mogames.gameNet.SaveManager;
   import mogames.gameUI.bagtool.DataToolModule;
   import mogames.gameUI.tip.TipUtil;
   
   public class GameChecker
   {
      
      private var _code:int;
      
      public function GameChecker()
      {
         super();
      }
      
      public function check(param1:Boolean = false) : void
      {
         if(DataToolModule.isActive)
         {
            return;
         }
         this._code = 0;
         this.checkSameZhiBao([30002,30003,30004,30005,30006,30007,30008,30009,30010,30011,30012,30013,30014,30015,30016,30017,30018,30020,30021,30122,30123,30024,30025,30026,30027,30028,30029,30030,30031,30036,30037,30038,30039,30040,30041,30042,30043,30044,30048,30051,30052,30053,30054,30055,30056,30057,30058,30059,30060,30061,30062,30063,30064,30065,30066,30067,30068,30069,30070,30071,30072,30073,30074,30075,30076,30077,30078,30079,30080,30081,30082,30083,30084,30085,30086,30087,30088,30089,30090,30091,30092,30093,30094,30095,30096,30097,30098,30099,30100,30101,30102,30103,30104,30105,30106,30107,30108,30109,30110,30111,30112,30114,30115,30116,30117,30118,30119,30120,30121,30122,30123,30124,30126,30128,30129,30130,30132,30133,30134,30135,30136,30137,30138,30139,30140,30141,30142,30143,30144,30148,30202,30203,30204,30205,30207,30208,30209,30210,30211,30212,30214,30215,30216,30217,30218,30219,30220,30221,30222,30223,30224,30226,30228,30229,30230,30232,30233,30234,30235,30236,30237,30238,30239,30240
         ,30241,30242,30243,30244,30248,30351,30352,30353,30354,30355,30356,30357,30358,30359,30360,30361,30362,30363,30364,30367,30368,30369,30370,30371,30372,30373,30374,30375,30385,30386,30387,30388,30390,30391,30392,30393,30394,30395,30396,30397,30451,30452,30455,30460,30462,30463,30464,30471,30472,31001,31002,31003,31004,31005,31006,31007,31009,31010,31011,31012,31013,31014,31015,31016,31017,31018,31019,31020,31021,31022,31023,31024,31025,31026,31027,31028,31029,31030,31031,31032,31033,31036,31037,31038,31039,31040,31041,31042,31043,31044,31045,31046,31047,31048,31051,31052,31053,31054,31055,31056,31057,31058,31059,31060,31061,31062,31063,31064,31066,31067,31068,31069,31071,31072,31073,31074,31076,31077,31078,31079,31090,31091,31092,31093,31094,31096,31097,31098,31099,31101,31102,31103,31104,31121,31122,31123,31124,31130,31131,31132,31161,31162,31163,31164,31165,31166,31167,31168,31180,31181,31182,31183,31184,31185,31186,31187,31190,31191,31192,31193,31194,31196,31197,31198,31199,31201,31202
         ,31203,31204,31205,31206,31207,31208,31211,31212,31213,31214,31215,31216,31217,31218,31221,31222,31223,31224,31225,31226,31227,31228,31231,31232,31233,31234,31235,31236,31237,31238,31240,31241,31242,31251,31252,31253,31254,31256,31257,31258,31259,31261,31262,31263,31264,31266,31267,31268,31269,31271,31272,31273,31274,31275,31276,31277,31278,31279,31281,31282,31283,31284,31291,31292,31293,31296,31297,31298,31301,31302,31303,31304,31311,31312,31313,31314,31321,31322,31323,31324,31331,31332,31333,31334,31341,31342,31343,31344,31351,31352,31353,31354,31361,31362,31363,31364,31365,31366,31367,31368,31371,31372,31373,31374,31375,31376,31377,31378,31381,31382,31383,31384,31385,31386,31387,31388,31391,31392,31395,31396,31401,31411,31421,31402,31403,31412,31413,31422,31423,31451,31452,31453,31454,31455,31456,31457,31458,31461,31462,31463,31464,31465,31466,31467,31468,31471,31472,31473,31474,31475,31476,31477,31481,31482,31483,31484,31485,31486,31487,31488,31478,31491,31492,31493,31494,31495,31496
         ,31497,31498,31501,31502,31503,31504,31511,31512,31513,31514,31521,31522,31523,31524,31531,31532,31541,31542,31551,31552,31553,31554,31561,31562,31571,31572,31581,31582,31601,31602,31603,31604,31605,31606,31607,31608,31611,31612,31613,31614,31615,31616,31617,31618,31621,31622,31623,31624,31625,31626,31627,31628,31631,31632,31633,31634,31635,31636,31637,31638,31651,31701,31702,31703,31704,31711,31712,31713,31714,31721,31722,31723,31724,31801,31802,31803,31804,31811,31812,31813,31814,31821,31822,31823,31824,32001,32002,32101,32102,32103,32111,32112,32113,32121,32122,32123]);
         this.checkMoneyZhiBao(4000,30026);
         this.checkMoneyZhiBao(4000,30126);
         this.checkMoneyZhiBao(4000,30226);
         this.checkMoneyZhiBao(7999,30075);
         this.checkMoneyZhiBao(7999,30076);
         this.checkMoneyZhiBao(5999,30077);
         this.checkMoneyZhiBao(5999,30078);
         this.checkMoneyZhiBao(20000,30082);
         this.checkMoneyZhiBao(7999,30090);
         this.checkMoneyZhiBao(7999,30091);
         this.checkMoneyZhiBao(7499,30092);
         this.checkMoneyZhiBao(7999,30093);
         this.checkMoneyZhiBao(29999,30083);
         this.checkMoneyZhiBao(7499,30094);
         this.checkMoneyZhiBao(7499,30394);
         this.checkMoneyZhiBao(7499,30095);
         this.checkMoneyZhiBao(7499,30395);
         this.checkMoneyZhiBao(7499,30096);
         this.checkMoneyZhiBao(7499,30396);
         this.checkMoneyZhiBao(9999,30097);
         this.checkMoneyZhiBao(9999,30397);
         this.checkMoneyZhiBao(32000,30084);
         this.checkMoneyZhiBao(7499,30085);
         this.checkMoneyZhiBao(7499,30385);
         this.checkMoneyZhiBao(5999,30086);
         this.checkMoneyZhiBao(5999,30386);
         this.checkMoneyZhiBao(7499,30087);
         this.checkMoneyZhiBao(7499,30387);
         this.checkMoneyZhiBao(7499,30088);
         this.checkMoneyZhiBao(7499,30388);
         this.checkMoneyZhiBao(28000,30089);
         this.checkMoneyZhiBao(5999,31001);
         this.checkMoneyZhiBao(5999,31101);
         this.checkMoneyZhiBao(7999,31002);
         this.checkMoneyZhiBao(7999,31102);
         this.checkMoneyZhiBao(7999,31003);
         this.checkMoneyZhiBao(7999,31103);
         this.checkMoneyZhiBao(7999,31004);
         this.checkMoneyZhiBao(7999,31104);
         this.checkMoneyZhiBao(7999,31021);
         this.checkMoneyZhiBao(7999,31121);
         this.checkMoneyZhiBao(149999,30098);
         this.checkMoneyZhiBao(129999,30370);
         this.checkMoneyZhiBao(109999,30070);
         this.checkMoneyZhiBao(7999,31022);
         this.checkMoneyZhiBao(7999,31122);
         this.checkMoneyZhiBao(7999,31023);
         this.checkMoneyZhiBao(7999,31123);
         this.checkMoneyZhiBao(7999,31024);
         this.checkMoneyZhiBao(7999,31124);
         this.checkMoneyZhiBao(7999,31026);
         this.checkMoneyZhiBao(7999,31027);
         this.checkMoneyZhiBao(7999,31028);
         this.checkMoneyZhiBao(7999,31029);
         this.checkMoneyZhiBao(30999,31030);
         this.checkMoneyZhiBao(30999,31130);
         this.checkMoneyZhiBao(30999,31131);
         this.checkMoneyZhiBao(30999,31132);
         this.checkMoneyZhiBao(7999,31041);
         this.checkMoneyZhiBao(7999,31042);
         this.checkMoneyZhiBao(7999,31043);
         this.checkMoneyZhiBao(7999,31044);
         this.checkMoneyZhiBao(30999,31090);
         this.checkMoneyZhiBao(209999,31099);
         this.checkMoneyZhiBao(188888,31096);
         this.checkMoneyZhiBao(208888,31097);
         this.checkMoneyZhiBao(7999,31047);
         this.checkMoneyZhiBao(7999,31045);
         this.checkMoneyZhiBao(7999,31046);
         this.checkMoneyZhiBao(7999,31047);
         this.checkMoneyZhiBao(7999,31048);
         this.checkMoneyZhiBao(23900,31091);
         this.checkMoneyZhiBao(7999,31071);
         this.checkMoneyZhiBao(7999,31072);
         this.checkMoneyZhiBao(7999,31073);
         this.checkMoneyZhiBao(7999,31074);
         this.checkMoneyZhiBao(29800,31092);
         this.checkMoneyZhiBao(109000,31093);
         this.checkMoneyZhiBao(7999,31076);
         this.checkMoneyZhiBao(7999,31077);
         this.checkMoneyZhiBao(7999,31078);
         this.checkMoneyZhiBao(7999,31079);
         this.checkMoneyZhiBao(30999,31190);
         this.checkMoneyZhiBao(7999,31211);
         this.checkMoneyZhiBao(7999,31212);
         this.checkMoneyZhiBao(7999,31213);
         this.checkMoneyZhiBao(7999,31214);
         this.checkMoneyZhiBao(30999,31191);
         this.checkMoneyZhiBao(7999,31215);
         this.checkMoneyZhiBao(7999,31216);
         this.checkMoneyZhiBao(7999,31217);
         this.checkMoneyZhiBao(7999,31218);
         this.checkMoneyZhiBao(30999,31192);
         this.checkMoneyZhiBao(30999,31196);
         this.checkMoneyZhiBao(30999,31197);
         this.checkMoneyZhiBao(7999,31225);
         this.checkMoneyZhiBao(7999,31226);
         this.checkMoneyZhiBao(7999,31227);
         this.checkMoneyZhiBao(7999,31228);
         this.checkMoneyZhiBao(30999,31193);
         this.checkMoneyZhiBao(30999,31198);
         this.checkMoneyZhiBao(30999,31199);
         this.checkMoneyZhiBao(122000,31194);
         this.checkMoneyZhiBao(7999,31235);
         this.checkMoneyZhiBao(7999,31236);
         this.checkMoneyZhiBao(220000,31094);
         this.checkMoneyZhiBao(7999,31237);
         this.checkMoneyZhiBao(7999,31238);
         this.checkMoneyZhiBao(30999,31240);
         this.checkMoneyZhiBao(30999,31241);
         this.checkMoneyZhiBao(30999,31242);
         this.checkMoneyZhiBao(7999,31256);
         this.checkMoneyZhiBao(7999,31257);
         this.checkMoneyZhiBao(7999,31258);
         this.checkMoneyZhiBao(7999,31259);
         this.checkMoneyZhiBao(30999,31271);
         this.checkMoneyZhiBao(30999,31272);
         this.checkMoneyZhiBao(30999,31273);
         this.checkMoneyZhiBao(7999,31266);
         this.checkMoneyZhiBao(7999,31267);
         this.checkMoneyZhiBao(5999,31268);
         this.checkMoneyZhiBao(7999,31269);
         this.checkMoneyZhiBao(28999,31274);
         this.checkMoneyZhiBao(28999,31275);
         this.checkMoneyZhiBao(28999,31276);
         this.checkMoneyZhiBao(7999,31286);
         this.checkMoneyZhiBao(7999,31287);
         this.checkMoneyZhiBao(7999,31288);
         this.checkMoneyZhiBao(7999,31289);
         this.checkMoneyZhiBao(30999,31277);
         this.checkMoneyZhiBao(30999,31278);
         this.checkMoneyZhiBao(30999,31279);
         this.checkMoneyZhiBao(122000,32101);
         this.checkMoneyZhiBao(122000,32102);
         this.checkMoneyZhiBao(122000,32103);
         this.checkMoneyZhiBao(7999,31296);
         this.checkMoneyZhiBao(7999,31297);
         this.checkMoneyZhiBao(7999,31298);
         this.checkMoneyZhiBao(7999,31299);
         this.checkMoneyZhiBao(30999,31331);
         this.checkMoneyZhiBao(30999,31341);
         this.checkMoneyZhiBao(30999,31351);
         this.checkMoneyZhiBao(9999,31365);
         this.checkMoneyZhiBao(9999,31366);
         this.checkMoneyZhiBao(7999,31367);
         this.checkMoneyZhiBao(7999,31368);
         this.checkMoneyZhiBao(30999,31332);
         this.checkMoneyZhiBao(30999,31342);
         this.checkMoneyZhiBao(30999,31352);
         this.checkMoneyZhiBao(7999,31375);
         this.checkMoneyZhiBao(7999,31376);
         this.checkMoneyZhiBao(7999,31377);
         this.checkMoneyZhiBao(7999,31378);
         this.checkMoneyZhiBao(30999,31333);
         this.checkMoneyZhiBao(30999,31343);
         this.checkMoneyZhiBao(30999,31353);
         this.checkMoneyZhiBao(7999,31385);
         this.checkMoneyZhiBao(7999,31386);
         this.checkMoneyZhiBao(9999,31387);
         this.checkMoneyZhiBao(9999,31388);
         this.checkMoneyZhiBao(30999,31334);
         this.checkMoneyZhiBao(30999,31344);
         this.checkMoneyZhiBao(30999,31354);
         this.checkMoneyZhiBao(122000,32111);
         this.checkMoneyZhiBao(122000,32112);
         this.checkMoneyZhiBao(122000,32113);
         this.checkMoneyZhiBao(7999,31395);
         this.checkMoneyZhiBao(7999,31396);
         this.checkMoneyZhiBao(7999,31397);
         this.checkMoneyZhiBao(7999,31398);
         this.checkMoneyZhiBao(7999,31455);
         this.checkMoneyZhiBao(7999,31456);
         this.checkMoneyZhiBao(7999,31457);
         this.checkMoneyZhiBao(7999,31458);
         this.checkMoneyZhiBao(7999,31465);
         this.checkMoneyZhiBao(7999,31466);
         this.checkMoneyZhiBao(7999,31467);
         this.checkMoneyZhiBao(7999,31468);
         this.checkMoneyZhiBao(7999,31475);
         this.checkMoneyZhiBao(7999,31476);
         this.checkMoneyZhiBao(4999,31477);
         this.checkMoneyZhiBao(7999,31478);
         this.checkMoneyZhiBao(7999,31485);
         this.checkMoneyZhiBao(7999,31486);
         this.checkMoneyZhiBao(7999,31487);
         this.checkMoneyZhiBao(7999,31488);
         this.checkMoneyZhiBao(7999,31495);
         this.checkMoneyZhiBao(7999,31496);
         this.checkMoneyZhiBao(7999,31497);
         this.checkMoneyZhiBao(7999,31498);
         this.checkMoneyZhiBao(7999,31605);
         this.checkMoneyZhiBao(7999,31606);
         this.checkMoneyZhiBao(7999,31607);
         this.checkMoneyZhiBao(7999,31608);
         this.checkMoneyZhiBao(7999,31615);
         this.checkMoneyZhiBao(7999,31616);
         this.checkMoneyZhiBao(7999,31617);
         this.checkMoneyZhiBao(7999,31618);
         this.checkMoneyZhiBao(7999,31625);
         this.checkMoneyZhiBao(7999,31626);
         this.checkMoneyZhiBao(7999,31627);
         this.checkMoneyZhiBao(7999,31628);
         this.checkMoneyZhiBao(7999,31635);
         this.checkMoneyZhiBao(7999,31636);
         this.checkMoneyZhiBao(7999,31637);
         this.checkMoneyZhiBao(7999,31638);
         this.checkMoneyZhiBao(30999,31501);
         this.checkMoneyZhiBao(30999,31511);
         this.checkMoneyZhiBao(30999,31521);
         this.checkMoneyZhiBao(30999,31502);
         this.checkMoneyZhiBao(30999,31512);
         this.checkMoneyZhiBao(30999,31522);
         this.checkMoneyZhiBao(30999,31503);
         this.checkMoneyZhiBao(30999,31513);
         this.checkMoneyZhiBao(30999,31523);
         this.checkMoneyZhiBao(30999,31504);
         this.checkMoneyZhiBao(30999,31514);
         this.checkMoneyZhiBao(30999,31524);
         this.checkMoneyZhiBao(30999,31531);
         this.checkMoneyZhiBao(30999,31541);
         this.checkMoneyZhiBao(30999,31551);
         this.checkMoneyZhiBao(30999,31532);
         this.checkMoneyZhiBao(30999,31542);
         this.checkMoneyZhiBao(30999,31552);
         this.checkMoneyZhiBao(30999,31533);
         this.checkMoneyZhiBao(30999,31543);
         this.checkMoneyZhiBao(30999,31553);
         this.checkMoneyZhiBao(30999,31534);
         this.checkMoneyZhiBao(30999,31544);
         this.checkMoneyZhiBao(30999,31554);
         this.checkMoneyZhiBao(30999,31561);
         this.checkMoneyZhiBao(30999,31571);
         this.checkMoneyZhiBao(30999,31581);
         this.checkMoneyZhiBao(30999,31562);
         this.checkMoneyZhiBao(30999,31572);
         this.checkMoneyZhiBao(30999,31582);
         this.checkMoneyZhiBao(30999,31005);
         this.checkMoneyZhiBao(30999,31006);
         this.checkMoneyZhiBao(121998,31007);
         this.checkMoneyZhiBao(3999,31010);
         this.checkMoneyZhiBao(3999,31011);
         this.checkMoneyZhiBao(3999,31012);
         this.checkMoneyZhiBao(3999,31013);
         this.checkMoneyZhiBao(15888,31014);
         this.checkMoneyZhiBao(3999,31015);
         this.checkMoneyZhiBao(3999,31016);
         this.checkMoneyZhiBao(3999,31017);
         this.checkMoneyZhiBao(3999,31018);
         this.checkMoneyZhiBao(15888,31019);
         this.checkMoneyZhiBao(3999,31031);
         this.checkMoneyZhiBao(3999,31032);
         this.checkMoneyZhiBao(3999,31033);
         this.checkMoneyZhiBao(3999,31034);
         this.checkMoneyZhiBao(15888,31035);
         this.checkMoneyZhiBao(3999,31036);
         this.checkMoneyZhiBao(3999,31037);
         this.checkMoneyZhiBao(3999,31038);
         this.checkMoneyZhiBao(3999,31039);
         this.checkMoneyZhiBao(15888,31040);
         this.checkMoneyZhiBao(63550,31020);
         this.checkMoneyZhiBao(3999,31051);
         this.checkMoneyZhiBao(3999,31052);
         this.checkMoneyZhiBao(3999,31053);
         this.checkMoneyZhiBao(3999,31054);
         this.checkMoneyZhiBao(11900,31055);
         this.checkMoneyZhiBao(3999,31056);
         this.checkMoneyZhiBao(3999,31057);
         this.checkMoneyZhiBao(3999,31058);
         this.checkMoneyZhiBao(3999,31059);
         this.checkMoneyZhiBao(15888,31060);
         this.checkMoneyZhiBao(3999,31066);
         this.checkMoneyZhiBao(3999,31067);
         this.checkMoneyZhiBao(3999,31068);
         this.checkMoneyZhiBao(3999,31069);
         this.checkMoneyZhiBao(15888,31180);
         this.checkMoneyZhiBao(3999,31201);
         this.checkMoneyZhiBao(3999,31202);
         this.checkMoneyZhiBao(3999,31203);
         this.checkMoneyZhiBao(3999,31204);
         this.checkMoneyZhiBao(15888,31181);
         this.checkMoneyZhiBao(59000,31025);
         this.checkMoneyZhiBao(3999,31205);
         this.checkMoneyZhiBao(3999,31206);
         this.checkMoneyZhiBao(3999,31207);
         this.checkMoneyZhiBao(3999,31208);
         this.checkMoneyZhiBao(15888,31182);
         this.checkMoneyZhiBao(3999,31221);
         this.checkMoneyZhiBao(3999,31222);
         this.checkMoneyZhiBao(3999,31223);
         this.checkMoneyZhiBao(3999,31224);
         this.checkMoneyZhiBao(15888,31183);
         this.checkMoneyZhiBao(3999,31231);
         this.checkMoneyZhiBao(3999,31232);
         this.checkMoneyZhiBao(3999,31233);
         this.checkMoneyZhiBao(3999,31234);
         this.checkMoneyZhiBao(15888,31184);
         this.checkMoneyZhiBao(15888,31186);
         this.checkMoneyZhiBao(3999,31251);
         this.checkMoneyZhiBao(3999,31252);
         this.checkMoneyZhiBao(3999,31253);
         this.checkMoneyZhiBao(3999,31254);
         this.checkMoneyZhiBao(15888,31185);
         this.checkMoneyZhiBao(15888,31187);
         this.checkMoneyZhiBao(3999,31261);
         this.checkMoneyZhiBao(3999,31262);
         this.checkMoneyZhiBao(3999,31263);
         this.checkMoneyZhiBao(3999,31264);
         this.checkMoneyZhiBao(15888,31301);
         this.checkMoneyZhiBao(15888,31311);
         this.checkMoneyZhiBao(15888,31321);
         this.checkMoneyZhiBao(59000,32001);
         this.checkMoneyZhiBao(3999,31281);
         this.checkMoneyZhiBao(3999,31282);
         this.checkMoneyZhiBao(3999,31283);
         this.checkMoneyZhiBao(3999,31284);
         this.checkMoneyZhiBao(15888,31302);
         this.checkMoneyZhiBao(15888,31312);
         this.checkMoneyZhiBao(15888,31322);
         this.checkMoneyZhiBao(3999,31291);
         this.checkMoneyZhiBao(3999,31292);
         this.checkMoneyZhiBao(3999,31293);
         this.checkMoneyZhiBao(3999,31294);
         this.checkMoneyZhiBao(15888,31303);
         this.checkMoneyZhiBao(15888,31313);
         this.checkMoneyZhiBao(15888,31323);
         this.checkMoneyZhiBao(4999,31361);
         this.checkMoneyZhiBao(4999,31362);
         this.checkMoneyZhiBao(3999,31363);
         this.checkMoneyZhiBao(3999,31364);
         this.checkMoneyZhiBao(15888,31304);
         this.checkMoneyZhiBao(15888,31314);
         this.checkMoneyZhiBao(15888,31324);
         this.checkMoneyZhiBao(59000,32002);
         this.checkMoneyZhiBao(3999,31371);
         this.checkMoneyZhiBao(3999,31372);
         this.checkMoneyZhiBao(3999,31373);
         this.checkMoneyZhiBao(3999,31374);
         this.checkMoneyZhiBao(15888,31401);
         this.checkMoneyZhiBao(15888,31411);
         this.checkMoneyZhiBao(15888,31421);
         this.checkMoneyZhiBao(15888,31402);
         this.checkMoneyZhiBao(15888,31412);
         this.checkMoneyZhiBao(15888,31422);
         this.checkMoneyZhiBao(15888,31403);
         this.checkMoneyZhiBao(15888,31413);
         this.checkMoneyZhiBao(15888,31423);
         this.checkMoneyZhiBao(15888,31404);
         this.checkMoneyZhiBao(15888,31414);
         this.checkMoneyZhiBao(15888,31424);
         this.checkMoneyZhiBao(3999,31381);
         this.checkMoneyZhiBao(3999,31382);
         this.checkMoneyZhiBao(3999,31383);
         this.checkMoneyZhiBao(3999,31384);
         this.checkMoneyZhiBao(3999,31391);
         this.checkMoneyZhiBao(3999,31392);
         this.checkMoneyZhiBao(3999,31393);
         this.checkMoneyZhiBao(3999,31394);
         this.checkMoneyZhiBao(3999,31451);
         this.checkMoneyZhiBao(3999,31452);
         this.checkMoneyZhiBao(3999,31453);
         this.checkMoneyZhiBao(3999,31454);
         this.checkMoneyZhiBao(3999,31461);
         this.checkMoneyZhiBao(3999,31462);
         this.checkMoneyZhiBao(3999,31463);
         this.checkMoneyZhiBao(3999,31464);
         this.checkMoneyZhiBao(3999,31471);
         this.checkMoneyZhiBao(3999,31472);
         this.checkMoneyZhiBao(2999,31473);
         this.checkMoneyZhiBao(3999,31474);
         this.checkMoneyZhiBao(15888,31701);
         this.checkMoneyZhiBao(15888,31711);
         this.checkMoneyZhiBao(15888,31721);
         this.checkMoneyZhiBao(15888,31702);
         this.checkMoneyZhiBao(15888,31712);
         this.checkMoneyZhiBao(15888,31722);
         this.checkMoneyZhiBao(15888,31703);
         this.checkMoneyZhiBao(15888,31713);
         this.checkMoneyZhiBao(15888,31723);
         this.checkMoneyZhiBao(15888,31704);
         this.checkMoneyZhiBao(15888,31714);
         this.checkMoneyZhiBao(15888,31724);
         this.checkMoneyZhiBao(3999,31481);
         this.checkMoneyZhiBao(3999,31482);
         this.checkMoneyZhiBao(3999,31483);
         this.checkMoneyZhiBao(3999,31484);
         this.checkMoneyZhiBao(3999,31491);
         this.checkMoneyZhiBao(3999,31492);
         this.checkMoneyZhiBao(3999,31493);
         this.checkMoneyZhiBao(3999,31494);
         this.checkMoneyZhiBao(3999,31601);
         this.checkMoneyZhiBao(3999,31602);
         this.checkMoneyZhiBao(3999,31603);
         this.checkMoneyZhiBao(3999,31604);
         this.checkMoneyZhiBao(3999,31611);
         this.checkMoneyZhiBao(3999,31612);
         this.checkMoneyZhiBao(3999,31613);
         this.checkMoneyZhiBao(3999,31614);
         this.checkMoneyZhiBao(3999,31621);
         this.checkMoneyZhiBao(3999,31622);
         this.checkMoneyZhiBao(3999,31623);
         this.checkMoneyZhiBao(3999,31624);
         this.checkMoneyZhiBao(3999,31631);
         this.checkMoneyZhiBao(3999,31632);
         this.checkMoneyZhiBao(3999,31633);
         this.checkMoneyZhiBao(3999,31634);
         this.checkMoneyZhiBao(15888,31801);
         this.checkMoneyZhiBao(15888,31811);
         this.checkMoneyZhiBao(15888,31821);
         this.checkMoneyZhiBao(15888,31802);
         this.checkMoneyZhiBao(15888,31812);
         this.checkMoneyZhiBao(15888,31822);
         this.checkMoneyZhiBao(15888,31803);
         this.checkMoneyZhiBao(15888,31813);
         this.checkMoneyZhiBao(15888,31823);
         this.checkMoneyZhiBao(15888,31804);
         this.checkMoneyZhiBao(15888,31814);
         this.checkMoneyZhiBao(15888,31824);
         this.checkMoneyHero(100,360);
         this.checkMoneyHero(3000,359);
         this.checkMoneyHero(999999,999);
         this.checkMoneyHero(999,619);
         this.checkMoneyHero(349,647);
         this.checkMoneyHero(2499,651);
         this.checkMoneyHero(3999,620);
         this.checkMoneyHero(2499,482);
         this.checkMoneyHero(4999,654);
         this.checkNoMoneyHero(319);
         this.checkMoneyGood(1749,11007);
         this.checkMoneyGood(2500,11106);
         this.checkMoneyGood(2500,11153);
         this.checkMoneyGood(4999,18553);
         this.checkMoneyGood(4999,18554);
         this.checkMoneyGood(4999,18555);
         this.checkMoneyGood(49,10854);
         this.checkMoneyGood(1999,10855);
         this.checkMoneyGood(4999,10856);
         this.checkMoneyGoodNum(999999,18553,580);
         this.checkHeroBR(49500000);
         this.checkFirstPay();
         this.checkPayReward();
         this.checkPetLevel();
         this.checkHeroLevel();
         this.checkMoneyPet(5999,1004);
         this.checkMoneyPet(7999,1005);
         this.checkPetBR(3200000);
         this.checkHeroSkill(9);
         this.checkPetSkill(7);
         if(this._code != 0)
         {
            RankCheatProxy.instance().submitRank(this._code);
         }
         if(param1 && this._code != 0)
         {
            SaveManager.instance().saveAuto();
         }
         if(this._code != 0 || ServerProxy.instance().checkCode != 0)
         {
            TipUtil.handlerLock();
         }
      }
      
      public function checkHeroLevel() : void
      {
         var _loc2_:HeroGameVO = null;
         var _loc1_:Array = HeroProxy.instance().ownerHeros;
         for each(_loc2_ in _loc1_)
         {
            if(_loc2_.level > 170)
            {
               if(_loc2_.level > MasterProxy.instance().masterVO.maxLevel)
               {
                  ServerProxy.instance().checkCode = 224;
                  this._code = 224;
                  return;
               }
            }
         }
      }
      
      public function checkTZQL(param1:int) : void
      {
         if(HeroProxy.instance().totalBR >= 200000)
         {
            return;
         }
         if(param1 < 15000)
         {
            return;
         }
         ServerProxy.instance().checkCode = 223;
         this._code = 223;
      }
      
      private function checkMoneyGoodNum(param1:int, param2:int, param3:int) : void
      {
         if(this.payMoney >= param1)
         {
            return;
         }
         if(DepotProxy.instance().findNum(param2) <= param3)
         {
            return;
         }
         ServerProxy.instance().checkCode = 222;
         this._code = 222;
      }
      
      private function checkPetSkill(param1:int) : void
      {
         var _loc3_:PetGameVO = null;
         var _loc2_:Array = PetProxy.instance().ownPets;
         for each(_loc3_ in _loc2_)
         {
            if(_loc3_.petSkill.level > param1)
            {
               ServerProxy.instance().checkCode = 221;
               this._code = 221;
               return;
            }
         }
      }
      
      private function checkHeroSkill(param1:int) : void
      {
         var _loc3_:HeroGameVO = null;
         var _loc2_:Array = HeroProxy.instance().ownerHeros;
         for each(_loc3_ in _loc2_)
         {
            if(_loc3_.mainSkill.level > param1)
            {
               ServerProxy.instance().checkCode = 220;
               this._code = 220;
               return;
            }
         }
      }
      
      private function checkPetLevel() : void
      {
         var _loc3_:PetGameVO = null;
         var _loc1_:Array = PetProxy.instance().ownPets;
         var _loc2_:int = MasterProxy.instance().masterVO.level;
         for each(_loc3_ in _loc1_)
         {
            if(_loc3_.level > _loc2_)
            {
               ServerProxy.instance().checkCode = 219;
               this._code = 219;
               return;
            }
         }
      }
      
      private function checkPKZhiBao(param1:int, param2:Array) : void
      {
         var _loc3_:int = 0;
         if(FlagProxy.instance().numFlag.findFlag(605).cur > param1)
         {
            return;
         }
         for each(_loc3_ in param2)
         {
            if(MasterProxy.instance().hasOneEquip(_loc3_))
            {
               ServerProxy.instance().checkCode = 218;
               this._code = 218;
            }
         }
      }
      
      private function checkPetBR(param1:int) : void
      {
         var _loc3_:PetGameVO = null;
         var _loc2_:Array = PetProxy.instance().ownPets;
         for each(_loc3_ in _loc2_)
         {
            if(_loc3_.battleRate >= param1)
            {
               ServerProxy.instance().checkCode = 217;
               this._code = 217;
               return;
            }
         }
      }
      
      private function checkMoneyPet(param1:int, param2:int) : void
      {
         var _loc4_:PetRandVO = null;
         var _loc5_:PetGameVO = null;
         if(Boolean(param1) && this.payMoney >= param1)
         {
            return;
         }
         var _loc3_:Array = PetProxy.instance().findPets(param2);
         for each(_loc5_ in _loc3_)
         {
            _loc4_ = PetConfig.instance().findPetRandVO(param2);
            if(_loc5_.argATK >= _loc4_.randATK.max)
            {
               if(_loc5_.argWIT >= _loc4_.randWIT.max)
               {
                  if(_loc5_.argDEF >= _loc4_.randDEF.max)
                  {
                     if(_loc5_.argBEI >= _loc4_.randBEI.max)
                     {
                        ServerProxy.instance().checkCode = 216;
                        this._code = 216;
                     }
                  }
               }
            }
         }
      }
      
      private function checkSameZhiBao(param1:Array) : void
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         if(param1.length <= 0)
         {
            return;
         }
         for each(_loc3_ in param1)
         {
            if(HeroProxy.instance().findEquipNum(_loc3_) + DepotProxy.instance().findNum(_loc3_) >= 2)
            {
               ServerProxy.instance().checkCode = 215;
               this._code = 215;
            }
         }
      }
      
      private function checkMoneyZhiBao(param1:int, param2:int) : void
      {
         if(Boolean(param1) && this.payMoney >= param1)
         {
            return;
         }
         if(!HeroProxy.instance().hasEquip(param2) && DepotProxy.instance().findNum(param2) <= 0)
         {
            return;
         }
         ServerProxy.instance().checkCode = 214;
         this._code = 214;
      }
      
      private function checkNoMoneyHero(param1:int) : void
      {
         if(this.payMoney > 0)
         {
            return;
         }
         if(!HeroProxy.instance().hasHero(param1))
         {
            return;
         }
         ServerProxy.instance().checkCode = 211;
         this._code = 211;
      }
      
      private function checkPayReward() : void
      {
         var _loc2_:PayRewardVO = null;
         var _loc1_:Array = PayRewardConfig.instance().rewards;
         for each(_loc2_ in _loc1_)
         {
            if(MoneyProxy.instance().payNum < _loc2_.needPay)
            {
               if(_loc2_.hasGet)
               {
                  ServerProxy.instance().checkCode = 209;
                  this._code = 209;
                  return;
               }
            }
         }
      }
      
      private function checkFirstPay() : void
      {
         if(MoneyProxy.instance().payNum >= 100)
         {
            return;
         }
         if(!FlagProxy.instance().openFlag.isComplete(201))
         {
            return;
         }
         ServerProxy.instance().checkCode = 208;
         this._code = 208;
      }
      
      private function checkHeroBR(param1:int) : void
      {
         var _loc2_:HeroGameVO = null;
         for each(_loc2_ in HeroProxy.instance().ownerHeros)
         {
            if(_loc2_.battleRate >= param1)
            {
               ServerProxy.instance().checkCode = 212;
               this._code = 212;
               return;
            }
         }
      }
      
      private function checkTimeGold(param1:int, param2:int) : void
      {
         if(this.payMoney > 0)
         {
            return;
         }
         if(ServerProxy.instance().playTime > param1)
         {
            return;
         }
         if(MasterProxy.instance().findValue(10000) < param2)
         {
            return;
         }
         ServerProxy.instance().checkCode = 213;
         this._code = 213;
      }
      
      private function checkMoneyGood(param1:int, param2:int) : void
      {
         if(this.payMoney >= param1)
         {
            return;
         }
         if(DepotProxy.instance().findNum(param2) <= 0)
         {
            return;
         }
         ServerProxy.instance().checkCode = 202;
         this._code = 202;
      }
      
      private function checkMoneyHero(param1:int, param2:int) : void
      {
         if(Boolean(param1) && this.payMoney >= param1)
         {
            return;
         }
         if(!HeroProxy.instance().hasHero(param2))
         {
            return;
         }
         ServerProxy.instance().checkCode = 204;
         this._code = 204;
      }
      
      private function checkMissionHero(param1:int, param2:int) : void
      {
         if(MissionProxy.instance().isFinish(param1))
         {
            return;
         }
         if(!HeroProxy.instance().hasHero(param2))
         {
            return;
         }
         ServerProxy.instance().checkCode = 205;
         this._code = 205;
      }
      
      private function get payMoney() : int
      {
         return MoneyProxy.instance().payNum;
      }
   }
}

