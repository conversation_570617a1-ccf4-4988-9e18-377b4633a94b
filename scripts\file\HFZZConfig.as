package file
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class HFZZConfig
   {
      
      private static var _instance:HFZZConfig;
      
      public var argBOSS:BossArgVO;
      
      public var armyNum:int;
      
      public var armyData:RoleArgVO;
      
      public var waveData:WaveDataVO;
      
      public var argSun:RoleArgVO;
      
      public function HFZZConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : HFZZConfig
      {
         if(!_instance)
         {
            _instance = new HFZZConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         var _loc1_:OneWaveVO = null;
         this.argSun = new BossArgVO(621,200000,1300,300,30,25,150,130,new BossSkillData1(100,{
            "hurt":3000,
            "rebound":20,
            "keepTime":2
         },8),0,0);
         this.argBOSS = new BossArgVO(454,400000,1600,400,50,50,150,150,new BossSkillData1(180,{
            "hurt":2500,
            "killPer":50
         },8),0,0);
         this.armyNum = 20;
         this.armyData = new RoleArgVO(111,20000,600,100,30,25,150,100,{
            "rate":150,
            "keepTime":2
         });
         this.waveData = new WaveDataVO(0);
         this.waveData.limitBR = new WaveLimitVO(10000,1.3,1.3);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(101,10000,650,0,30,25,150,130,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc1_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(102,10000,650,0,30,25,150,130,{
            "rate":200,
            "hurtBei":2,
            "atkPer":100,
            "keepTime":5
         })));
         _loc1_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(103,10000,650,0,30,25,150,130,{
            "rate":200,
            "defPer":20,
            "keepTime":5
         })));
         _loc1_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(104,10000,650,0,30,25,150,130,{
            "rate":500,
            "curePer":50
         })));
         _loc1_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(105,10000,650,0,30,25,150,130,{
            "rate":200,
            "curePer":30
         })));
         _loc1_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(106,10000,650,0,30,25,150,130,{"atkPer":50})));
         _loc1_.addFu(new BossArgVO(592,80000,1000,150,50,50,150,150,new BossSkillData0(150,{
            "hurt":2000,
            "atkPer":20,
            "keepTime":5
         },5),0,0));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(16);
         _loc1_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(107,10000,650,0,30,25,150,130,{
            "rate":200,
            "hurtBei":2.5
         })));
         _loc1_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(111,10000,650,0,30,25,150,130,{
            "rate":200,
            "keepTime":3
         })));
         _loc1_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(109,10000,650,0,30,25,150,130,{
            "rate":200,
            "hurtPer":80,
            "spdPer":50,
            "keepTime":3
         })));
         _loc1_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(110,10000,650,0,30,25,150,130,{
            "rate":200,
            "hurtBei":1.8
         })));
         _loc1_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(111,10000,650,0,30,25,150,130,{
            "rate":200,
            "keepTime":3
         })));
         _loc1_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(112,10000,650,0,30,25,150,130,{
            "rate":200,
            "hurtBei":1.8
         })));
         _loc1_.addFu(new BossArgVO(356,80000,1000,150,50,50,150,150,new BossSkillData0(150,{
            "hurt":2000,
            "roleNum":30
         },5),0,0));
         this.waveData.addWave(_loc1_);
      }
   }
}

