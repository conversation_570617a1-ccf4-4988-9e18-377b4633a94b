package mogames.gameData.mission.extra
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ExtraWave2162
   {
      
      public function ExtraWave2162()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2162);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(822,1400000,6200,650,80,80,300,90,new BossSkillData0(150,{
            "hurt":25000,
            "atkPer":5
         },5),1023,0);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(275,29500,2650,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(275,29500,2650,120,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(272,29500,2650,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(272,29500,2650,120,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(277,400000,4800,450,50,80,150,80,new BossSkillData1(10,{"hurt":7000},2),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(273,29500,2650,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(273,29500,2650,120,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(272,29500,2650,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(274,29500,2650,120,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(277,400000,4800,450,50,80,150,80,new BossSkillData1(10,{"hurt":7000},2),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(273,29500,2650,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(273,29500,2650,120,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(272,29500,2650,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(272,29500,2650,120,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(277,400000,4800,450,50,80,150,80,new BossSkillData0(250,{
            "hurt":7000,
            "hurtCount":5
         },2),1003,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(273,29500,2650,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(274,29500,2650,120,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(272,29500,2650,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(272,29500,2650,120,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(276,400000,4800,450,50,80,150,80,new BossSkillData0(250,{"hurt":7000},2),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(273,29500,2650,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(273,29500,2650,120,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(273,29500,2650,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(275,29500,2650,120,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

