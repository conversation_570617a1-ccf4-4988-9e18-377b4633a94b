package file
{
   import mogames.gameData.base.func.ShenZDZVO;
   import mogames.gameData.base.vo.NeedVO;
   
   public class ShenZDZConfig
   {
      
      private static var _instance:ShenZDZConfig;
      
      private var _list:Array;
      
      public var stoneNeed:NeedVO;
      
      public function ShenZDZConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : ShenZDZConfig
      {
         if(!_instance)
         {
            _instance = new ShenZDZConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this.stoneNeed = new NeedVO(10986,1);
         this._list = [];
         this._list[this._list.length] = new ShenZDZVO(20051,600000,1500,500,[new NeedVO(10450,8),new NeedVO(10451,20),new NeedVO(10452,20)]);
         this._list[this._list.length] = new ShenZDZVO(21051,600000,1500,500,[new NeedVO(10450,8),new NeedVO(10451,20),new NeedVO(10452,20)]);
         this._list[this._list.length] = new ShenZDZVO(22051,600000,1500,500,[new NeedVO(10450,8),new NeedVO(10451,20),new NeedVO(10452,20)]);
         this._list[this._list.length] = new ShenZDZVO(23051,600000,1500,500,[new NeedVO(10450,8),new NeedVO(10451,20),new NeedVO(10452,20)]);
         this._list[this._list.length] = new ShenZDZVO(24051,600000,1500,500,[new NeedVO(10450,8),new NeedVO(10451,20),new NeedVO(10452,20)]);
         this._list[this._list.length] = new ShenZDZVO(25051,600000,1500,500,[new NeedVO(10461,8),new NeedVO(10462,20),new NeedVO(10463,20)]);
         this._list[this._list.length] = new ShenZDZVO(20151,600000,1500,500,[new NeedVO(10450,8),new NeedVO(10453,20),new NeedVO(10454,20)]);
         this._list[this._list.length] = new ShenZDZVO(21151,600000,1500,500,[new NeedVO(10450,8),new NeedVO(10453,20),new NeedVO(10454,20)]);
         this._list[this._list.length] = new ShenZDZVO(22151,600000,1500,500,[new NeedVO(10450,8),new NeedVO(10453,20),new NeedVO(10454,20)]);
         this._list[this._list.length] = new ShenZDZVO(23151,600000,1500,500,[new NeedVO(10450,8),new NeedVO(10453,20),new NeedVO(10454,20)]);
         this._list[this._list.length] = new ShenZDZVO(24151,600000,1500,500,[new NeedVO(10450,8),new NeedVO(10453,20),new NeedVO(10454,20)]);
         this._list[this._list.length] = new ShenZDZVO(25151,600000,1500,500,[new NeedVO(10461,8),new NeedVO(10464,20),new NeedVO(10465,20)]);
         this._list[this._list.length] = new ShenZDZVO(20251,600000,1500,500,[new NeedVO(10450,8),new NeedVO(10459,20),new NeedVO(10460,20)]);
         this._list[this._list.length] = new ShenZDZVO(21251,600000,1500,500,[new NeedVO(10450,8),new NeedVO(10459,20),new NeedVO(10460,20)]);
         this._list[this._list.length] = new ShenZDZVO(22251,600000,1500,500,[new NeedVO(10450,8),new NeedVO(10459,20),new NeedVO(10460,20)]);
         this._list[this._list.length] = new ShenZDZVO(23251,600000,1500,500,[new NeedVO(10450,8),new NeedVO(10459,20),new NeedVO(10460,20)]);
         this._list[this._list.length] = new ShenZDZVO(24251,600000,1500,500,[new NeedVO(10450,8),new NeedVO(10459,20),new NeedVO(10460,20)]);
         this._list[this._list.length] = new ShenZDZVO(25251,600000,1500,500,[new NeedVO(10461,8),new NeedVO(10463,20),new NeedVO(10464,20)]);
         this._list[this._list.length] = new ShenZDZVO(20351,600000,1500,500,[new NeedVO(10450,8),new NeedVO(10455,20),new NeedVO(10456,20)]);
         this._list[this._list.length] = new ShenZDZVO(21351,600000,1500,500,[new NeedVO(10450,8),new NeedVO(10455,20),new NeedVO(10456,20)]);
         this._list[this._list.length] = new ShenZDZVO(22351,600000,1500,500,[new NeedVO(10450,8),new NeedVO(10455,20),new NeedVO(10456,20)]);
         this._list[this._list.length] = new ShenZDZVO(23351,600000,1500,500,[new NeedVO(10450,8),new NeedVO(10455,20),new NeedVO(10456,20)]);
         this._list[this._list.length] = new ShenZDZVO(24351,600000,1500,500,[new NeedVO(10450,8),new NeedVO(10455,20),new NeedVO(10456,20)]);
         this._list[this._list.length] = new ShenZDZVO(25351,600000,1500,500,[new NeedVO(10461,8),new NeedVO(10462,20),new NeedVO(10464,20)]);
         this._list[this._list.length] = new ShenZDZVO(20451,600000,1500,500,[new NeedVO(10450,8),new NeedVO(10457,20),new NeedVO(10458,20)]);
         this._list[this._list.length] = new ShenZDZVO(21451,600000,1500,500,[new NeedVO(10450,8),new NeedVO(10457,20),new NeedVO(10458,20)]);
         this._list[this._list.length] = new ShenZDZVO(22451,600000,1500,500,[new NeedVO(10450,8),new NeedVO(10457,20),new NeedVO(10458,20)]);
         this._list[this._list.length] = new ShenZDZVO(23451,600000,1500,500,[new NeedVO(10450,8),new NeedVO(10457,20),new NeedVO(10458,20)]);
         this._list[this._list.length] = new ShenZDZVO(24451,600000,1500,500,[new NeedVO(10450,8),new NeedVO(10457,20),new NeedVO(10458,20)]);
         this._list[this._list.length] = new ShenZDZVO(25451,600000,1500,500,[new NeedVO(10461,8),new NeedVO(10463,20),new NeedVO(10465,20)]);
      }
      
      public function get list() : Array
      {
         return this._list;
      }
   }
}

