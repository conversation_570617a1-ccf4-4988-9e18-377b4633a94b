package file
{
   import mogames.gameData.base.func.PFJLVO;
   import mogames.gameData.base.vo.NeedVO;
   
   public class PFJLConfig
   {
      
      private static var _instance:PFJLConfig;
      
      private var _list:Vector.<PFJLVO>;
      
      public function PFJLConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : PFJLConfig
      {
         if(!_instance)
         {
            _instance = new PFJLConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = new Vector.<PFJLVO>();
         this._list[this._list.length] = new PFJLVO(40101,40102,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(40201,40202,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(40301,40302,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(40401,40402,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(40501,40502,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(40601,40602,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(40701,40702,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(40801,40802,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(40901,40902,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(41001,41002,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(41101,41102,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(41201,41202,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(41301,41302,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(41401,41402,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(41501,41502,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(41601,41602,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(41701,41702,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(41801,41802,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(41901,41902,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42001,42002,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42101,42102,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42111,42112,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42121,42122,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42131,42132,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42141,42142,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42151,42152,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42161,42162,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42171,42172,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42181,42182,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42191,42192,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42201,42202,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42211,42212,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42221,42222,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42231,42232,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42241,42242,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42251,42252,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42261,42262,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42271,42272,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42281,42282,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42291,42292,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42301,42302,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42311,42312,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42321,42322,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42331,42332,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42341,42342,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42351,42352,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42361,42362,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42371,42372,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42381,42382,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42391,42392,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42401,42402,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42411,42412,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42421,42422,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42431,42432,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42441,42442,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42451,42452,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42461,42462,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42471,42472,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42481,42482,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42491,42492,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42501,42502,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42511,42512,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42521,42522,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42531,42532,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42541,42542,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(42551,42552,[new NeedVO(10000,20000),new NeedVO(10851,5)]);
         this._list[this._list.length] = new PFJLVO(40102,40103,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(40202,40203,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(40302,40303,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(40402,40403,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(40502,40503,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(40602,40603,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(40702,40703,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(40802,40803,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(40902,40903,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(41002,41003,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(41102,41103,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(41202,41203,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(41302,41303,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(41402,41403,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(41502,41503,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(41602,41603,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(41702,41703,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(41802,41803,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(41902,41903,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42002,42003,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42102,42103,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42112,42113,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42122,42123,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42132,42133,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42142,42143,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42152,42153,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42162,42163,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42172,42173,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42182,42183,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42192,42193,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42202,42203,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42212,42213,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42222,42223,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42232,42233,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42242,42243,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42252,42253,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42262,42263,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42272,42273,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42282,42283,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42292,42293,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42302,42303,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42312,42313,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42322,42323,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42332,42333,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42342,42343,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42352,42353,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42362,42363,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42372,42373,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42382,42383,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42392,42393,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42402,42403,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42412,42413,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42422,42423,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42432,42433,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42442,42443,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42452,42453,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42462,42463,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42472,42473,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42482,42483,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42492,42493,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42502,42503,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42512,42513,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42522,42523,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42532,42533,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42542,42543,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(42552,42553,[new NeedVO(10000,50000),new NeedVO(10852,5)]);
         this._list[this._list.length] = new PFJLVO(40103,40104,[new NeedVO(10000,100000),new NeedVO(10853,5)]);
         this._list[this._list.length] = new PFJLVO(40203,40204,[new NeedVO(10000,100000),new NeedVO(10853,5)]);
         this._list[this._list.length] = new PFJLVO(40403,40404,[new NeedVO(10000,100000),new NeedVO(10853,5)]);
         this._list[this._list.length] = new PFJLVO(40503,40504,[new NeedVO(10000,100000),new NeedVO(10853,5)]);
         this._list[this._list.length] = new PFJLVO(40603,40604,[new NeedVO(10000,100000),new NeedVO(10853,5)]);
         this._list[this._list.length] = new PFJLVO(40703,40704,[new NeedVO(10000,100000),new NeedVO(10853,5)]);
         this._list[this._list.length] = new PFJLVO(40903,40904,[new NeedVO(10000,100000),new NeedVO(10853,5)]);
         this._list[this._list.length] = new PFJLVO(41003,41004,[new NeedVO(10000,100000),new NeedVO(10853,5)]);
         this._list[this._list.length] = new PFJLVO(41103,41104,[new NeedVO(10000,100000),new NeedVO(10853,5)]);
         this._list[this._list.length] = new PFJLVO(41403,41404,[new NeedVO(10000,100000),new NeedVO(10853,5)]);
         this._list[this._list.length] = new PFJLVO(41503,41504,[new NeedVO(10000,100000),new NeedVO(10853,5)]);
         this._list[this._list.length] = new PFJLVO(41703,41704,[new NeedVO(10000,100000),new NeedVO(10853,5)]);
         this._list[this._list.length] = new PFJLVO(42003,42004,[new NeedVO(10000,100000),new NeedVO(10853,5)]);
         this._list[this._list.length] = new PFJLVO(42133,42134,[new NeedVO(10000,100000),new NeedVO(10853,5)]);
         this._list[this._list.length] = new PFJLVO(42173,42174,[new NeedVO(10000,100000),new NeedVO(10853,5)]);
         this._list[this._list.length] = new PFJLVO(42143,42144,[new NeedVO(10000,100000),new NeedVO(10853,5)]);
         this._list[this._list.length] = new PFJLVO(42213,42214,[new NeedVO(10000,100000),new NeedVO(10853,5)]);
         this._list[this._list.length] = new PFJLVO(42253,42254,[new NeedVO(10000,100000),new NeedVO(10853,5)]);
         this._list[this._list.length] = new PFJLVO(42263,42264,[new NeedVO(10000,100000),new NeedVO(10853,5)]);
         this._list[this._list.length] = new PFJLVO(42273,42274,[new NeedVO(10000,100000),new NeedVO(10853,5)]);
         this._list[this._list.length] = new PFJLVO(42363,42364,[new NeedVO(10000,100000),new NeedVO(10853,5)]);
         this._list[this._list.length] = new PFJLVO(42383,42384,[new NeedVO(10000,100000),new NeedVO(10853,5)]);
         this._list[this._list.length] = new PFJLVO(42403,42404,[new NeedVO(10000,100000),new NeedVO(10853,5)]);
         this._list[this._list.length] = new PFJLVO(42473,42474,[new NeedVO(10000,100000),new NeedVO(10853,5)]);
         this._list[this._list.length] = new PFJLVO(42513,42514,[new NeedVO(10000,100000),new NeedVO(10853,5)]);
         this._list[this._list.length] = new PFJLVO(42543,42544,[new NeedVO(10000,100000),new NeedVO(10853,5)]);
         this._list[this._list.length] = new PFJLVO(42553,42554,[new NeedVO(10000,100000),new NeedVO(10853,5)]);
         this._list[this._list.length] = new PFJLVO(40104,40105,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(40204,40205,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(40303,40305,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(40404,40405,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(40504,40505,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(40604,40605,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(40704,40705,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(40803,40805,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(40904,40905,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(41004,41005,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(41104,41105,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(41203,41205,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(41303,41305,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(41404,41405,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(41504,41505,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(41603,41605,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(41704,41705,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(41803,41805,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(41903,41905,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42004,42005,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42103,42105,[new NeedVO(10000,200000)]);
         this._list[this._list.length] = new PFJLVO(42113,42115,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42123,42125,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42134,42135,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42144,42145,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42153,42155,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42163,42165,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42174,42175,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42193,42195,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42203,42205,[new NeedVO(10000,200000)]);
         this._list[this._list.length] = new PFJLVO(42214,42215,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42223,42225,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42233,42235,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42243,42245,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42254,42255,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42264,42265,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42274,42275,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42283,42285,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42293,42295,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42303,42305,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42503,42505,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42313,42315,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42323,42325,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42333,42335,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42343,42345,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42353,42355,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42364,42365,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42373,42375,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42384,42385,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42393,42395,[new NeedVO(10000,200000)]);
         this._list[this._list.length] = new PFJLVO(42404,42405,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42413,42415,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42423,42425,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42433,42435,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42443,42445,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42453,42455,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42463,42465,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42474,42475,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42483,42485,[new NeedVO(10000,200000)]);
         this._list[this._list.length] = new PFJLVO(42493,42495,[new NeedVO(10000,200000)]);
         this._list[this._list.length] = new PFJLVO(42514,42515,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42523,42525,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42533,42535,[new NeedVO(10000,200000)]);
         this._list[this._list.length] = new PFJLVO(42544,42545,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(42554,42555,[new NeedVO(10000,200000),new NeedVO(10854,5)]);
         this._list[this._list.length] = new PFJLVO(40105,40106,[new NeedVO(10000,888888),new NeedVO(10855,5)]);
         this._list[this._list.length] = new PFJLVO(40205,40206,[new NeedVO(10000,888888),new NeedVO(10855,5)]);
         this._list[this._list.length] = new PFJLVO(40505,40506,[new NeedVO(10000,888888)]);
         this._list[this._list.length] = new PFJLVO(40605,40606,[new NeedVO(10000,888888),new NeedVO(10855,5)]);
         this._list[this._list.length] = new PFJLVO(40705,40706,[new NeedVO(10000,888888),new NeedVO(10855,5)]);
         this._list[this._list.length] = new PFJLVO(40905,40906,[new NeedVO(10000,888888)]);
         this._list[this._list.length] = new PFJLVO(41005,41006,[new NeedVO(10000,888888),new NeedVO(10855,5)]);
         this._list[this._list.length] = new PFJLVO(41105,41106,[new NeedVO(10000,888888),new NeedVO(10855,5)]);
         this._list[this._list.length] = new PFJLVO(41405,41406,[new NeedVO(10000,888888),new NeedVO(10855,5)]);
         this._list[this._list.length] = new PFJLVO(41505,41506,[new NeedVO(10000,888888),new NeedVO(10855,5)]);
         this._list[this._list.length] = new PFJLVO(41705,41706,[new NeedVO(10000,888888),new NeedVO(10855,5)]);
         this._list[this._list.length] = new PFJLVO(42005,42006,[new NeedVO(10000,888888)]);
         this._list[this._list.length] = new PFJLVO(42105,42106,[new NeedVO(10000,888888)]);
         this._list[this._list.length] = new PFJLVO(42115,42116,[new NeedVO(10000,888888),new NeedVO(10855,5)]);
         this._list[this._list.length] = new PFJLVO(42135,42136,[new NeedVO(10000,888888),new NeedVO(10855,5)]);
         this._list[this._list.length] = new PFJLVO(42145,42146,[new NeedVO(10000,888888),new NeedVO(10855,5)]);
         this._list[this._list.length] = new PFJLVO(42175,42176,[new NeedVO(10000,888888)]);
         this._list[this._list.length] = new PFJLVO(42215,42216,[new NeedVO(10000,888888),new NeedVO(10855,5)]);
         this._list[this._list.length] = new PFJLVO(42235,42236,[new NeedVO(10000,888888),new NeedVO(10855,5)]);
         this._list[this._list.length] = new PFJLVO(42255,42256,[new NeedVO(10000,888888),new NeedVO(10855,5)]);
         this._list[this._list.length] = new PFJLVO(42265,42266,[new NeedVO(10000,888888),new NeedVO(10855,5)]);
         this._list[this._list.length] = new PFJLVO(42305,42306,[new NeedVO(10000,888888),new NeedVO(10855,5)]);
         this._list[this._list.length] = new PFJLVO(42335,42336,[new NeedVO(10000,888888)]);
         this._list[this._list.length] = new PFJLVO(42345,42346,[new NeedVO(10000,888888),new NeedVO(10855,5)]);
         this._list[this._list.length] = new PFJLVO(42355,42356,[new NeedVO(10000,888888),new NeedVO(10855,5)]);
         this._list[this._list.length] = new PFJLVO(42365,42366,[new NeedVO(10000,888888)]);
         this._list[this._list.length] = new PFJLVO(42385,42386,[new NeedVO(10000,888888),new NeedVO(10855,5)]);
         this._list[this._list.length] = new PFJLVO(42405,42406,[new NeedVO(10000,888888),new NeedVO(10855,5)]);
         this._list[this._list.length] = new PFJLVO(42415,42416,[new NeedVO(10000,888888)]);
         this._list[this._list.length] = new PFJLVO(42475,42476,[new NeedVO(10000,888888),new NeedVO(10855,5)]);
         this._list[this._list.length] = new PFJLVO(42515,42516,[new NeedVO(10000,888888),new NeedVO(10855,5)]);
         this._list[this._list.length] = new PFJLVO(42545,42546,[new NeedVO(10000,888888),new NeedVO(10855,5)]);
         this._list[this._list.length] = new PFJLVO(42555,42556,[new NeedVO(10000,888888),new NeedVO(10855,5)]);
         this._list[this._list.length] = new PFJLVO(42564,42566,[new NeedVO(10000,888888),new NeedVO(10855,5)]);
         this._list[this._list.length] = new PFJLVO(40106,40107,[new NeedVO(10000,999999),new NeedVO(10856,5)]);
         this._list[this._list.length] = new PFJLVO(40206,40207,[new NeedVO(10000,999999),new NeedVO(10856,5)]);
         this._list[this._list.length] = new PFJLVO(41506,41507,[new NeedVO(10000,999999),new NeedVO(10856,5)]);
         this._list[this._list.length] = new PFJLVO(41706,41707,[new NeedVO(10000,999999),new NeedVO(10856,5)]);
         this._list[this._list.length] = new PFJLVO(42116,42117,[new NeedVO(10000,999999),new NeedVO(10856,5)]);
         this._list[this._list.length] = new PFJLVO(42146,42147,[new NeedVO(10000,999999),new NeedVO(10856,5)]);
         this._list[this._list.length] = new PFJLVO(42386,42387,[new NeedVO(10000,999999),new NeedVO(10856,5)]);
         this._list[this._list.length] = new PFJLVO(42556,42557,[new NeedVO(10000,999999),new NeedVO(10856,5)]);
         this._list[this._list.length] = new PFJLVO(42216,42217,[new NeedVO(10000,999999),new NeedVO(10856,5)]);
         this._list[this._list.length] = new PFJLVO(42256,42257,[new NeedVO(10000,999999),new NeedVO(10856,5)]);
         this._list[this._list.length] = new PFJLVO(42516,42517,[new NeedVO(10000,999999),new NeedVO(10856,5)]);
         this._list[this._list.length] = new PFJLVO(42566,42567,[new NeedVO(10000,999999),new NeedVO(10856,5)]);
      }
      
      public function findVO(param1:int) : PFJLVO
      {
         var _loc2_:PFJLVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.oldID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

