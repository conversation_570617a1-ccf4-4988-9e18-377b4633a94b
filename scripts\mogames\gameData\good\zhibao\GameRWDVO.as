package mogames.gameData.good.zhibao
{
   import com.mogames.utils.MathUtil;
   import com.mogames.utils.TxtUtil;
   import mogames.gameData.good.base.ConstEquipVO;
   import mogames.gameData.good.equip.GameZhiBaoVO;
   import mogames.gameData.role.battle.HurtData;
   import mogames.gameEffect.EffectManager;
   import mogames.gameRole.base.IRole;
   import mogames.gameRole.view.HeroBaseView;
   
   public class GameRWDVO extends GameZhiBaoVO
   {
      
      public function GameRWDVO(param1:ConstEquipVO)
      {
         super(param1);
      }
      
      override public function hurtBefore(param1:IRole, param2:HurtData) : void
      {
         if(!MathUtil.checkOdds(20))
         {
            return;
         }
         param2.hurtValue = 0;
         (param1.view as HeroBaseView).startZBEffect();
         EffectManager.addHeadWord(TxtUtil.setColor("法宝免伤！","ffff00"),param1.x,param1.y - param1.height);
      }
   }
}

