package mogames.gameData.mission.data
{
   import com.mogames.data.Oint;
   import com.mogames.event.GameSignal;
   import com.mogames.event.SignalManager;
   import com.mogames.utils.MathUtil;
   import mogames.gameData.vip.VipProxy;
   
   public class BattleResData
   {
      
      private var _curWood:Oint = new Oint();
      
      private var _curFood:Oint = new Oint();
      
      public function BattleResData()
      {
         super();
      }
      
      public function init() : void
      {
         if(VipProxy.instance().hasFunc(209))
         {
            MathUtil.saveINT(this._curWood,200);
            MathUtil.saveINT(this._curFood,200);
         }
         else
         {
            MathUtil.saveINT(this._curWood,0);
            MathUtil.saveINT(this._curFood,0);
         }
      }
      
      public function changeWood(param1:int) : void
      {
         var _loc2_:int = MathUtil.loadINT(this._curWood) + param1;
         if(_loc2_ <= 0)
         {
            _loc2_ = 0;
         }
         MathUtil.saveINT(this._curWood,_loc2_);
         SignalManager.signalRes.dispatchEvent({
            "signal":GameSignal.REFRESH_RES,
            "type":"refreshWood"
         });
      }
      
      public function changeFood(param1:int) : void
      {
         var _loc2_:int = MathUtil.loadINT(this._curFood) + param1;
         if(_loc2_ <= 0)
         {
            _loc2_ = 0;
         }
         MathUtil.saveINT(this._curFood,_loc2_);
         SignalManager.signalRes.dispatchEvent({
            "signal":GameSignal.REFRESH_RES,
            "type":"refreshFood"
         });
      }
      
      public function isLackWood(param1:int) : Boolean
      {
         return this.curWood < param1;
      }
      
      public function isLackFood(param1:int) : Boolean
      {
         return this.curFood < param1;
      }
      
      public function get curWood() : int
      {
         return MathUtil.loadINT(this._curWood);
      }
      
      public function get curFood() : int
      {
         return MathUtil.loadINT(this._curFood);
      }
   }
}

