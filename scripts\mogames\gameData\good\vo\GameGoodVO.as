package mogames.gameData.good.vo
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import com.mogames.utils.TxtUtil;
   import mogames.ConstData;
   import mogames.gameData.good.base.ConstGoodVO;
   
   public class GameGoodVO
   {
      
      protected var _constVO:ConstGoodVO;
      
      protected var _amount:Oint = new Oint();
      
      public function GameGoodVO(param1:ConstGoodVO)
      {
         super();
         this._constVO = param1;
         MathUtil.saveINT(this._amount,1);
      }
      
      public function set amount(param1:int) : void
      {
         MathUtil.saveINT(this._amount,param1);
      }
      
      public function get amount() : int
      {
         return MathUtil.loadINT(this._amount);
      }
      
      public function get quality() : int
      {
         return this._constVO.quality;
      }
      
      public function get constGood() : ConstGoodVO
      {
         return this._constVO;
      }
      
      public function get goodID() : int
      {
         return this._constVO.id;
      }
      
      public function get qualityName() : String
      {
         return TxtUtil.setColor(this._constVO.name,ConstData.GOOD_COLOR1[this.quality]);
      }
      
      public function get infor() : String
      {
         return this._constVO.infor;
      }
      
      public function get saveData() : Object
      {
         return this._constVO.id + "H" + this.amount;
      }
   }
}

