package mogames.gameData.mission.extra
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ExtraWave2513
   {
      
      public function ExtraWave2513()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2513);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(779,592000000,2880000,1000000,120,150,300,90,new BossSkillData0(150,{"hurt":5000000},3),1009,0);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(776,11350000,575000,520000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(776,11350000,575000,520000,100,100,200,120,0)));
         _loc2_.addFu(new <PERSON>ArgVO(777,92000000,1050000,550000,100,120,150,80,new BossSkillData1(12,{
            "hurt":800000,
            "roleNum":20
         },3),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(774,11350000,575000,520000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(774,11350000,575000,520000,100,100,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(774,11350000,575000,520000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(774,11350000,575000,520000,100,100,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(774,11350000,575000,520000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(774,11350000,575000,520000,100,100,200,120,0)));
         _loc2_.addFu(new BossArgVO(778,92000000,1050000,550000,100,120,150,80,new BossSkillData1(12,{
            "hurt":800000,
            "roleNum":20
         },3),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(776,11350000,575000,520000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(774,2450000,200000,110,100,100,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(776,11350000,575000,520000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(13,new RoleArgVO(774,11350000,575000,520000,100,100,200,120,0)));
         _loc2_.addFu(new BossArgVO(779,92000000,1050000,550000,100,120,150,80,new BossSkillData0(250,{
            "hurt":800000,
            "hurtCount":5
         },3),1003,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(774,11350000,575000,520000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(774,2450000,200000,110,100,100,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(774,11350000,575000,520000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(776,11350000,575000,520000,100,100,200,120,0)));
         _loc2_.addFu(new BossArgVO(778,92000000,1050000,550000,100,120,150,80,new BossSkillData0(250,{
            "hurt":800000,
            "roleNum":3
         },3),1004,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(776,11350000,575000,520000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(774,11350000,575000,520000,100,100,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(774,11350000,575000,520000,100,100,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(774,2450000,200000,110,100,100,200,120,0)));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

