package mogames.gameData.flag.base
{
   public class GameFlag
   {
      
      protected var _flags:Array;
      
      public function GameFlag()
      {
         super();
         this._flags = [];
      }
      
      public function startNew() : void
      {
         this._flags.length = 0;
      }
      
      public function set loadData(param1:Array) : void
      {
         var _loc2_:Array = null;
         var _loc3_:FlagVO = null;
         var _loc4_:String = null;
         if(!param1)
         {
            return;
         }
         for each(_loc4_ in param1)
         {
            _loc2_ = _loc4_.split("H");
            _loc3_ = this.findFlag(int(_loc2_[0]));
            if(_loc3_)
            {
               _loc3_.setValue(_loc2_[1]);
            }
         }
      }
      
      public function get saveData() : Array
      {
         var _loc2_:FlagVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._flags)
         {
            if(_loc2_.needSave)
            {
               _loc1_[_loc1_.length] = _loc2_.id + "H" + _loc2_.cur;
            }
         }
         return _loc1_;
      }
      
      public function dailyRefresh() : void
      {
         var _loc1_:FlagVO = null;
         for each(_loc1_ in this._flags)
         {
            _loc1_.dailyRefresh();
         }
      }
      
      public function addFlag(param1:FlagVO) : void
      {
         this._flags[this._flags.length] = param1;
      }
      
      public function changeValue(param1:int, param2:int = 1) : void
      {
         var _loc3_:FlagVO = this.findFlag(param1);
         if(_loc3_)
         {
            _loc3_.changeValue(param2);
         }
      }
      
      public function setValue(param1:int, param2:int = 1) : void
      {
         var _loc3_:FlagVO = this.findFlag(param1);
         if(_loc3_)
         {
            _loc3_.setValue(param2);
         }
      }
      
      public function isComplete(param1:int) : Boolean
      {
         var _loc2_:FlagVO = this.findFlag(param1);
         if(!_loc2_)
         {
            return false;
         }
         return _loc2_.isComplete;
      }
      
      public function findFlag(param1:int) : FlagVO
      {
         var _loc2_:FlagVO = null;
         for each(_loc2_ in this._flags)
         {
            if(_loc2_.id == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

