package com.mogames.data
{
   public class FuncVector
   {
      
      private var _list:Vector.<Function>;
      
      public function FuncVector()
      {
         super();
         this._list = new Vector.<Function>();
      }
      
      public function add(param1:Function) : void
      {
         if(this._list.indexOf(param1) != -1)
         {
            return;
         }
         this._list[this._list.length] = param1;
      }
      
      public function remove(param1:Function) : void
      {
         var _loc2_:int = int(this._list.indexOf(param1));
         if(_loc2_ != -1)
         {
            this._list.splice(_loc2_,1);
         }
      }
      
      public function get funcs() : Vector.<Function>
      {
         return this._list;
      }
      
      public function clean() : void
      {
         this._list.length = 0;
      }
   }
}

