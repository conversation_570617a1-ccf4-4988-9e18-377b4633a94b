package mogames.gameData.mission.extra
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ExtraWave2122
   {
      
      public function ExtraWave2122()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2122);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(822,900000,4800,700,80,80,300,90,new Boss<PERSON>killData0(150,{"hurt":15000},3),1009,0);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(273,21500,2280,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(273,21500,2280,110,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(278,300000,4600,450,50,80,150,80,new BossSkillData1(12,{
            "hurt":5000,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(272,21500,2280,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(272,21500,2280,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(272,21500,2280,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(272,21500,2280,110,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(278,300000,4600,450,50,80,150,80,new BossSkillData1(12,{
            "hurt":6000,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(273,21500,2280,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(274,21500,2280,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(272,21500,2280,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(272,21500,2280,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(273,21500,2280,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(275,21500,2280,110,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(278,300000,4600,450,50,80,150,80,new BossSkillData0(150,{
            "hurt":6000,
            "hurtCount":5
         },2),1003,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(272,21500,2280,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(274,21500,2280,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(273,21500,2280,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(275,21500,2280,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(272,21500,2280,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(273,21500,2280,110,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(278,300000,4600,450,50,80,150,80,new BossSkillData0(150,{
            "hurt":6000,
            "roleNum":3
         },2),1004,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(272,21500,2280,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(274,21500,2280,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

