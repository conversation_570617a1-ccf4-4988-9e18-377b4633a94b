package com.mogames.others
{
   import com.mogames.utils.MathUtil;
   import flash.geom.Point;
   
   public class RandCircle
   {
      
      private var _center:Point;
      
      private var _radius:Number;
      
      public function RandCircle(param1:Point, param2:Number)
      {
         super();
         this._center = param1;
         this._radius = param2;
      }
      
      public function get randPoint() : Point
      {
         var _loc1_:Number = Math.random() * Math.PI * (MathUtil.checkOdds(500) ? 1 : -1);
         var _loc2_:Number = Math.random() * this._radius;
         return new Point(int(_loc2_ * Math.cos(_loc1_) + this._center.x),int(_loc2_ * Math.sin(_loc1_) + this._center.y));
      }
   }
}

