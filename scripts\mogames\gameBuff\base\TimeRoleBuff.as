package mogames.gameBuff.base
{
   import mogames.gameData.buff.ConstBuffVO;
   import mogames.gameData.buff.base.BuffVO;
   import mogames.gameRole.base.IRole;
   
   public class TimeRoleBuff extends RoleBuff
   {
      
      public function TimeRoleBuff(param1:ConstBuffVO)
      {
         super(param1);
      }
      
      override public function start(param1:IRole, param2:BuffVO) : void
      {
         super.start(param1,param2);
         this.createData();
         _timer.setTimeOut(_buffVO.time,handlerEnd);
      }
      
      protected function createData() : void
      {
      }
   }
}

