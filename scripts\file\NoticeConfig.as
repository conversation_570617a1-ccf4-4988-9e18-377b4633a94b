package file
{
   import com.mogames.utils.TxtUtil;
   
   public class NoticeConfig
   {
      
      private static var _instance:NoticeConfig;
      
      public var notices:String;
      
      public var url:String;
      
      public function NoticeConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : NoticeConfig
      {
         if(!_instance)
         {
            _instance = new NoticeConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this.url = "https://my.4399.com/forums/thread-64200527" + "" + "";
         this.notices = TxtUtil.setColor("惊天神将V24.9更新：","FF00FF") + "<br>" + TxtUtil.setColor("风云活动：02.18-03.03","00FF00") + " <br>" + TxtUtil.setColor("\t 充值礼包","FFFFFF") + "<br>" + TxtUtil.setColor("\t 幸运转盘调整道具","FFFFFF") + "<br>" + TxtUtil.setColor("\t 免费砸蛋新增皮肤:吕蒙、李典、邓艾、荀攸、夏侯敦","FFFFFF") + "<br>" + TxtUtil.setColor("1、新增[真]至宝：青面砍刀、火玉珠","00FF00") + "<br>" + TxtUtil.setColor("2、新增[真]至宝熔炼进阶：绿蕴环、火灵环","00FF00") + "<br>" + TxtUtil.setColor("3、调整活跃度材料奖励：檀香炉材料","00FF00") + "<br>" + TxtUtil.setColor("4、提升至宝玉珠古壶属性","00FF00") + "<br>" + TxtUtil.setColor("5、免费砸蛋取消皮肤掉落：鲁肃、黄忠、许褚、徐晃、周瑜","00FF00") + "<br>" + TxtUtil.setColor("6、返利活动结束","00FF00") + "<br>" + TxtUtil.setColor("玩家交流群：577965374","FF00FF") + "<br>";
      }
   }
}

