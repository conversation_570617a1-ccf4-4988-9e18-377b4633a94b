package mogames.gameData.mission.base
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import file.SecretConfig;
   import mogames.gameData.mission.SecretProxy;
   
   public class SecretConstVO
   {
      
      private var _areaID:int;
      
      private var _mid:Oint = new Oint();
      
      private var _open:Oint = new Oint();
      
      private var _rewards:Array;
      
      private var _index:int;
      
      public function SecretConstVO(param1:int, param2:int, param3:int, param4:Array, param5:int)
      {
         super();
         MathUtil.saveINT(this._mid,param2);
         MathUtil.saveINT(this._open,param3);
         this._areaID = param1;
         this._rewards = param4;
         this._index = param5;
      }
      
      public function get mid() : int
      {
         return MathUtil.loadINT(this._mid);
      }
      
      public function get isOpen() : Boolean
      {
         var _loc1_:int = MathUtil.loadINT(this._open);
         if(_loc1_ == 0)
         {
            return true;
         }
         return SecretProxy.instance().isFinish(_loc1_);
      }
      
      public function get lockStr() : String
      {
         var _loc1_:SecretConstVO = SecretConfig.instance().findConst(MathUtil.loadINT(this._open));
         if(_loc1_.isReward)
         {
            return "请先领取前面宝箱奖励！";
         }
         return "请先通关前一关！";
      }
      
      public function get isReward() : Boolean
      {
         return this._rewards != null;
      }
      
      public function get rewards() : Array
      {
         return this._rewards;
      }
      
      public function get areaID() : int
      {
         return this._areaID;
      }
      
      public function get index() : int
      {
         return this._index;
      }
   }
}

