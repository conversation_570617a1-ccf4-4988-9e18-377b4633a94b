package mogames.gameData.main
{
   import mogames.ConstData;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameData.tavern.TavernHeroProxy;
   
   public class MainProxy
   {
      
      private static var _instance:MainProxy;
      
      private var _happens:Array;
      
      public function MainProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this._happens = [];
      }
      
      public static function instance() : MainProxy
      {
         if(!_instance)
         {
            _instance = new MainProxy();
         }
         return _instance;
      }
      
      public function addTip(param1:String) : void
      {
         this._happens[this._happens.length] = param1;
      }
      
      public function refreshHappen() : void
      {
         this._happens.length = 0;
      }
      
      public function get happens() : Array
      {
         return this._happens;
      }
      
      public function cleanHero(param1:int) : void
      {
         TaskProxy.instance().removeTask(param1,ConstData.TASK_TAVERN);
         TavernHeroProxy.instance().removeByID(param1);
      }
   }
}

