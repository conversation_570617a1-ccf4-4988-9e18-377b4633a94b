package mogames.gameData.depot
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import file.GoodConfig;
   import flash.utils.Dictionary;
   import mogames.ConstData;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.game.LackVO;
   import mogames.gameData.good.base.ConstBagVO;
   import mogames.gameData.good.base.ConstEquipVO;
   import mogames.gameData.good.equip.GameEquipVO;
   import mogames.gameData.good.equip.GamePetEquipVO;
   import mogames.gameData.good.vo.GameGoodVO;
   import mogames.gameData.master.MasterProxy;
   import mogames.gameData.mission.MissionProxy;
   import mogames.gameData.role.hero.HeroGameVO;
   import mogames.gameData.vip.VipProxy;
   import mogames.gameUI.bagtool.DataToolModule;
   
   public class DepotProxy
   {
      
      private static var _instance:DepotProxy;
      
      private static const MAX_DUI:int = 99;
      
      private var _default:Oint = new Oint();
      
      private var _grid:Oint = new Oint();
      
      private var _level:Oint = new Oint();
      
      private var _star0:Oint = new Oint();
      
      private var _star1:Oint = new Oint();
      
      private var _dict:Dictionary;
      
      public function DepotProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this._dict = new Dictionary();
         MathUtil.saveINT(this._default,10);
         MathUtil.saveINT(this._grid,10);
         MathUtil.saveINT(this._level,30);
         MathUtil.saveINT(this._star0,20);
         MathUtil.saveINT(this._star1,25);
      }
      
      public static function instance() : DepotProxy
      {
         if(!_instance)
         {
            _instance = new DepotProxy();
         }
         return _instance;
      }
      
      public function startNew() : void
      {
         this._dict[ConstData.GOOD_PROP] = [];
         this._dict[ConstData.GOOD_EQUIP] = [];
         this._dict[ConstData.GOOD_ZHIBAO] = [];
         this._dict[ConstData.GOOD_FASHION] = [];
         this._dict[ConstData.GOOD_TOKEN] = [];
         this._dict[ConstData.GOOD_CLIP] = [];
         this._dict[ConstData.GOOD_OTHER] = [];
         this._dict[ConstData.GOOD_PET] = [];
      }
      
      public function set loadData(param1:Object) : void
      {
         this.startNew();
         if(!param1)
         {
            return;
         }
         this.parseGood(param1.prop);
         this.parseEquip(param1.equip);
         this.parseEquip(param1.zhibao);
         this.parseEquip(param1.fashion);
         this.parseGood(param1.token);
         this.parseGood(param1.clip);
         this.parseGood(param1.other);
         this.parsePetEquip(param1.pet);
      }
      
      public function get saveData() : Object
      {
         return {
            "prop":this.collectGood(this._dict[ConstData.GOOD_PROP]),
            "equip":this.collectEquip(this._dict[ConstData.GOOD_EQUIP]),
            "zhibao":this.collectEquip(this._dict[ConstData.GOOD_ZHIBAO]),
            "fashion":this.collectEquip(this._dict[ConstData.GOOD_FASHION]),
            "token":this.collectGood(this._dict[ConstData.GOOD_TOKEN]),
            "clip":this.collectGood(this._dict[ConstData.GOOD_CLIP]),
            "other":this.collectGood(this._dict[ConstData.GOOD_OTHER]),
            "pet":this.collectPetEquip(this._dict[ConstData.GOOD_PET])
         };
      }
      
      public function initDepot() : void
      {
         if(this.findNum(70001) != 0)
         {
            return;
         }
         this.addDirect(GoodConfig.instance().newGameGood(70001));
      }
      
      public function useItems(param1:int, param2:int = 1) : void
      {
         if(param2 <= 0)
         {
            return;
         }
         var _loc3_:int = 0;
         while(_loc3_ < param2)
         {
            this.useItemByID(param1);
            _loc3_++;
         }
         var _loc4_:int = this.findNum(param1);
         if(_loc4_ <= MAX_DUI)
         {
            return;
         }
         this.delItemByID(param1);
         var _loc5_:GameGoodVO = GoodConfig.instance().newGameGood(param1);
         _loc5_.amount = _loc4_;
         this.addMulitItem(_loc5_);
      }
      
      public function useItemByID(param1:int) : void
      {
         var _loc4_:GameGoodVO = null;
         var _loc2_:Array = this._dict[GoodConfig.instance().findGoodLabel(param1)];
         var _loc3_:int = int(_loc2_.length - 1);
         while(_loc3_ >= 0)
         {
            _loc4_ = _loc2_[_loc3_];
            if(_loc4_.constGood.id == param1)
            {
               --_loc4_.amount;
               if(_loc4_.amount <= 0)
               {
                  _loc2_.splice(_loc3_,1);
               }
               return;
            }
            _loc3_--;
         }
      }
      
      public function delItemByID(param1:int) : void
      {
         var _loc2_:Array = this._dict[GoodConfig.instance().findGoodLabel(param1)];
         var _loc3_:int = 0;
         while(_loc3_ < _loc2_.length)
         {
            if(_loc2_[_loc3_].constGood.id == param1)
            {
               this.delItemByVO(_loc2_[_loc3_]);
               _loc3_--;
            }
            _loc3_++;
         }
      }
      
      public function delItemByVO(param1:GameGoodVO) : void
      {
         var _loc2_:Array = this._dict[GoodConfig.instance().findGoodLabel(param1.constGood.id)];
         var _loc3_:int = int(_loc2_.indexOf(param1));
         if(_loc3_ != -1)
         {
            _loc2_.splice(_loc3_,1);
         }
      }
      
      public function addItems(param1:Array) : void
      {
         var _loc2_:int = 0;
         var _loc3_:int = int(param1.length);
         while(_loc2_ < _loc3_)
         {
            this.addMulitItem(param1[_loc2_]);
            _loc2_++;
         }
      }
      
      public function addMulitItem(param1:GameGoodVO) : void
      {
         if(param1.constGood is ConstEquipVO)
         {
            this.addDirect(param1);
         }
         else if(param1.constGood.isPile == 0 && param1.amount == 1)
         {
            this.addDirect(param1);
         }
         else
         {
            this.addAmount(param1);
         }
      }
      
      private function addAmount(param1:GameGoodVO) : void
      {
         var _loc2_:int = 0;
         while(_loc2_ < param1.amount)
         {
            if(param1.constGood.isPile == 0)
            {
               this.addNoPileItem(param1.constGood.id);
            }
            else
            {
               this.addPileItem(param1.constGood.id);
            }
            _loc2_++;
         }
      }
      
      public function addPileItem(param1:int) : void
      {
         var _loc5_:GameGoodVO = null;
         var _loc2_:Array = this._dict[GoodConfig.instance().findGoodLabel(param1)];
         var _loc3_:int = 0;
         var _loc4_:int = int(_loc2_.length);
         while(_loc3_ < _loc4_)
         {
            _loc5_ = _loc2_[_loc3_];
            if(_loc5_.constGood.id == param1 && _loc5_.amount < MAX_DUI)
            {
               ++_loc5_.amount;
               return;
            }
            _loc3_++;
         }
         _loc2_[_loc2_.length] = GoodConfig.instance().newGameGood(param1);
      }
      
      public function addNoPileItem(param1:int) : void
      {
         var _loc2_:Array = this._dict[GoodConfig.instance().findGoodLabel(param1)];
         _loc2_[_loc2_.length] = GoodConfig.instance().newGameGood(param1);
      }
      
      public function addDirect(param1:GameGoodVO) : void
      {
         var _loc2_:Array = this._dict[GoodConfig.instance().findGoodLabel(param1.constGood.id)];
         _loc2_[_loc2_.length] = param1;
      }
      
      public function checkLack(param1:Array) : LackVO
      {
         if(param1[0].length > 0 && this.countNum(param1[0]) > this.curGrid - this._dict[ConstData.GOOD_PROP].length)
         {
            return new LackVO("【道具】栏空间不足！");
         }
         if(param1[1].length > 0 && this.countNum(param1[1]) > this.curGrid - this._dict[ConstData.GOOD_EQUIP].length)
         {
            return new LackVO("【装备】栏空间不足！");
         }
         if(param1[2].length > 0 && this.countNum(param1[2]) > this.curGrid - this._dict[ConstData.GOOD_ZHIBAO].length)
         {
            return new LackVO("【至宝】栏空间不足！");
         }
         if(param1[3].length > 0 && this.countNum(param1[3]) > this.curGrid - this._dict[ConstData.GOOD_FASHION].length)
         {
            return new LackVO("【皮肤】栏空间不足！");
         }
         if(param1[4].length > 0 && this.countNum(param1[4]) > this.curGrid - this._dict[ConstData.GOOD_TOKEN].length)
         {
            return new LackVO("【信物】栏空间不足！");
         }
         if(param1[5].length > 0 && this.countNum(param1[5]) > this.curGrid - this._dict[ConstData.GOOD_CLIP].length)
         {
            return new LackVO("【碎片】栏空间不足！");
         }
         if(param1[6].length > 0 && this.countNum(param1[6]) > this.curGrid - this._dict[ConstData.GOOD_OTHER].length)
         {
            return new LackVO("【其他】栏空间不足！");
         }
         if(param1[7].length > 0 && this.countNum(param1[7]) > this.curGrid - this._dict[ConstData.GOOD_PET].length)
         {
            return new LackVO("【兽装】栏空间不足！");
         }
         return null;
      }
      
      public function checkLabel(param1:int) : LackVO
      {
         if(this._dict[param1].length >= this.curGrid)
         {
            return new LackVO("国库空间不足！");
         }
         return null;
      }
      
      public function countNum(param1:Array) : int
      {
         var _loc3_:GameGoodVO = null;
         var _loc2_:int = 0;
         for each(_loc3_ in param1)
         {
            if(_loc3_.constGood.isPile)
            {
               _loc2_++;
            }
            else
            {
               _loc2_ += _loc3_.amount;
            }
         }
         return _loc2_;
      }
      
      public function findGoodList(param1:int) : Array
      {
         return this._dict[param1];
      }
      
      public function get curGrid() : int
      {
         return this.curLine * MathUtil.loadINT(this._grid);
      }
      
      public function get curLine() : int
      {
         if(DataToolModule.isActive)
         {
            return 32;
         }
         var _loc1_:int = MathUtil.loadINT(this._default);
         if(MissionProxy.instance().isFinish(1012))
         {
            _loc1_++;
         }
         if(MissionProxy.instance().curStar(201) >= MathUtil.loadINT(this._star0))
         {
            _loc1_++;
         }
         if(MissionProxy.instance().isFinish(1027))
         {
            _loc1_++;
         }
         if(MissionProxy.instance().curStar(202) >= MathUtil.loadINT(this._star1))
         {
            _loc1_++;
         }
         if(MasterProxy.instance().masterVO.level >= MathUtil.loadINT(this._level))
         {
            _loc1_++;
         }
         if(MissionProxy.instance().isFinish(1077))
         {
            _loc1_++;
         }
         if(MissionProxy.instance().isFinish(1117))
         {
            _loc1_++;
         }
         if(MissionProxy.instance().isFinish(1157))
         {
            _loc1_++;
         }
         if(MissionProxy.instance().isFinish(1197))
         {
            _loc1_++;
         }
         if(VipProxy.instance().hasFunc(108))
         {
            _loc1_++;
         }
         return FlagProxy.instance().limitFlag.findFlag(300).cur + _loc1_;
      }
      
      public function findNum(param1:int) : Number
      {
         var _loc2_:int = 0;
         var _loc4_:GameGoodVO = null;
         var _loc3_:Array = this._dict[GoodConfig.instance().findGoodLabel(param1)];
         for each(_loc4_ in _loc3_)
         {
            if(_loc4_.constGood.id == param1)
            {
               _loc2_ += _loc4_.amount;
            }
         }
         return _loc2_;
      }
      
      public function findHeroEquip(param1:HeroGameVO) : Array
      {
         var _loc4_:GameEquipVO = null;
         var _loc2_:Array = this._dict[ConstData.GOOD_EQUIP].concat(this._dict[ConstData.GOOD_ZHIBAO],this._dict[ConstData.GOOD_FASHION]);
         var _loc3_:Array = [];
         for each(_loc4_ in _loc2_)
         {
            if(!(_loc4_.constEquip.zhiyeLimit != -1 && _loc4_.constEquip.zhiyeLimit != param1.heroInfo.career))
            {
               if(!(_loc4_.constEquip.roleLimit != -1 && _loc4_.constEquip.roleLimit != param1.heroID))
               {
                  _loc3_[_loc3_.length] = _loc4_;
               }
            }
         }
         return _loc3_;
      }
      
      public function get cardEquips() : Array
      {
         var _loc3_:GameEquipVO = null;
         var _loc1_:Array = this._dict[ConstData.GOOD_EQUIP].concat(this._dict[ConstData.GOOD_ZHIBAO],this._dict[ConstData.GOOD_FASHION]);
         var _loc2_:Array = [];
         for each(_loc3_ in _loc1_)
         {
            if(_loc3_.constEquip.bodyType != 5)
            {
               _loc2_[_loc2_.length] = _loc3_;
            }
         }
         return _loc2_;
      }
      
      public function findBagGood() : Array
      {
         var _loc3_:GameGoodVO = null;
         var _loc1_:Array = this._dict[ConstData.GOOD_PROP];
         var _loc2_:Array = [];
         for each(_loc3_ in _loc1_)
         {
            if(_loc3_.constGood is ConstBagVO)
            {
               _loc2_[_loc2_.length] = _loc3_;
            }
         }
         return _loc2_;
      }
      
      private function collectGood(param1:Array) : Array
      {
         var _loc3_:GameGoodVO = null;
         var _loc2_:Array = [];
         for each(_loc3_ in param1)
         {
            _loc2_[_loc2_.length] = _loc3_.saveData;
         }
         return _loc2_;
      }
      
      private function parseGood(param1:Array) : void
      {
         var _loc2_:Array = null;
         var _loc3_:String = null;
         var _loc4_:GameGoodVO = null;
         for each(_loc3_ in param1)
         {
            _loc2_ = _loc3_.split("H");
            _loc4_ = GoodConfig.instance().newGameGood(int(_loc2_[0]));
            if(_loc4_)
            {
               _loc4_.amount = int(_loc2_[1]);
               this._dict[_loc4_.constGood.label].push(_loc4_);
            }
         }
      }
      
      private function collectEquip(param1:Array) : Array
      {
         var _loc3_:GameEquipVO = null;
         var _loc2_:Array = [];
         for each(_loc3_ in param1)
         {
            _loc2_[_loc2_.length] = _loc3_.saveData;
         }
         return _loc2_;
      }
      
      private function parseEquip(param1:Array) : void
      {
         var _loc2_:Array = null;
         var _loc3_:Object = null;
         var _loc4_:GameEquipVO = null;
         for each(_loc3_ in param1)
         {
            _loc4_ = GoodConfig.instance().newGameEquip(_loc3_.id,1);
            if(_loc4_)
            {
               _loc4_.loadData = _loc3_;
               this._dict[_loc4_.constGood.label].push(_loc4_);
            }
         }
      }
      
      private function collectPetEquip(param1:Array) : Array
      {
         var _loc3_:GamePetEquipVO = null;
         var _loc2_:Array = [];
         for each(_loc3_ in param1)
         {
            _loc2_[_loc2_.length] = _loc3_.saveData;
         }
         return _loc2_;
      }
      
      private function parsePetEquip(param1:Array) : void
      {
         var _loc2_:Array = null;
         var _loc3_:Object = null;
         var _loc4_:GamePetEquipVO = null;
         for each(_loc3_ in param1)
         {
            _loc4_ = GoodConfig.instance().newPetEquip(_loc3_.id,1);
            if(_loc4_)
            {
               _loc4_.loadData = _loc3_;
               this._dict[_loc4_.constGood.label].push(_loc4_);
            }
         }
      }
   }
}

