package file
{
   import mogames.gameData.base.vo.NeedVO;
   import mogames.gameData.tavern.vo.HireNeedVO;
   
   public class TavernConfig
   {
      
      private static var _instance:TavernConfig;
      
      private var _hires:Vector.<HireNeedVO>;
      
      public function TavernConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : TavernConfig
      {
         if(!_instance)
         {
            _instance = new TavernConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._hires = new Vector.<HireNeedVO>();
         this._hires[this._hires.length] = new HireNeedVO(300,[new NeedVO(10000,25000)]);
         this._hires[this._hires.length] = new HireNeedVO(301,[new NeedVO(10000,6000)]);
         this._hires[this._hires.length] = new HireNeedVO(302,[new NeedVO(10000,7000)]);
         this._hires[this._hires.length] = new HireNeedVO(303,[new NeedVO(10000,6000)]);
         this._hires[this._hires.length] = new HireNeedVO(304,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(305,[new NeedVO(10000,5000)]);
         this._hires[this._hires.length] = new HireNeedVO(306,[new NeedVO(10000,8000)]);
         this._hires[this._hires.length] = new HireNeedVO(307,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(308,[new NeedVO(10006,300)]);
         this._hires[this._hires.length] = new HireNeedVO(309,[new NeedVO(10000,7000)]);
         this._hires[this._hires.length] = new HireNeedVO(310,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(311,[new NeedVO(10000,6000)]);
         this._hires[this._hires.length] = new HireNeedVO(312,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(313,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(314,[new NeedVO(10000,9000)]);
         this._hires[this._hires.length] = new HireNeedVO(315,[new NeedVO(10000,8000)]);
         this._hires[this._hires.length] = new HireNeedVO(316,[new NeedVO(10000,7000)]);
         this._hires[this._hires.length] = new HireNeedVO(317,[new NeedVO(10000,8000)]);
         this._hires[this._hires.length] = new HireNeedVO(318,[new NeedVO(10000,9000)]);
         this._hires[this._hires.length] = new HireNeedVO(319,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(320,[new NeedVO(10000,25000)]);
         this._hires[this._hires.length] = new HireNeedVO(321,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(322,[new NeedVO(10000,25000)]);
         this._hires[this._hires.length] = new HireNeedVO(323,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(324,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(325,[new NeedVO(10000,25000)]);
         this._hires[this._hires.length] = new HireNeedVO(326,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(327,[new NeedVO(10000,8000)]);
         this._hires[this._hires.length] = new HireNeedVO(328,[new NeedVO(10000,9000)]);
         this._hires[this._hires.length] = new HireNeedVO(329,[new NeedVO(10000,7000)]);
         this._hires[this._hires.length] = new HireNeedVO(330,[new NeedVO(10000,666)]);
         this._hires[this._hires.length] = new HireNeedVO(331,[new NeedVO(10000,9000)]);
         this._hires[this._hires.length] = new HireNeedVO(332,[new NeedVO(10000,8000)]);
         this._hires[this._hires.length] = new HireNeedVO(333,[new NeedVO(10000,8000)]);
         this._hires[this._hires.length] = new HireNeedVO(334,[new NeedVO(10000,9000)]);
         this._hires[this._hires.length] = new HireNeedVO(335,[new NeedVO(10000,7000)]);
         this._hires[this._hires.length] = new HireNeedVO(336,[new NeedVO(10000,8000)]);
         this._hires[this._hires.length] = new HireNeedVO(337,[new NeedVO(10000,9000)]);
         this._hires[this._hires.length] = new HireNeedVO(338,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(340,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(341,[new NeedVO(10006,300)]);
         this._hires[this._hires.length] = new HireNeedVO(342,[new NeedVO(10000,8000)]);
         this._hires[this._hires.length] = new HireNeedVO(343,[new NeedVO(10000,9000)]);
         this._hires[this._hires.length] = new HireNeedVO(344,[new NeedVO(10000,8000)]);
         this._hires[this._hires.length] = new HireNeedVO(345,[new NeedVO(10000,7000)]);
         this._hires[this._hires.length] = new HireNeedVO(346,[new NeedVO(10000,7000)]);
         this._hires[this._hires.length] = new HireNeedVO(347,[new NeedVO(10000,9000)]);
         this._hires[this._hires.length] = new HireNeedVO(348,[new NeedVO(10000,8000)]);
         this._hires[this._hires.length] = new HireNeedVO(349,[new NeedVO(10000,8000)]);
         this._hires[this._hires.length] = new HireNeedVO(350,[new NeedVO(10000,9000)]);
         this._hires[this._hires.length] = new HireNeedVO(351,[new NeedVO(10006,300)]);
         this._hires[this._hires.length] = new HireNeedVO(352,[new NeedVO(10006,300)]);
         this._hires[this._hires.length] = new HireNeedVO(353,[new NeedVO(10006,300)]);
         this._hires[this._hires.length] = new HireNeedVO(354,[new NeedVO(10006,300)]);
         this._hires[this._hires.length] = new HireNeedVO(355,[new NeedVO(10006,300)]);
         this._hires[this._hires.length] = new HireNeedVO(356,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(357,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(358,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(359,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(360,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(361,[new NeedVO(10000,9000)]);
         this._hires[this._hires.length] = new HireNeedVO(362,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(363,[new NeedVO(10006,300)]);
         this._hires[this._hires.length] = new HireNeedVO(364,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(365,[new NeedVO(10000,9000)]);
         this._hires[this._hires.length] = new HireNeedVO(366,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(367,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(368,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(369,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(370,[new NeedVO(10000,9000)]);
         this._hires[this._hires.length] = new HireNeedVO(371,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(372,[new NeedVO(10006,300)]);
         this._hires[this._hires.length] = new HireNeedVO(373,[new NeedVO(10000,9000)]);
         this._hires[this._hires.length] = new HireNeedVO(374,[new NeedVO(10000,9000)]);
         this._hires[this._hires.length] = new HireNeedVO(375,[new NeedVO(10006,300)]);
         this._hires[this._hires.length] = new HireNeedVO(376,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(377,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(378,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(379,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(380,[new NeedVO(10006,300)]);
         this._hires[this._hires.length] = new HireNeedVO(381,[new NeedVO(10000,9000)]);
         this._hires[this._hires.length] = new HireNeedVO(382,[new NeedVO(10000,9000)]);
         this._hires[this._hires.length] = new HireNeedVO(383,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(384,[new NeedVO(10000,9000)]);
         this._hires[this._hires.length] = new HireNeedVO(385,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(386,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(387,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(388,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(389,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(390,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(391,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(392,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(393,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(394,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(395,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(396,[new NeedVO(10006,300)]);
         this._hires[this._hires.length] = new HireNeedVO(397,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(398,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(399,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(400,[new NeedVO(10006,300)]);
         this._hires[this._hires.length] = new HireNeedVO(401,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(402,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(403,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(404,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(405,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(406,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(407,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(408,[new NeedVO(10006,300)]);
         this._hires[this._hires.length] = new HireNeedVO(409,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(410,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(411,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(412,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(413,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(414,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(415,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(416,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(417,[new NeedVO(10006,300)]);
         this._hires[this._hires.length] = new HireNeedVO(418,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(419,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(420,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(421,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(422,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(423,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(424,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(425,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(426,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(427,[new NeedVO(10006,300)]);
         this._hires[this._hires.length] = new HireNeedVO(428,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(429,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(430,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(431,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(432,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(433,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(434,[new NeedVO(10006,300)]);
         this._hires[this._hires.length] = new HireNeedVO(435,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(436,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(437,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(438,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(439,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(440,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(441,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(442,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(443,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(444,[new NeedVO(10006,300)]);
         this._hires[this._hires.length] = new HireNeedVO(445,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(446,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(447,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(448,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(449,[new NeedVO(10006,300)]);
         this._hires[this._hires.length] = new HireNeedVO(450,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(451,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(452,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(453,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(454,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(455,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(456,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(457,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(458,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(460,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(461,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(462,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(463,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(464,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(465,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(466,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(467,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(468,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(469,[new NeedVO(10006,300)]);
         this._hires[this._hires.length] = new HireNeedVO(470,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(471,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(472,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(473,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(474,[new NeedVO(10006,300)]);
         this._hires[this._hires.length] = new HireNeedVO(475,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(476,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(477,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(478,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(479,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(480,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(481,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(482,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(483,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(484,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(485,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(486,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(487,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(488,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(489,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(490,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(491,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(492,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(493,[new NeedVO(10006,500)]);
         this._hires[this._hires.length] = new HireNeedVO(494,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(495,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(496,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(497,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(498,[new NeedVO(10006,500)]);
         this._hires[this._hires.length] = new HireNeedVO(499,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(500,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(501,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(502,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(503,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(504,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(505,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(506,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(507,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(508,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(509,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(510,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(511,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(512,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(513,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(514,[new NeedVO(10006,500)]);
         this._hires[this._hires.length] = new HireNeedVO(515,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(516,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(517,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(518,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(519,[new NeedVO(10006,500)]);
         this._hires[this._hires.length] = new HireNeedVO(520,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(521,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(522,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(523,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(524,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(525,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(526,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(527,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(528,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(529,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(530,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(531,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(532,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(533,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(534,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(535,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(536,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(537,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(538,[new NeedVO(10006,500)]);
         this._hires[this._hires.length] = new HireNeedVO(539,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(540,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(541,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(542,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(543,[new NeedVO(10006,500)]);
         this._hires[this._hires.length] = new HireNeedVO(544,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(545,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(546,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(547,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(548,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(549,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(550,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(551,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(552,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(553,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(554,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(555,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(556,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(557,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(558,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(559,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(560,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(561,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(562,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(563,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(564,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(565,[new NeedVO(10006,500)]);
         this._hires[this._hires.length] = new HireNeedVO(566,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(567,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(568,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(569,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(570,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(571,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(572,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(573,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(574,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(575,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(576,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(577,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(578,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(579,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(580,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(581,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(582,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(583,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(584,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(585,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(586,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(587,[new NeedVO(10006,500)]);
         this._hires[this._hires.length] = new HireNeedVO(588,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(589,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(590,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(591,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(592,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(593,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(594,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(595,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(596,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(597,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(598,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(599,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(600,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(601,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(602,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(603,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(604,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(605,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(606,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(607,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(608,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(609,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(610,[new NeedVO(10006,500)]);
         this._hires[this._hires.length] = new HireNeedVO(611,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(612,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(613,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(614,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(615,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(616,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(617,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(618,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(619,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(620,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(621,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(622,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(623,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(624,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(625,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(626,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(627,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(628,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(629,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(630,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(631,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(632,[new NeedVO(10006,500)]);
         this._hires[this._hires.length] = new HireNeedVO(633,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(634,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(635,[new NeedVO(10000,10000)]);
         this._hires[this._hires.length] = new HireNeedVO(636,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(637,[new NeedVO(10006,500)]);
         this._hires[this._hires.length] = new HireNeedVO(638,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(639,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(640,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(641,[new NeedVO(10000,20000)]);
         this._hires[this._hires.length] = new HireNeedVO(642,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(643,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(644,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(645,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(646,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(647,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(648,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(649,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(650,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(651,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(652,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(653,[new NeedVO(10007,800)]);
         this._hires[this._hires.length] = new HireNeedVO(654,[new NeedVO(10007,800)]);
      }
      
      public function findFreeHire(param1:int) : HireNeedVO
      {
         var _loc2_:HireNeedVO = null;
         for each(_loc2_ in this._hires)
         {
            if(_loc2_.heroID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

