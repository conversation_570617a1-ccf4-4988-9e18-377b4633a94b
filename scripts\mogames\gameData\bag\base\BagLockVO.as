package mogames.gameData.bag.base
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import com.mogames.utils.TxtUtil;
   
   public class BagLockVO
   {
      
      private var _bagID:Oint = new Oint();
      
      private var _tip:String;
      
      public function BagLockVO(param1:int, param2:String = "")
      {
         super();
         MathUtil.saveINT(this._bagID,param1);
         this._tip = param2;
      }
      
      public function get isOpen() : Boolean
      {
         return true;
      }
      
      public function get bagID() : int
      {
         return MathUtil.loadINT(this._bagID);
      }
      
      public function get tip() : String
      {
         return TxtUtil.setColor(this._tip,"ffff00");
      }
   }
}

