package mogames.gameData.mission.extra
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ExtraWave2044
   {
      
      public function ExtraWave2044()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2044);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(244,575000,3650,650,80,80,300,90,new Boss<PERSON>killData0(150,{"hurt":12000},5),1006,0);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(14,new RoleArgVO(247,9200,1090,110,20,50,190,80,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(247,9200,1090,110,20,50,190,80,0)));
         _loc2_.addFu(new BossArgVO(240,110000,3500,450,50,80,150,80,new BossSkillData1(12,{
            "hurt":2750,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(247,9200,1090,110,20,50,190,80,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(247,9200,1090,110,20,50,190,80,0)));
         _loc2_.addFu(new BossArgVO(240,110000,3500,450,50,80,150,80,new BossSkillData1(12,{
            "hurt":2650,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(247,9200,1090,110,20,50,190,80,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(247,9200,1090,110,20,50,190,80,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(247,9200,1090,110,20,50,190,80,0)));
         _loc2_.addFu(new BossArgVO(240,110000,3500,450,50,80,150,80,new BossSkillData0(150,{"hurt":3000},2),1010,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(247,9200,1090,110,20,50,190,80,0)));
         _loc2_.addFu(new BossArgVO(240,110000,3500,450,50,80,150,80,new BossSkillData0(150,{
            "hurt":2600,
            "keepTime":3
         },1),1014,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(247,9200,1090,110,20,50,190,80,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(247,9200,1090,110,20,50,190,80,0)));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

