package mogames.gameData.cheat
{
   import mogames.gameData.ServerProxy;
   import mogames.gameUI.prompt.ConfirmMessage;
   import mogames.gameUI.tip.TipUtil;
   
   public class PKGChecker
   {
      
      public function PKGChecker()
      {
         super();
      }
      
      public function check() : void
      {
         if(ServerProxy.instance().checkVerOld())
         {
            ConfirmMessage.instance().showMsg("版本过低，请升级到最新版本！",false);
            return;
         }
         if(!ServerProxy.instance().isSameIndex || !ServerProxy.instance().isSameUID)
         {
            ServerProxy.instance().checkCode = 103;
         }
         if(!ServerProxy.instance().checkCode)
         {
            return;
         }
         TipUtil.handlerLock();
      }
   }
}

