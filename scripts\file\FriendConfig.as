package file
{
   import mogames.gameData.role.base.FriendBuildVO;
   import mogames.gameData.role.base.FriendTalentVO;
   import mogames.gameData.role.base.RoleAttVO;
   
   public class FriendConfig
   {
      
      private static var _instance:FriendConfig;
      
      private var _atts:Vector.<RoleAttVO>;
      
      private var _builds:Vector.<FriendBuildVO>;
      
      private var _talents:Vector.<FriendTalentVO>;
      
      public function FriendConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : FriendConfig
      {
         if(!_instance)
         {
            _instance = new FriendConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._atts = new Vector.<RoleAttVO>();
         this._builds = new Vector.<FriendBuildVO>();
         this._talents = new Vector.<FriendTalentVO>();
         this._atts[this._atts.length] = new RoleAttVO(101,19,3,0,20,36,135,80,116,16,4.8);
         this._atts[this._atts.length] = new RoleAttVO(102,20,3,0,18,32,155,70,120,16.8,3.2);
         this._atts[this._atts.length] = new RoleAttVO(103,16,2,0,10,18,105,75,88,14,2.4);
         this._atts[this._atts.length] = new RoleAttVO(104,21,3,0,17,26,120,80,100,16,2.6);
         this._atts[this._atts.length] = new RoleAttVO(105,23,3,0,22,25,145,80,140,16,3.2);
         this._atts[this._atts.length] = new RoleAttVO(106,27,3,0,26,32,135,95,160,15.6,2.4);
         this._atts[this._atts.length] = new RoleAttVO(107,19,3,0,24,20,120,80,100,16.4,2.8);
         this._atts[this._atts.length] = new RoleAttVO(108,10,1,0,18,35,140,65,60,10.4,1.6);
         this._atts[this._atts.length] = new RoleAttVO(109,17,3,0,22,20,125,70,100,14,3.2);
         this._atts[this._atts.length] = new RoleAttVO(110,11,1,0,15,25,150,65,64,8.8,2.4);
         this._atts[this._atts.length] = new RoleAttVO(111,10,4,0,45,50,180,85,60,24,3.6);
         this._atts[this._atts.length] = new RoleAttVO(112,11,1,0,10,30,160,65,68,9.6,2);
         this._builds[this._builds.length] = new FriendBuildVO(101,1101,1.5,55,35);
         this._builds[this._builds.length] = new FriendBuildVO(102,1102,1.3,65,25);
         this._builds[this._builds.length] = new FriendBuildVO(103,1201,1.2,60,30);
         this._builds[this._builds.length] = new FriendBuildVO(104,1202,1.5,50,40);
         this._builds[this._builds.length] = new FriendBuildVO(105,1301,1.5,40,50);
         this._builds[this._builds.length] = new FriendBuildVO(106,1302,1.3,30,60);
         this._builds[this._builds.length] = new FriendBuildVO(107,2101,1.5,75,15);
         this._builds[this._builds.length] = new FriendBuildVO(108,2102,1.2,45,35);
         this._builds[this._builds.length] = new FriendBuildVO(109,2201,1.5,50,40);
         this._builds[this._builds.length] = new FriendBuildVO(110,2202,1.2,35,45);
         this._builds[this._builds.length] = new FriendBuildVO(111,2301,1.5,30,60);
         this._builds[this._builds.length] = new FriendBuildVO(112,2302,1.2,40,40);
         this._talents[this._talents.length] = new FriendTalentVO(101,1103,1104,1105);
         this._talents[this._talents.length] = new FriendTalentVO(102,1103,1104,1106);
         this._talents[this._talents.length] = new FriendTalentVO(103,1203,1204,1205);
         this._talents[this._talents.length] = new FriendTalentVO(104,1203,1204,1206);
         this._talents[this._talents.length] = new FriendTalentVO(105,1303,1304,1305);
         this._talents[this._talents.length] = new FriendTalentVO(106,1303,1304,1306);
         this._talents[this._talents.length] = new FriendTalentVO(107,2103,2104,2105);
         this._talents[this._talents.length] = new FriendTalentVO(108,2103,2104,2106);
         this._talents[this._talents.length] = new FriendTalentVO(109,2203,2204,2205);
         this._talents[this._talents.length] = new FriendTalentVO(110,2203,2204,2206);
         this._talents[this._talents.length] = new FriendTalentVO(111,2303,2304,2305);
         this._talents[this._talents.length] = new FriendTalentVO(112,2303,2304,2306);
      }
      
      public function findATT(param1:int) : RoleAttVO
      {
         var _loc2_:RoleAttVO = null;
         for each(_loc2_ in this._atts)
         {
            if(_loc2_.roleID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function findTalent(param1:int) : FriendTalentVO
      {
         var _loc2_:FriendTalentVO = null;
         for each(_loc2_ in this._talents)
         {
            if(_loc2_.roleID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function findBuild(param1:int) : FriendBuildVO
      {
         var _loc2_:FriendBuildVO = null;
         for each(_loc2_ in this._builds)
         {
            if(_loc2_.roleID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

