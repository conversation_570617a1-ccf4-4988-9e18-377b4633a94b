package file
{
   import com.mogames.utils.MathUtil;
   import mogames.ConstData;
   import mogames.gameData.base.WhereProxy;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.master.MasterProxy;
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class HaiGuConfig
   {
      
      private static var _instance:HaiGuConfig;
      
      private var _waveData:WaveDataVO;
      
      public function HaiGuConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : HaiGuConfig
      {
         if(!_instance)
         {
            _instance = new HaiGuConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         var _loc1_:OneWaveVO = null;
         this._waveData = new WaveDataVO(0);
         _loc1_ = new OneWaveVO(5);
         _loc1_.addFu(new BossArgVO(this.randID,this.countHP(2.2),this.countATK(3),10,30,50,150,160,new BossSkillData0(20,{
            "hurt":53,
            "keepTime":5,
            "hurtCount":3
         },2),1001,0));
         _loc1_.addFu(new BossArgVO(this.randID,this.countHP(2.4),this.countATK(3.2),10,30,50,150,160,new BossSkillData0(20,{
            "hurt":250,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addFu(new BossArgVO(this.randID,this.countHP(2.6),this.countATK(3.4),10,30,50,150,160,new BossSkillData0(20,{
            "hurt":250,
            "hurtCount":5
         },2),1003,0));
         _loc1_.addFu(new BossArgVO(this.randID,this.countHP(2.8),this.countATK(3.6),10,30,50,150,160,new BossSkillData0(20,{
            "hurt":260,
            "roleNum":4
         },2),1004,0));
         _loc1_.addFu(new BossArgVO(this.randID,this.countHP(2.1),this.countATK(3.8),10,30,50,150,160,new BossSkillData0(20,{
            "hurt":260,
            "roleNum":10
         },2),1005,0));
         this._waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(20);
         _loc1_.addFu(new BossArgVO(this.randID,this.countHP(3),this.countATK(4),10,30,50,150,160,new BossSkillData0(30,{"hurt":380},2),1006,0));
         _loc1_.addFu(new BossArgVO(this.randID,this.countHP(3.2),this.countATK(4.2),10,30,50,150,160,new BossSkillData0(30,{
            "hurt":300,
            "keepTime":3
         },2),1007,0));
         _loc1_.addFu(new BossArgVO(this.randID,this.countHP(3.4),this.countATK(4.4),10,30,50,150,160,new BossSkillData0(30,{"hurt":320},2),1008,0));
         _loc1_.addFu(new BossArgVO(this.randID,this.countHP(3.6),this.countATK(4.6),10,30,50,150,160,new BossSkillData0(30,{"hurt":340},2),1009,0));
         _loc1_.addFu(new BossArgVO(this.randID,this.countHP(3.8),this.countATK(4.8),10,30,50,150,160,new BossSkillData0(30,{"hurt":360},2),1010,0));
         this._waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(25);
         _loc1_.addFu(new BossArgVO(this.randID,this.countHP(4),this.countATK(5),20,30,50,150,160,new BossSkillData0(40,{"hurt":480},2),1011,0));
         _loc1_.addFu(new BossArgVO(this.randID,this.countHP(4.2),this.countATK(5.2),20,30,50,150,160,new BossSkillData0(40,{"hurt":400},2),1012,0));
         _loc1_.addFu(new BossArgVO(this.randID,this.countHP(4.4),this.countATK(5.4),20,30,50,150,160,new BossSkillData0(40,{"hurt":420},2),1013,0));
         _loc1_.addFu(new BossArgVO(this.randID,this.countHP(4.6),this.countATK(5.6),20,30,50,150,160,new BossSkillData0(40,{
            "hurt":255,
            "keepTime":3
         },2),1014,0));
         _loc1_.addFu(new BossArgVO(this.randID,this.countHP(4.8),this.countATK(5.8),20,30,50,150,160,new BossSkillData0(40,{
            "hurt":83,
            "keepTime":8,
            "hurtCount":2
         },2),1015,0));
         this._waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(30);
         _loc1_.addFu(new BossArgVO(this.randID,this.countHP(4),this.countATK(6),30,30,50,150,160,new BossSkillData0(50,{
            "hurt":560,
            "roleNum":3
         },2),1016,0));
         _loc1_.addFu(new BossArgVO(this.randID,this.countHP(4.2),this.countATK(6.2),30,30,50,150,160,new BossSkillData0(50,{
            "hurt":590,
            "hurtCount":3
         },2),1017,0));
         _loc1_.addFu(new BossArgVO(this.randID,this.countHP(4.4),this.countATK(6.4),30,30,50,150,160,new BossSkillData0(50,{"hurt":520},2),1018,0));
         _loc1_.addFu(new BossArgVO(this.randID,this.countHP(4.6),this.countATK(6.6),30,30,50,150,160,new BossSkillData0(50,{
            "hurt":550,
            "hurtCount":3
         },2),1019,0));
         _loc1_.addFu(new BossArgVO(this.randID,this.countHP(4.8),this.countATK(6.8),30,30,50,150,160,new BossSkillData0(50,{
            "hurt":580,
            "keepTime":3
         },2),1020,0));
         this._waveData.addWave(_loc1_);
      }
      
      public function get waveData() : WaveDataVO
      {
         return this._waveData;
      }
      
      private function get randID() : int
      {
         var _loc1_:Array = WhereProxy.instance().findWhere(0);
         if(_loc1_.length <= 0)
         {
            _loc1_ = [202,203,204,205,206,207,208,209,210,211,212];
         }
         return _loc1_[int(Math.random() * _loc1_.length)];
      }
      
      private function countATK(param1:Number) : int
      {
         return MasterProxy.instance().masterVO.level * param1 * 0.9;
      }
      
      private function countHP(param1:Number) : int
      {
         return MasterProxy.instance().masterVO.level * param1 * 120;
      }
      
      public function newReward(param1:Boolean) : Array
      {
         if(!param1)
         {
            return this.loseReward;
         }
         return this.winReward;
      }
      
      private function get winReward() : Array
      {
         if(WhereProxy.instance().checkDeadQuality(3))
         {
            return this.countWin0();
         }
         return this.countWin1();
      }
      
      private function countWin0() : Array
      {
         var _loc4_:int = 0;
         var _loc1_:Array = [];
         var _loc2_:int = ConstData.INT1.v;
         if(MathUtil.checkOdds(200))
         {
            _loc2_ = ConstData.INT2.v;
         }
         var _loc3_:Array = [10110,10111,10112];
         var _loc5_:int = 0;
         while(_loc5_ < _loc2_)
         {
            _loc4_ = Math.random() * _loc3_.length;
            _loc1_[_loc5_] = new BaseRewardVO(_loc3_[_loc4_],Math.random() * 3 + 1);
            _loc3_.splice(_loc4_,1);
            _loc5_++;
         }
         _loc1_[_loc1_.length] = new BaseRewardVO(10113,3);
         return _loc1_;
      }
      
      private function countWin1() : Array
      {
         var _loc2_:int = 0;
         var _loc5_:int = 0;
         var _loc1_:Array = [];
         var _loc3_:int = Math.random() * 100 + 1;
         if(_loc3_ <= 20)
         {
            _loc2_ = ConstData.INT1.v;
         }
         else if(_loc3_ <= 80)
         {
            _loc2_ = ConstData.INT2.v;
         }
         else
         {
            _loc2_ = ConstData.INT3.v;
         }
         var _loc4_:Array = [10110,10111,10112];
         var _loc6_:int = 0;
         while(_loc6_ < _loc2_)
         {
            _loc5_ = Math.random() * _loc4_.length;
            _loc1_[_loc6_] = new BaseRewardVO(_loc4_[_loc5_],Math.random() * 3 + 1);
            _loc4_.splice(_loc5_,1);
            _loc6_++;
         }
         return _loc1_;
      }
      
      private function get loseReward() : Array
      {
         return [new BaseRewardVO(10110,1)];
      }
   }
}

