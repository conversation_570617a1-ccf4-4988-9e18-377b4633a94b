package mogames.gameData.achieve.base
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import com.mogames.utils.TxtUtil;
   import mogames.ConstData;
   import mogames.gameEffect.EffectManager;
   
   public class BaseAchieveVO
   {
      
      protected var _id:Oint = new Oint();
      
      protected var _get:Oint = new Oint();
      
      protected var _fail:Oint = new Oint();
      
      protected var _name:String;
      
      protected var _infor:String;
      
      protected var _rewards:Array;
      
      public function BaseAchieveVO(param1:int, param2:Array, param3:String, param4:String)
      {
         super();
         MathUtil.saveINT(this._id,param1);
         MathUtil.saveINT(this._get,0);
         MathUtil.saveINT(this._fail,0);
         this._rewards = param2;
         this._name = param3;
         this._infor = param4;
      }
      
      public function checkOpen(param1:int = 1) : void
      {
         this.dispatchTip();
      }
      
      public function reset() : void
      {
      }
      
      public function setGet() : void
      {
         MathUtil.saveINT(this._get,1);
      }
      
      public function setFail() : void
      {
         MathUtil.saveINT(this._fail,1);
      }
      
      public function get isOpen() : Boolean
      {
         return false;
      }
      
      public function get isGet() : Boolean
      {
         return Boolean(MathUtil.loadINT(this._get));
      }
      
      public function get isHide() : Boolean
      {
         return this.label == ConstData.INT4.v;
      }
      
      public function get isFail() : Boolean
      {
         return Boolean(MathUtil.loadINT(this._fail));
      }
      
      public function get id() : int
      {
         return MathUtil.loadINT(this._id);
      }
      
      public function get label() : int
      {
         return int(String(this.id).charAt(0));
      }
      
      public function get name() : String
      {
         return this._name;
      }
      
      public function get infor() : String
      {
         return this._infor;
      }
      
      public function get rewards() : Array
      {
         return this._rewards;
      }
      
      protected function dispatchTip() : void
      {
         if(!this.isOpen)
         {
            return;
         }
         if(this.isHide)
         {
            EffectManager.addTipText("解锁隐藏成就：" + TxtUtil.setColor("【" + this.name + "】","99FF00"),"AUDIO_ACHIEVE");
         }
         else
         {
            EffectManager.addTipText("解锁成就：" + TxtUtil.setColor("【" + this.name + "】","99FF00"),"AUDIO_ACHIEVE");
         }
      }
      
      public function get sortIndex() : int
      {
         if(this.isGet)
         {
            return 0;
         }
         if(this.isOpen && !this.isGet)
         {
            return 2;
         }
         return 1;
      }
      
      public function get saveData() : String
      {
         return "";
      }
      
      public function set loadData(param1:Array) : void
      {
      }
   }
}

