package mogames.gameData.base
{
   import com.mogames.utils.TxtUtil;
   import mogames.gameData.base.vo.NeedVO;
   import mogames.gameData.base.vo.ZhiBaoNeedVO;
   import mogames.gameData.depot.DepotProxy;
   import mogames.gameData.game.LackVO;
   import mogames.gameData.master.MasterProxy;
   
   public class UseProxy
   {
      
      private static var _instance:UseProxy;
      
      public function UseProxy()
      {
         super();
      }
      
      public static function instance() : UseProxy
      {
         if(!_instance)
         {
            _instance = new UseProxy();
         }
         return _instance;
      }
      
      public function useStuff(param1:Array, param2:Function) : void
      {
         var _loc3_:NeedVO = null;
         for each(_loc3_ in param1)
         {
            if(!_loc3_.isCachet)
            {
               if(_loc3_.isVirtual)
               {
                  MasterProxy.instance().changeValue(_loc3_.needID,-_loc3_.needNum);
               }
               else if(_loc3_ is ZhiBaoNeedVO)
               {
                  DepotProxy.instance().delItemByID(_loc3_.needID);
               }
               else
               {
                  DepotProxy.instance().useItems(_loc3_.needID,_loc3_.needNum);
               }
            }
         }
         if(param2 != null)
         {
            param2();
         }
      }
      
      public function useWithCachet(param1:Array) : void
      {
         var _loc2_:NeedVO = null;
         for each(_loc2_ in param1)
         {
            if(_loc2_.isVirtual)
            {
               MasterProxy.instance().changeValue(_loc2_.needID,-_loc2_.needNum);
            }
            else if(_loc2_ is ZhiBaoNeedVO)
            {
               DepotProxy.instance().delItemByID(_loc2_.needID);
            }
            else
            {
               DepotProxy.instance().useItems(_loc2_.needID,_loc2_.needNum);
            }
         }
      }
      
      public function checkLack(param1:Array) : LackVO
      {
         var _loc2_:LackVO = null;
         var _loc3_:NeedVO = null;
         for each(_loc3_ in param1)
         {
            _loc2_ = _loc3_.lackVO;
            if(_loc2_ != null)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function nameList(param1:Array) : Array
      {
         var _loc3_:NeedVO = null;
         var _loc2_:Array = [];
         for each(_loc3_ in param1)
         {
            _loc2_[_loc2_.length] = _loc3_.needName + "X" + _loc3_.needNum;
         }
         return _loc2_;
      }
      
      public function contactName0(param1:Array) : String
      {
         var _loc3_:NeedVO = null;
         var _loc2_:Array = [];
         for each(_loc3_ in param1)
         {
            if(_loc3_.lackVO != null)
            {
               _loc2_.push(TxtUtil.setColor(_loc3_.needName + "X" + _loc3_.needNum,"ff0000"));
            }
            else
            {
               _loc2_.push(TxtUtil.setColor(_loc3_.needName + "X" + _loc3_.needNum,"99ff00"));
            }
         }
         return _loc2_.join("，");
      }
      
      public function contactName1(param1:Array) : String
      {
         var _loc3_:NeedVO = null;
         var _loc2_:Array = [];
         for each(_loc3_ in param1)
         {
            _loc2_[_loc2_.length] = _loc3_.needName + "X" + _loc3_.needNum;
         }
         return _loc2_.join("，");
      }
      
      public function countactName2(param1:Array, param2:String) : String
      {
         var _loc4_:NeedVO = null;
         var _loc3_:Array = [];
         for each(_loc4_ in param1)
         {
            _loc3_[_loc3_.length] = _loc4_.needName + param2 + _loc4_.needNum;
         }
         return _loc3_.join("，");
      }
   }
}

