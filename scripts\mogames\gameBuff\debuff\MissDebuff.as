package mogames.gameBuff.debuff
{
   import mogames.gameBuff.base.TimeRoleBuff;
   import mogames.gameData.buff.ConstBuffVO;
   
   public class MissDebuff extends TimeRoleBuff
   {
      
      private var _value:int;
      
      public function MissDebuff(param1:ConstBuffVO)
      {
         super(param1);
      }
      
      override protected function createData() : void
      {
         this._value = _buffVO.args.isPer ? int(_owner.roleVO.totalMISS * _buffVO.args.value * 0.01) : int(_buffVO.args.value);
         _owner.roleVO.skillMISS -= this._value;
         _owner.roleVO.updateMISS();
      }
      
      override protected function onCleanRole() : void
      {
         _owner.roleVO.skillMISS += this._value;
         _owner.roleVO.updateMISS();
      }
   }
}

