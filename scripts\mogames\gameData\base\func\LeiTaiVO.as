package mogames.gameData.base.func
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import file.FubenConfig;
   import mogames.gameData.role.battle.RoleArgVO;
   
   public class LeiTaiVO
   {
      
      private var _argVO:RoleArgVO;
      
      private var _need:Oint = new Oint();
      
      private var _drops:Array;
      
      public function LeiTaiVO(param1:RoleArgVO, param2:int, param3:Array)
      {
         super();
         this._argVO = param1;
         MathUtil.saveINT(this._need,param2);
         this._drops = param3;
      }
      
      public function get argVO() : RoleArgVO
      {
         return this._argVO;
      }
      
      public function get needCachet() : int
      {
         return MathUtil.loadINT(this._need);
      }
      
      public function get drops() : Array
      {
         return this._drops;
      }
      
      public function get uiDrops() : Array
      {
         return FubenConfig.instance().showDrops(this._drops);
      }
   }
}

