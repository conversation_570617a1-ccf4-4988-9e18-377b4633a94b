package mogames.gameData.fuben.union
{
   import com.mogames.utils.TxtUtil;
   import file.UnionConfig;
   import mogames.ConstData;
   import mogames.gameData.base.vo.BaseDropVO;
   import mogames.gameData.base.vo.BaseRewardVO;
   
   public class UnionFBInfor
   {
      
      public var rankID:int;
      
      public var hpID:int;
      
      public var flagID:int;
      
      public var totalHP:Number;
      
      public var country:Array;
      
      private var _rewards:Array;
      
      public var fubenVO:FubenUnionVO;
      
      public function UnionFBInfor(param1:int, param2:int, param3:int, param4:int, param5:int, param6:Array, param7:Array)
      {
         super();
         this.rankID = param1;
         this.hpID = param2;
         this.flagID = param4;
         this.totalHP = param5;
         this.country = param6;
         this._rewards = param7;
         this.fubenVO = UnionConfig.instance().findFuben(param3);
      }
      
      public function get isNeedConfigHero() : Boolean
      {
         return this.fubenVO.fubenID != 307;
      }
      
      public function get countryLimit() : String
      {
         if(this.country.length >= 4)
         {
            return "无出战限制";
         }
         var _loc1_:Array = [];
         var _loc2_:int = 0;
         var _loc3_:int = int(this.country.length);
         while(_loc2_ < _loc3_)
         {
            _loc1_[_loc2_] = ConstData.COUNTRY1[this.country[_loc2_]];
            _loc2_++;
         }
         return "只能出战" + TxtUtil.setColor("（" + _loc1_.join("|") + "）","FF0000") + "阵营的武将";
      }
      
      public function get winRewards() : Array
      {
         var _loc2_:Object = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._rewards)
         {
            if(_loc2_ is BaseRewardVO)
            {
               _loc1_[_loc1_.length] = _loc2_;
            }
            else if(_loc2_ is BaseDropVO && (_loc2_ as BaseDropVO).isDrop)
            {
               _loc1_[_loc1_.length] = (_loc2_ as BaseDropVO).rewardVO;
            }
         }
         return _loc1_;
      }
      
      public function get allRewards() : Array
      {
         var _loc2_:Object = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._rewards)
         {
            if(_loc2_ is BaseRewardVO)
            {
               _loc1_[_loc1_.length] = _loc2_;
            }
            else if(_loc2_ is BaseDropVO)
            {
               _loc1_[_loc1_.length] = (_loc2_ as BaseDropVO).rewardVO;
            }
         }
         return _loc1_;
      }
   }
}

