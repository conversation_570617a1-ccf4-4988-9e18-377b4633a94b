package mogames.gameData.cell
{
   import com.mogames.data.Oint;
   import com.mogames.data.RandomVO;
   import com.mogames.utils.MathUtil;
   import com.mogames.utils.TxtUtil;
   import file.CellConfig;
   import file.HeroConfig;
   import mogames.ConstData;
   import mogames.gameData.base.WhereProxy;
   import mogames.gameData.role.hero.HeroGameVO;
   import mogames.gameData.role.hero.HeroOtherVO;
   import mogames.gameData.vip.VipProxy;
   
   public class CellGameVO
   {
      
      private var _level:Oint = new Oint();
      
      private var _heroID:Oint = new Oint();
      
      private var _hunger:Oint = new Oint();
      
      private var _health:Oint = new Oint();
      
      private var _count:Oint = new Oint();
      
      private var _total:Oint = new Oint();
      
      private var _maxLv:Oint = new Oint();
      
      private var _constVO:CellConstVO;
      
      private var _heroVO:HeroGameVO;
      
      private var _border:RandomVO = new RandomVO(0,1000);
      
      public function CellGameVO(param1:int)
      {
         super();
         MathUtil.saveINT(this._level,0);
         MathUtil.saveINT(this._maxLv,3);
         this._constVO = CellConfig.instance().findConst(param1);
         this.removePrisoner();
      }
      
      public function setLevel(param1:int) : void
      {
         MathUtil.saveINT(this._level,param1);
      }
      
      public function changeHealth(param1:int) : void
      {
         var _loc2_:int = this.health + param1;
         if(_loc2_ >= this._border.max)
         {
            _loc2_ = this._border.max;
         }
         else if(_loc2_ <= this._border.min)
         {
            _loc2_ = this._border.min;
         }
         MathUtil.saveINT(this._health,_loc2_);
      }
      
      public function changeHunger(param1:int) : void
      {
         var _loc2_:int = this.hunger + param1;
         if(_loc2_ >= this._border.max)
         {
            _loc2_ = this._border.max;
         }
         else if(_loc2_ <= this._border.min)
         {
            _loc2_ = this._border.min;
         }
         MathUtil.saveINT(this._hunger,_loc2_);
      }
      
      public function addPrisoner(param1:int) : void
      {
         MathUtil.saveINT(this._heroID,param1);
         this._heroVO = WhereProxy.instance().findHero(param1);
         var _loc2_:HeroOtherVO = HeroConfig.instance().findOther(param1);
         MathUtil.saveINT(this._health,_loc2_.health);
         MathUtil.saveINT(this._hunger,_loc2_.hunger);
         MathUtil.saveINT(this._count,0);
         MathUtil.saveINT(this._total,CellConfig.instance().randomCount(this._heroVO.heroInfo.quality));
         if(VipProxy.instance().hasFunc(110) && this._constVO.isVIP)
         {
            MathUtil.saveINT(this._total,this.total * 0.65);
         }
      }
      
      public function removePrisoner() : void
      {
         MathUtil.saveINT(this._heroID,0);
         MathUtil.saveINT(this._health,0);
         MathUtil.saveINT(this._hunger,0);
         MathUtil.saveINT(this._count,0);
         MathUtil.saveINT(this._total,100);
         this._heroVO = null;
      }
      
      public function addCount() : void
      {
         var _loc1_:int = this.count + ConstData.INT1.v;
         if(_loc1_ >= this.total)
         {
            _loc1_ = this.total;
         }
         MathUtil.saveINT(this._count,_loc1_);
      }
      
      public function get level() : int
      {
         return MathUtil.loadINT(this._level);
      }
      
      public function set prisoner(param1:int) : void
      {
         MathUtil.saveINT(this._heroID,param1);
      }
      
      public function get prisoner() : int
      {
         return MathUtil.loadINT(this._heroID);
      }
      
      public function get hasPrisoner() : Boolean
      {
         return MathUtil.loadINT(this._heroID) != 0;
      }
      
      public function get isDead() : Boolean
      {
         return this.health <= 0 || this.hunger >= 1000;
      }
      
      public function get heroVO() : HeroGameVO
      {
         return this._heroVO;
      }
      
      public function get health() : int
      {
         return MathUtil.loadINT(this._health);
      }
      
      public function get hunger() : int
      {
         return MathUtil.loadINT(this._hunger);
      }
      
      public function get learnNeeds() : Array
      {
         return this._constVO.findNeeds(this.level);
      }
      
      public function get isMaxLevel() : Boolean
      {
         return this.level >= MathUtil.loadINT(this._maxLv);
      }
      
      public function get isOpen() : Boolean
      {
         return this._constVO.isOpen;
      }
      
      public function get index() : int
      {
         return this._constVO.index;
      }
      
      public function get bgFrame() : int
      {
         return this._constVO.bgFrame;
      }
      
      public function get count() : int
      {
         return MathUtil.loadINT(this._count);
      }
      
      public function get total() : int
      {
         return MathUtil.loadINT(this._total);
      }
      
      public function get canHire() : Boolean
      {
         return this.count >= this.total;
      }
      
      public function get openInfor() : String
      {
         return this._constVO.openInfor;
      }
      
      public function get name() : String
      {
         return TxtUtil.convertChinaNum(this.index + 1) + "号牢房";
      }
      
      public function get prisonerInfor() : String
      {
         return "俘虏名：" + this.heroVO.heroInfo.name + "<br>" + "健康值：" + TxtUtil.setColor(this.health,"FFFF00") + "<br>" + "饥饿值：" + TxtUtil.setColor(this.hunger,"FFFF00");
      }
      
      public function get saveData() : String
      {
         var _loc1_:Array = [this.index,this.level,this.prisoner,this.health,this.hunger,this.count,this.total];
         return _loc1_.join("H");
      }
      
      public function set loadData(param1:Array) : void
      {
         MathUtil.saveINT(this._level,int(param1[1]));
         MathUtil.saveINT(this._heroID,int(param1[2]));
         MathUtil.saveINT(this._health,int(param1[3]));
         MathUtil.saveINT(this._hunger,int(param1[4]));
         MathUtil.saveINT(this._count,int(param1[5]));
         MathUtil.saveINT(this._total,int(param1[6]));
         if(this.hasPrisoner)
         {
            this._heroVO = WhereProxy.instance().findHero(int(param1[2]));
         }
      }
   }
}

