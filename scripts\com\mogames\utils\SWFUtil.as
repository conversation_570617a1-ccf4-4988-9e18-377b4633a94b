package com.mogames.utils
{
   import flash.display.LoaderInfo;
   import flash.utils.ByteArray;
   import flash.utils.Endian;
   
   public class SWFUtil
   {
      
      private static var bytes:ByteArray;
      
      private static var className:Array;
      
      private static var tagNum:int;
      
      public function SWFUtil()
      {
         super();
      }
      
      public static function parseClass(param1:LoaderInfo, param2:String) : Class
      {
         return param1.applicationDomain.getDefinition(param2) as Class;
      }
      
      public static function getSWFClassName(param1:ByteArray) : Array
      {
         var _loc2_:String = null;
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc6_:ByteArray = null;
         tagNum = 0;
         className = [];
         bytes = new ByteArray();
         bytes.writeBytes(param1);
         bytes.position = 0;
         bytes.endian = Endian.LITTLE_ENDIAN;
         _loc2_ = bytes.readUTFBytes(3);
         if(_loc2_ != "FWS" && _loc2_ != "CWS")
         {
            throw new Error("不能识别的SWF文件格式");
         }
         bytes.readByte();
         bytes.readUnsignedInt();
         bytes.readBytes(bytes);
         bytes.length -= 8;
         if(_loc2_ == "CWS")
         {
            bytes.uncompress();
         }
         bytes.position = 13;
         while(bytes.bytesAvailable)
         {
            readSWFTag(bytes);
         }
         return className.splice(0,className.length);
      }
      
      private static function readSWFTag(param1:ByteArray) : void
      {
         var _loc5_:ByteArray = null;
         var _loc2_:int = int(param1.readUnsignedShort());
         var _loc3_:* = _loc2_ >> 6;
         var _loc4_:* = _loc2_ & 0x3F;
         if((_loc4_ & 0x3F) == 63)
         {
            _loc4_ = int(param1.readUnsignedInt());
         }
         if(_loc3_ == 76)
         {
            _loc5_ = new ByteArray();
            if(_loc4_ != 0)
            {
               param1.readBytes(_loc5_,0,_loc4_);
            }
            getClass(_loc5_);
         }
         else
         {
            param1.position += _loc4_;
         }
      }
      
      private static function getClass(param1:ByteArray) : void
      {
         var _loc2_:String = null;
         var _loc3_:int = readUI16(param1);
         var _loc4_:int = 0;
         while(_loc4_ < _loc3_)
         {
            readUI16(param1);
            _loc2_ = readSwfString(param1);
            className.push(_loc2_);
            _loc4_++;
            ++tagNum;
            if(tagNum > 400)
            {
               return;
            }
         }
      }
      
      private static function readSwfString(param1:ByteArray) : String
      {
         var _loc2_:ByteArray = null;
         var _loc5_:String = null;
         var _loc3_:int = 1;
         var _loc4_:int = 0;
         while(true)
         {
            _loc4_ = param1.readByte();
            if(_loc4_ == 0)
            {
               break;
            }
            _loc3_++;
         }
         _loc2_ = new ByteArray();
         _loc2_.writeBytes(param1,param1.position - _loc3_,_loc3_);
         _loc2_.position = 0;
         return _loc2_.readUTFBytes(_loc3_);
      }
      
      private static function readUI16(param1:ByteArray) : int
      {
         var _loc2_:* = param1.readUnsignedByte();
         var _loc3_:* = param1.readUnsignedByte();
         return _loc2_ + (_loc3_ << 8);
      }
   }
}

