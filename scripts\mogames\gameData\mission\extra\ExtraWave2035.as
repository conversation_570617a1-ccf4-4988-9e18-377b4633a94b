package mogames.gameData.mission.extra
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ExtraWave2035
   {
      
      public function ExtraWave2035()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2035);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(243,525000,3520,600,80,80,300,90,new BossSkillData0(150,{
            "hurt":3500,
            "keepTime":8,
            "hurtCount":6
         },3),1015,0);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(248,8500,1020,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(250,8500,1020,120,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(248,8500,1020,120,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(248,8500,1020,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(250,8500,1020,120,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(248,8500,1020,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(250,8500,1020,120,50,80,200,120,0)));
         _loc2_.addFu(new BossArgVO(241,105000,3400,400,50,80,150,80,new BossSkillData0(150,{
            "hurt":2000,
            "roleNum":5
         },2),1016,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(248,8500,1020,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(250,8500,1020,120,50,80,200,120,0)));
         _loc2_.addFu(new BossArgVO(241,105000,3400,400,50,80,150,80,new BossSkillData1(10,{
            "hurt":2000,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(248,8500,1020,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(250,8500,1020,120,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(248,8500,1020,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(250,8500,1020,120,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(250,8500,1020,120,50,80,200,120,0)));
         _loc2_.addFu(new BossArgVO(241,105000,3400,400,50,80,150,80,new BossSkillData1(10,{"hurt":2000},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(248,8500,1020,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(250,8500,1020,120,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(248,8500,1020,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(250,8500,1020,120,50,80,200,120,0)));
         _loc2_.addFu(new BossArgVO(241,105000,3400,400,50,80,150,80,new BossSkillData0(150,{
            "hurt":2000,
            "roleNum":5
         },2),1016,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

