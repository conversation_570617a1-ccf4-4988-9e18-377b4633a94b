package mogames.gameData.mission.extra
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ExtraWave2073
   {
      
      public function ExtraWave2073()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2073);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(245,700000,4000,650,80,80,300,90,new BossSkillData0(150,{
            "hurt":4300,
            "roleNum":5
         },5),1016,0);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(247,12100,1380,130,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(249,12100,1380,130,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(250,12100,1380,130,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(249,12100,1380,130,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(250,12100,1380,130,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(249,12100,1380,130,50,80,200,120,0)));
         _loc2_.addFu(new BossArgVO(240,140000,3800,450,50,80,150,80,new BossSkillData1(10,{
            "hurt":4000,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(249,12100,1380,130,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(249,12100,1380,130,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(248,12100,1380,130,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(249,12100,1380,130,50,80,200,120,0)));
         _loc2_.addFu(new BossArgVO(240,140000,3800,450,50,80,150,80,new BossSkillData0(150,{"hurt":4000},3),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(248,12100,1380,130,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(249,12100,1380,130,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(249,12100,1380,130,50,80,200,120,0)));
         _loc2_.addFu(new BossArgVO(240,140000,3800,450,50,80,150,80,new BossSkillData1(10,{
            "hurt":4000,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(247,12100,1380,130,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(249,12100,1380,130,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(250,12100,1380,130,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(249,12100,1380,130,50,80,200,120,0)));
         _loc2_.addFu(new BossArgVO(240,140000,3800,450,50,80,150,80,new BossSkillData0(150,{
            "hurt":4000,
            "keepTime":3
         },2),1014,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(250,12100,1380,130,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(249,12100,1380,130,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

