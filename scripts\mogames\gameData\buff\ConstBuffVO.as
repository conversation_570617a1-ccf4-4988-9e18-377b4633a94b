package mogames.gameData.buff
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   
   public class ConstBuffVO
   {
      
      private var _buffID:Oint = new Oint();
      
      private var _over:Boolean;
      
      private var _skin:Object;
      
      public function ConstBuffVO(param1:int, param2:<PERSON><PERSON>an, param3:Object = null)
      {
         super();
         MathUtil.saveINT(this._buffID,param1);
         this._over = param2;
         this._skin = param3;
      }
      
      public function get buffID() : int
      {
         return MathUtil.loadINT(this._buffID);
      }
      
      public function get skin() : Object
      {
         return this._skin;
      }
      
      public function get isOver() : Boolean
      {
         return this._over;
      }
   }
}

