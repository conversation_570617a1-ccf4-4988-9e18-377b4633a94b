package mogames.gameBullet.base
{
   import com.mogames.display.SysBMC;
   import com.mogames.event.GameSignal;
   import com.mogames.event.SignalManager;
   import flash.geom.Point;
   import mogames.Layers;
   import mogames.gameAsset.AssetManager;
   import mogames.gameData.bullet.BulletConstVO;
   import mogames.gameData.role.battle.HurtData;
   import mogames.gameRole.base.IRole;
   
   public class BaseBullet implements IBullet
   {
      
      protected var _bmc:SysBMC;
      
      protected var _owner:IRole;
      
      protected var _target:IRole;
      
      protected var _hurtData:HurtData;
      
      protected var _constVO:BulletConstVO;
      
      protected var _speed:Number;
      
      public function BaseBullet()
      {
         super();
         this._bmc = new SysBMC();
         Layers.frontLayer.addChild(this._bmc);
      }
      
      public function createBullet(param1:BulletConstVO, param2:HurtData, param3:IRole) : void
      {
         this._constVO = param1;
         this._hurtData = param2;
         this._target = param3;
         this._speed = this._constVO.speed * 0.33;
         this._owner = this._hurtData.source;
         this._bmc.visible = true;
         this._bmc.setSequences(AssetManager.findAniRes(this._constVO.bodySkin));
         this._target.signal.add(this.listenDead);
      }
      
      public function start(param1:int, param2:int) : void
      {
         this._bmc.setLocation(param1,param2);
         Layers.render.add(this.updateLocation);
      }
      
      private function listenDead(param1:Object) : void
      {
         if(param1.type != "ROLE_DEAD")
         {
            return;
         }
         this._target = null;
      }
      
      protected function updateLocation() : void
      {
         if(this.canHurt)
         {
            this.handlerEnd();
         }
      }
      
      protected function handlerEnd() : void
      {
         this.dispatchATK();
         Layers.render.remove(this.updateLocation);
         this.dispatchRecyle();
      }
      
      protected function dispatchRecyle() : void
      {
         SignalManager.signalBullet.dispatchEvent({
            "signal":GameSignal.BULLET_DEL,
            "bullet":this
         });
         SignalManager.signalRecyle.dispatchEvent({
            "signal":GameSignal.RECYLE_BULLET,
            "bullet":this
         });
      }
      
      protected function dispatchATK() : void
      {
         if(!this.targetEnabled)
         {
            return;
         }
         this._target.setHurt(this._hurtData,this._constVO.hurtSkin,this._constVO.hurtSound);
      }
      
      protected function get canHurt() : Boolean
      {
         if(!this.targetEnabled)
         {
            return false;
         }
         return Point.distance(this._target.location,this._bmc.location) <= (this._target.width + this._constVO.width) * 0.5;
      }
      
      protected function get targetEnabled() : Boolean
      {
         return Boolean(this._target) && !this._target.isDead;
      }
      
      public function recyle() : void
      {
         Layers.render.remove(this.updateLocation);
         if(this._bmc)
         {
            this._bmc.clean();
            this._bmc.setLocation(-999,-999);
            this._bmc.visible = false;
            this._bmc.rotation = 0;
         }
         this._owner = null;
         this._target = null;
         this._hurtData = null;
         this._constVO = null;
      }
      
      public function destroy() : void
      {
         Layers.render.remove(this.updateLocation);
         if(this._bmc)
         {
            this._bmc.destroy();
         }
         this._bmc = null;
         this._owner = null;
         this._target = null;
         this._hurtData = null;
         this._constVO = null;
      }
   }
}

