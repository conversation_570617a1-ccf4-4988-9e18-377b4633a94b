package mogames.gameData.good.base
{
   public class IconVO
   {
      
      protected var _name:String;
      
      protected var _iname:String;
      
      protected var _infor:String;
      
      public function IconVO(param1:String, param2:String, param3:String)
      {
         super();
         this._name = param1;
         this._iname = param2;
         this._infor = param3;
      }
      
      public function get name() : String
      {
         return this._name;
      }
      
      public function get iname() : String
      {
         return this._iname;
      }
      
      public function get infor() : String
      {
         return this._infor;
      }
   }
}

