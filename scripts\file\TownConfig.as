package file
{
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.town.vo.TownConstVO;
   
   public class TownConfig
   {
      
      private static var _instance:TownConfig;
      
      private var _towns:Vector.<TownConstVO>;
      
      public function TownConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : TownConfig
      {
         if(!_instance)
         {
            _instance = new TownConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._towns = new Vector.<TownConstVO>();
         this._towns.push(new TownConstVO(1004,5,new BaseRewardVO(10003,4)));
         this._towns.push(new TownConstVO(1008,5,new BaseRewardVO(10004,4)));
         this._towns.push(new TownConstVO(1012,5,new BaseRewardVO(10005,4)));
         this._towns.push(new TownConstVO(1019,5,new BaseRewardVO(10003,5)));
         this._towns.push(new TownConstVO(1022,10,new BaseRewardVO(10004,5)));
         this._towns.push(new TownConstVO(1027,10,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(1031,10,new BaseRewardVO(10005,5)));
         this._towns.push(new TownConstVO(1034,10,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(1037,10,new BaseRewardVO(10006,4)));
         this._towns.push(new TownConstVO(1041,0,new BaseRewardVO(10003,2)));
         this._towns.push(new TownConstVO(1045,0,new BaseRewardVO(10004,3)));
         this._towns.push(new TownConstVO(1049,0,new BaseRewardVO(10005,2)));
         this._towns.push(new TownConstVO(1053,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(1057,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(1062,0,new BaseRewardVO(10003,2)));
         this._towns.push(new TownConstVO(1067,0,new BaseRewardVO(10004,3)));
         this._towns.push(new TownConstVO(1072,0,new BaseRewardVO(10005,2)));
         this._towns.push(new TownConstVO(1077,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(1082,0,new BaseRewardVO(10004,2)));
         this._towns.push(new TownConstVO(1087,0,new BaseRewardVO(10005,3)));
         this._towns.push(new TownConstVO(1092,0,new BaseRewardVO(10006,2)));
         this._towns.push(new TownConstVO(1097,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(1102,0,new BaseRewardVO(10004,2)));
         this._towns.push(new TownConstVO(1107,0,new BaseRewardVO(10005,3)));
         this._towns.push(new TownConstVO(1112,0,new BaseRewardVO(10006,2)));
         this._towns.push(new TownConstVO(1117,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(1122,0,new BaseRewardVO(10004,2)));
         this._towns.push(new TownConstVO(1127,0,new BaseRewardVO(10005,3)));
         this._towns.push(new TownConstVO(1132,0,new BaseRewardVO(10006,2)));
         this._towns.push(new TownConstVO(1137,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(1142,0,new BaseRewardVO(10004,2)));
         this._towns.push(new TownConstVO(1147,0,new BaseRewardVO(10005,3)));
         this._towns.push(new TownConstVO(1152,0,new BaseRewardVO(10006,2)));
         this._towns.push(new TownConstVO(1157,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(1162,0,new BaseRewardVO(10004,2)));
         this._towns.push(new TownConstVO(1167,0,new BaseRewardVO(10005,3)));
         this._towns.push(new TownConstVO(1172,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(1177,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(1182,0,new BaseRewardVO(10004,2)));
         this._towns.push(new TownConstVO(1187,0,new BaseRewardVO(10005,3)));
         this._towns.push(new TownConstVO(1192,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(1197,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(1202,0,new BaseRewardVO(10004,2)));
         this._towns.push(new TownConstVO(1207,0,new BaseRewardVO(10005,3)));
         this._towns.push(new TownConstVO(1212,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(1217,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(1222,0,new BaseRewardVO(10004,2)));
         this._towns.push(new TownConstVO(1227,0,new BaseRewardVO(10005,3)));
         this._towns.push(new TownConstVO(1232,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(1237,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(1242,0,new BaseRewardVO(10004,2)));
         this._towns.push(new TownConstVO(1247,0,new BaseRewardVO(10005,3)));
         this._towns.push(new TownConstVO(1252,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(1257,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2006,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2012,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2019,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2025,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2031,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2037,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2043,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2049,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2056,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2062,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2068,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2074,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2080,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2086,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2093,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2099,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2105,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2111,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2117,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2123,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2130,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2136,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2142,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2148,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2154,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2160,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2167,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2173,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2179,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2185,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2191,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2197,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2204,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2210,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2216,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2222,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2227,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2232,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2237,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2242,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2247,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2252,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2257,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2262,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2267,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2272,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2277,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2282,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2287,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2292,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2297,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2302,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2307,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2312,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2317,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2322,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2327,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2332,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2337,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2342,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2347,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2352,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2357,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2362,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2367,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2372,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2377,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2382,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2387,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2392,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2397,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2402,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2407,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2412,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2417,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2422,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2427,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2432,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2437,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2442,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2447,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2451,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2455,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2459,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2463,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2467,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2471,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2475,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2479,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2483,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2487,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2491,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2495,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2499,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2503,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2507,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2511,0,new BaseRewardVO(10006,3)));
         this._towns.push(new TownConstVO(2515,0,new BaseRewardVO(10007,3)));
         this._towns.push(new TownConstVO(2519,0,new BaseRewardVO(10006,3)));
      }
      
      public function findTown(param1:int) : TownConstVO
      {
         var _loc2_:TownConstVO = null;
         for each(_loc2_ in this._towns)
         {
            if(_loc2_.id == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

