package com.mogames.comt
{
   import com.mogames.sound.SoundManager;
   import com.mogames.utils.FontUtil;
   import flash.events.TimerEvent;
   import flash.text.TextField;
   import flash.utils.Timer;
   
   public class Typer
   {
      
      private var _timer:Timer;
      
      private var _strHTML:String;
      
      private var _strTXT:String;
      
      private var _text:TextField;
      
      private var _index:int;
      
      private var _total:int;
      
      private var _sound:String;
      
      private var _endFunc:Function;
      
      public function Typer()
      {
         super();
         this._timer = new Timer(30);
         this._timer.addEventListener(TimerEvent.TIMER,this.onType);
      }
      
      public function initTyper(param1:String, param2:TextField, param3:Function = null, param4:String = "AUDIO_TYPE") : void
      {
         this._strHTML = param1;
         this._text = param2;
         this._sound = param4;
         this._endFunc = param3;
         var _loc5_:TextField = new TextField();
         _loc5_.htmlText = param1;
         this._strTXT = _loc5_.text;
         this._total = this._strTXT.length;
         this._index = 0;
      }
      
      public function startTyper() : void
      {
         if(this._strHTML)
         {
            this._timer.start();
         }
         else
         {
            this.handlerEnd();
         }
      }
      
      public function stopTyper() : void
      {
         this._timer.stop();
      }
      
      private function onType(param1:TimerEvent) : void
      {
         if(this._index >= this._total)
         {
            this.handlerEnd();
         }
         else
         {
            FontUtil.appendText(this._text,this._strTXT.charAt(this._index));
            ++this._index;
         }
         SoundManager.instance().playNumAudio(this._sound);
      }
      
      public function handlerEnd() : void
      {
         this._timer.stop();
         FontUtil.setHtml(this._text,this._strHTML);
         if(this._endFunc != null)
         {
            this._endFunc();
         }
         SoundManager.instance().stopAudio(this._sound);
      }
      
      public function get running() : Boolean
      {
         return this._timer.running;
      }
      
      public function destroy() : void
      {
         this._timer.removeEventListener(TimerEvent.TIMER,this.onType);
         this._timer = null;
         this._text = null;
         this._endFunc = null;
      }
   }
}

