package mogames.gameData.map
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import file.AreaConfig;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.MissionProxy;
   import mogames.gameData.mission.base.MissionFlagVO;
   import mogames.gameData.town.TownProxy;
   import mogames.gameData.town.vo.TownGameVO;
   
   public class AreaVO
   {
      
      public var id:int;
      
      public var name:String;
      
      public var tipX:int;
      
      public var tipY:int;
      
      private var _openID:Oint = new Oint();
      
      private var _secretFlag:Oint = new Oint();
      
      public function AreaVO(param1:int, param2:String, param3:int, param4:int, param5:int, param6:int = 0)
      {
         super();
         this.id = param1;
         this.name = param2;
         this.tipX = param4;
         this.tipY = param5;
         MathUtil.saveINT(this._openID,param3);
         MathUtil.saveINT(this._secretFlag,param6);
      }
      
      private function get openID() : int
      {
         return MathUtil.loadINT(this._openID);
      }
      
      public function get isOpen() : Boolean
      {
         if(this.openID == 100)
         {
            return FlagProxy.instance().openFlag.isComplete(100);
         }
         return AreaConfig.instance().findArea(this.openID).isFinish;
      }
      
      public function get isFinish() : Boolean
      {
         var _loc2_:MissionFlagVO = null;
         var _loc1_:Array = MissionProxy.instance().findAreaMissions(this.id);
         if(_loc1_.length <= 0)
         {
            return false;
         }
         for each(_loc2_ in _loc1_)
         {
            if(!_loc2_.isFinish)
            {
               return false;
            }
         }
         return true;
      }
      
      public function get isSecret() : Boolean
      {
         var _loc2_:MissionFlagVO = null;
         if(Sanx.isLocal)
         {
            return true;
         }
         var _loc1_:Array = MissionProxy.instance().findAreaMissions(this.id);
         if(_loc1_.length <= 0)
         {
            return false;
         }
         for each(_loc2_ in _loc1_)
         {
            if(_loc2_.starNum < 2)
            {
               return false;
            }
         }
         return true;
      }
      
      public function get isBattleTask() : Boolean
      {
         var _loc2_:TownGameVO = null;
         var _loc3_:int = 0;
         var _loc1_:Array = MissionProxy.instance().countFinishTaskTown(this.id);
         for each(_loc3_ in _loc1_)
         {
            _loc2_ = TownProxy.instance().findTown(_loc3_);
            if(_loc2_ && _loc2_.hasTask && _loc2_.townTask.isBattle)
            {
               return true;
            }
         }
         return false;
      }
      
      public function get secretFlag() : int
      {
         return MathUtil.loadINT(this._secretFlag);
      }
      
      public function get secretName() : String
      {
         return this.name + "秘境";
      }
   }
}

