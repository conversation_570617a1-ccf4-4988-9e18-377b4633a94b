package file
{
   import mogames.gameData.fuben.vo.FubenNormalVO;
   
   public class FubenSDConfig
   {
      
      public function FubenSDConfig()
      {
         super();
      }
      
      public function countSDReward(param1:FubenNormalVO) : Array
      {
         switch(param1.fubenID)
         {
            case 101:
               return JieBiaoConfig.instance().newReward(true);
            case 102:
               return MilinConfig.instance().newReward(true);
            case 103:
               return FeiXuConfig.instance().countWinReward(Math.random() + 0.5);
            case 104:
               return HaiGuConfig.instance().newReward(true);
            case 105:
               return KuangShanConfig.instance().newReward(true);
            case 106:
               return YouChuanConfig.instance().newReward(true);
            case 401:
               return HXLConfig.instance().newReward(true);
            case 402:
               return BHDConfig.instance().newReward(true);
            case 403:
               return QLSConfig.instance().newReward(true);
            case 404:
               return QSLConfig.instance().newReward(true);
            case 405:
               return XWDConfig.instance().newReward(true);
            case 406:
               return XWZZConfig.instance().newReward(true);
            case 407:
               return ZQDConfig.instance().newReward(true);
            case 408:
               return ZQSConfig.instance().newReward(true);
            case 601:
               return HuangMoConfig.instance().newReward(true);
            case 602:
               return DSGYConfig.instance().newReward(true);
            case 603:
               return HuLunConfig.instance().newReward(true);
            case 604:
               return MoBeiConfig.instance().newReward(true);
            case 605:
               return MokuConfig.instance().newReward(true);
            case 606:
               return ShanMaiConfig.instance().newReward(true);
            default:
               return null;
         }
      }
   }
}

