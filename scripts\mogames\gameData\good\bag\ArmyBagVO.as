package mogames.gameData.good.bag
{
   import com.mogames.data.Oint;
   import com.mogames.event.GameSignal;
   import com.mogames.event.SignalManager;
   import com.mogames.sound.SoundManager;
   import com.mogames.utils.MathUtil;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.good.bag.base.GameBagVO;
   import mogames.gameData.good.base.ConstBagVO;
   import mogames.gameData.mission.BattleProxy;
   import mogames.gameData.role.battle.BattleFriendVO;
   import mogames.gameData.role.battle.BattleHeroVO;
   import mogames.gameData.role.hero.HeroGameVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameRole.RoleFactory;
   import mogames.gameRole.TargetUtil;
   import mogames.gameRole.base.IRole;
   import mogames.gameRole.view.RoleBaseView;
   
   public class ArmyBagVO extends GameBagVO
   {
      
      private var _num:Oint = new Oint();
      
      public function ArmyBagVO(param1:ConstBagVO, param2:int)
      {
         super(param1);
         MathUtil.saveINT(this._num,param2);
      }
      
      override public function handerUse(param1:Function, param2:Function) : void
      {
         var _loc8_:BattleFriendVO = null;
         var _loc9_:IRole = null;
         if(BattleProxy.instance().battleType == 11)
         {
            EffectManager.addPureText("荆州之战无法使用兵卷轴！");
            if(param2 != null)
            {
               param2();
            }
            return;
         }
         if(BattleProxy.instance().battleType == 4)
         {
            EffectManager.addPureText("劫镖副本无法使用兵卷轴！");
            if(param2 != null)
            {
               param2();
            }
            return;
         }
         var _loc3_:Array = TargetUtil.friendHeros();
         if(_loc3_.length <= 0)
         {
            EffectManager.addPureText("战场中没有我方武将，无法召集士兵！");
            if(param2 != null)
            {
               param2();
            }
            return;
         }
         var _loc4_:HeroGameVO = (_loc3_[int(Math.random() * _loc3_.length)].roleVO as BattleHeroVO).heroVO;
         var _loc5_:Rectangle = BattleProxy.instance().dataMove.rectFar;
         var _loc6_:int = MathUtil.loadINT(this._num);
         var _loc7_:int = 0;
         while(_loc7_ < _loc6_)
         {
            _loc8_ = new BattleFriendVO();
            _loc8_.createData(_loc4_,0);
            _loc9_ = RoleFactory.newRole(_loc4_.heroInfo.armyID);
            _loc9_.createVO(_loc8_,BattleProxy.instance().dataMove.findFriendRect(_loc8_.skinVO.isFar));
            _loc9_.createView(new RoleBaseView(),1,1);
            _loc9_.createOut(null);
            _loc9_.x = _loc5_.x + Math.random() * _loc5_.width;
            _loc9_.y = _loc5_.y + Math.random() * _loc5_.height;
            Layers.obLayer.addChild(_loc9_.view);
            EffectManager.addBMC("SEQ_COME_UP_CLIP",Layers.frontLayer,_loc9_.x,_loc9_.y);
            SignalManager.signalRole.dispatchEvent({
               "signal":GameSignal.ROLE_ADD,
               "type":"add_friend_army",
               "role":_loc9_
            });
            _loc7_++;
         }
         SoundManager.instance().playAudio("AUDIO_BING_FU");
         super.handerUse(param1,param2);
      }
   }
}

