package mogames.gameData.mission.extra
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ExtraWave2001
   {
      
      public function ExtraWave2001()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2001);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(243,400000,2850,600,80,80,300,90,new BossSkillData0(150,{
            "hurt":3500,
            "hurtCount":5
         },5),1002,0);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(247,7500,950,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(17,new RoleArgVO(247,7500,950,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(17,new RoleArgVO(247,7500,950,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(17,new RoleArgVO(247,7500,950,100,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(240,87000,3400,450,50,80,150,80,new BossSkillData1(16,{"hurt":2250},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(17,new RoleArgVO(247,7500,950,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(17,new RoleArgVO(247,7500,950,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(17,new RoleArgVO(247,7500,950,100,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(240,87000,3400,450,50,80,150,80,new BossSkillData1(16,{"hurt":2250},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(17,new RoleArgVO(247,7500,950,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(17,new RoleArgVO(247,7500,950,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(17,new RoleArgVO(247,7500,950,100,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(240,87000,3400,450,50,80,150,80,new BossSkillData1(16,{"hurt":2250},2),1011,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

