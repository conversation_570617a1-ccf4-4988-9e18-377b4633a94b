package mogames.gameData.mall
{
   import com.mogames.utils.TxtUtil;
   import mogames.gameData.game.LackVO;
   import mogames.gameData.mall.vo.MallBaseVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameUI.prompt.DecideModule;
   
   public class MallProxy
   {
      
      private static var _instance:MallProxy;
      
      public function MallProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
      }
      
      public static function instance() : MallProxy
      {
         if(!_instance)
         {
            _instance = new MallProxy();
         }
         return _instance;
      }
      
      public function handlerBuy(param1:MallBaseVO, param2:int, param3:Function) : void
      {
         var confirmBuy:Function = null;
         var vo:MallBaseVO = param1;
         var buyNum:int = param2;
         var okFunc:Function = param3;
         confirmBuy = function():void
         {
            var _loc1_:LackVO = vo.checkLack(buyNum);
            if(_loc1_ != null)
            {
               EffectManager.addPureText(TxtUtil.setColor(_loc1_.str));
            }
            else
            {
               vo.handlerBuy(buyNum,okFunc);
            }
         };
         DecideModule.instance().showDecide(vo.askBuy(buyNum),true,confirmBuy,null,"是的，我要购买！","等下，我再看看！");
      }
   }
}

