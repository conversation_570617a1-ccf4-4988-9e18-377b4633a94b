package mogames.gameData.boon
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import com.mogames.utils.TxtUtil;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.flag.base.FlagVO;
   
   public class HYInfoVO
   {
      
      private var _flag:Oint = new Oint();
      
      private var _num:Oint = new Oint();
      
      private var _infor:String;
      
      public function HYInfoVO(param1:int, param2:int, param3:String)
      {
         super();
         MathUtil.saveINT(this._flag,param1);
         MathUtil.saveINT(this._num,param2);
         this._infor = param3;
      }
      
      public function get isComplete() : Boolean
      {
         return FlagProxy.instance().limitFlag.isComplete(MathUtil.loadINT(this._flag));
      }
      
      public function get num() : int
      {
         return MathUtil.loadINT(this._num);
      }
      
      public function get infor() : String
      {
         return this._infor;
      }
      
      public function get process() : String
      {
         var _loc1_:FlagVO = FlagProxy.instance().limitFlag.findFlag(MathUtil.loadINT(this._flag));
         var _loc2_:* = "（" + _loc1_.cur + "/" + _loc1_.total + "）";
         if(_loc1_.isComplete)
         {
            return TxtUtil.setColor(_loc2_,"99ff00");
         }
         return TxtUtil.setColor(_loc2_,"ff0000");
      }
   }
}

