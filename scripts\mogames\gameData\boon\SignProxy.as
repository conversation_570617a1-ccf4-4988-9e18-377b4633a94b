package mogames.gameData.boon
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import file.SignConfig;
   import mogames.gameData.ServerProxy;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.flag.base.FlagVO;
   
   public class SignProxy
   {
      
      private static var _instance:SignProxy;
      
      private var _startFlag:Oint = new Oint();
      
      public function SignProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         MathUtil.saveINT(this._startFlag,156);
      }
      
      public static function instance() : SignProxy
      {
         if(!_instance)
         {
            _instance = new SignProxy();
         }
         return _instance;
      }
      
      public function monthRefresh() : void
      {
         var _loc1_:int = 0;
         while(_loc1_ < 31)
         {
            FlagProxy.instance().openFlag.findFlag(this.startFlag + _loc1_).setValue(0);
            _loc1_++;
         }
         SignConfig.instance().monthRefresh();
      }
      
      public function checkCanBQ(param1:int) : Boolean
      {
         if(param1 <= 1)
         {
            return false;
         }
         var _loc2_:int = 1;
         while(_loc2_ < param1)
         {
            if(!this.findFlag(_loc2_).isComplete)
            {
               return true;
            }
            _loc2_++;
         }
         return false;
      }
      
      public function countMaxBQ(param1:int) : int
      {
         var _loc2_:int = 0;
         var _loc3_:int = 1;
         while(_loc3_ < param1)
         {
            if(!this.findFlag(_loc3_).isComplete)
            {
               _loc2_++;
            }
            _loc3_++;
         }
         return _loc2_;
      }
      
      public function handlerOneBQ() : int
      {
         var _loc2_:FlagVO = null;
         var _loc1_:int = ServerProxy.instance().curTS.day;
         var _loc3_:int = 1;
         while(_loc3_ < _loc1_)
         {
            _loc2_ = this.findFlag(_loc3_);
            if(!_loc2_.isComplete)
            {
               _loc2_.setValue(1);
               return _loc3_;
            }
            _loc3_++;
         }
         return 0;
      }
      
      public function handlerBuQian(param1:int) : void
      {
         var _loc2_:int = 0;
         while(_loc2_ < param1)
         {
            this.handlerOneBQ();
            _loc2_++;
         }
      }
      
      public function handlerSign(param1:int) : void
      {
         this.findFlag(param1).setValue(1);
      }
      
      public function hasSign(param1:int) : Boolean
      {
         return this.findFlag(param1).isComplete;
      }
      
      public function get canSign() : Boolean
      {
         return !this.hasSign(ServerProxy.instance().newTS.day);
      }
      
      public function get signTotal() : int
      {
         var _loc1_:int = 0;
         var _loc2_:FlagVO = null;
         var _loc3_:int = 0;
         while(_loc3_ < 31)
         {
            if(this.findFlag(_loc3_ + 1).isComplete)
            {
               _loc1_++;
            }
            _loc3_++;
         }
         return _loc1_;
      }
      
      public function findFlag(param1:int) : FlagVO
      {
         return FlagProxy.instance().openFlag.findFlag(this.startFlag + param1 - 1);
      }
      
      public function get startFlag() : int
      {
         return MathUtil.loadINT(this._startFlag);
      }
      
      public function get tipGet() : Boolean
      {
         return this.canSign || SignConfig.instance().canGet;
      }
   }
}

