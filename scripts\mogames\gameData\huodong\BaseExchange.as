package mogames.gameData.huodong
{
   import com.mogames.utils.TxtUtil;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import mogames.gameEffect.EffectManager;
   import mogames.gameUI.prompt.LockMessage;
   import unit4399.Open4399Func;
   
   public class BaseExchange
   {
      
      protected var _okFunc:Function;
      
      protected var _loader:URLLoader;
      
      protected var _req:URLRequest;
      
      protected var _key:String;
      
      public function BaseExchange()
      {
         super();
         this._loader = new URLLoader();
         this._loader.addEventListener(IOErrorEvent.IO_ERROR,this.errorHandler);
         this._loader.addEventListener(Event.COMPLETE,this.loaderCompleteHandler);
      }
      
      public function init(param1:Function) : void
      {
         this._okFunc = param1;
      }
      
      public function startExchange(... rest) : void
      {
      }
      
      public function get uid() : String
      {
         return Open4399Func.userData.uid;
      }
      
      protected function handlerLoaded(param1:Object) : void
      {
         LockMessage.instance().destroy();
      }
      
      protected function countKey(param1:Array) : String
      {
         return "";
      }
      
      private function loaderCompleteHandler(param1:Event) : void
      {
         this.handlerLoaded(this._loader.data);
      }
      
      private function errorHandler(param1:IOErrorEvent) : void
      {
         LockMessage.instance().destroy();
         EffectManager.addPureText(TxtUtil.setColor("服务器异常，请重试！"));
      }
      
      public function destroy() : void
      {
         this._loader.removeEventListener(IOErrorEvent.IO_ERROR,this.errorHandler);
         this._loader.removeEventListener(Event.COMPLETE,this.loaderCompleteHandler);
         this._loader = null;
         this._loader = null;
      }
   }
}

