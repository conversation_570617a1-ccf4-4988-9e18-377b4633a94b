package mogames.gameData.good.base
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import file.GoodConfig;
   import mogames.ConstData;
   import mogames.gameData.good.vo.GameATTVO;
   
   public class KacaoVO
   {
      
      private var _index:Oint = new Oint();
      
      private var _attVO:GameATTVO;
      
      public function KacaoVO(param1:int)
      {
         super();
         MathUtil.saveINT(this._index,param1);
      }
      
      public function addCard(param1:int) : void
      {
         if(param1 == 0)
         {
            this._attVO = null;
         }
         else
         {
            this._attVO = GoodConfig.instance().newGameGood(param1) as GameATTVO;
         }
      }
      
      public function get hasCard() : <PERSON><PERSON>an
      {
         return this._attVO != null;
      }
      
      public function get attVO() : GameATTVO
      {
         return this._attVO;
      }
      
      public function get infor() : String
      {
         if(!this._attVO)
         {
            return "未插入卡片";
         }
         return this._attVO.qualityName;
      }
      
      public function get index() : int
      {
         return MathUtil.loadINT(this._index);
      }
      
      public function set loadData(param1:Array) : void
      {
         if(param1[1] != ConstData.NONE)
         {
            this.addCard(int(param1[1]));
         }
      }
      
      public function get saveData() : String
      {
         var _loc1_:Array = [];
         _loc1_[0] = this.index;
         _loc1_[1] = this._attVO ? this._attVO.constGood.id : ConstData.NONE;
         return _loc1_.join("H");
      }
   }
}

