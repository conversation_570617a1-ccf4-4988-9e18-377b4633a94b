package file
{
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.fuben.vo.FubenNormalVO;
   import mogames.gameData.fuben.vo.FubenTZBZVO;
   import mogames.gameData.fuben.vo.FubenTZQLVO;
   import mogames.gameData.fuben.vo.FubenVO;
   
   public class FubenConfig
   {
      
      private static var _instance:FubenConfig;
      
      private var _list:Array;
      
      public function FubenConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : FubenConfig
      {
         if(!_instance)
         {
            _instance = new FubenConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = [];
         this._list[this._list.length] = new FubenNormalVO(101,1,{
            "level":10,
            "num":0
         },[new BaseRewardVO(10000,1),new BaseRewardVO(50004,1),new BaseRewardVO(10271,1),new BaseRewardVO(10272,1),new BaseRewardVO(10006,15),new BaseRewardVO(10007,15),new BaseRewardVO(10851,1),new BaseRewardVO(50004,1),new BaseRewardVO(50004,1),new BaseRewardVO(50004,1),new BaseRewardVO(50004,1),new BaseRewardVO(50004,1),new BaseRewardVO(50004,1),new BaseRewardVO(50004,1),new BaseRewardVO(50004,1)],"一伙反叛势力正运输物资进入大都，企图在城内发动叛变，速速拦截。<br><br>" + "点击武将可释放武将技能（出现光圈）；");
         this._list[this._list.length] = new FubenNormalVO(102,2,{
            "level":15,
            "num":8
         },[new BaseRewardVO(10000,1),new BaseRewardVO(10821,1),new BaseRewardVO(68001,1),new BaseRewardVO(68001,2),new BaseRewardVO(68001,3),new BaseRewardVO(68001,2),new BaseRewardVO(68001,1),new BaseRewardVO(68002,1),new BaseRewardVO(68002,2),new BaseRewardVO(68002,3),new BaseRewardVO(68002,2),new BaseRewardVO(68002,1),new BaseRewardVO(10980,1),new BaseRewardVO(10980,1),new BaseRewardVO(10306,1),new BaseRewardVO(10306,1),new BaseRewardVO(10306,1)],"王大头偶得一藏宝图，带领手下进入迷林寻宝，贼人得知后中途设了埋伏。<br><br>" + "副本需出战8名武将；");
         this._list[this._list.length] = new FubenNormalVO(103,3,{
            "level":5,
            "num":0
         },[new BaseRewardVO(10000,1),new BaseRewardVO(10402,1),new BaseRewardVO(10403,1),new BaseRewardVO(10404,2),new BaseRewardVO(10411,1),new BaseRewardVO(10412,1),new BaseRewardVO(68001,1),new BaseRewardVO(68002,1)],"王大头独自废墟中找到了银票宝藏，被贼人团团围住，准备抢夺宝藏。<br><br>" + "胜利后的银票奖励与宝藏血量有关；");
         this._list[this._list.length] = new FubenNormalVO(104,4,{
            "level":10,
            "num":5
         },[new BaseRewardVO(10110,1),new BaseRewardVO(10111,1),new BaseRewardVO(10112,1),new BaseRewardVO(10113,1)],"骸骨之地游荡着将相亡魂，听说聚齐亡魂，用招魂幡法器可使其复活。<br><br>" + "副本需出战五名武将；");
         this._list[this._list.length] = new FubenNormalVO(105,5,{
            "level":30,
            "num":0
         },[new BaseRewardVO(10521,1),new BaseRewardVO(10522,1),new BaseRewardVO(10523,1),new BaseRewardVO(10521,1),new BaseRewardVO(10524,1),new BaseRewardVO(10525,1),new BaseRewardVO(10526,1)],"矿洞地形险恶，物资丰富，王大头独自勘查中被一伙神秘势力围剿。<br><br>" + "可使用鼠标攻击投石车；");
         this._list[this._list.length] = new FubenNormalVO(106,6,{
            "level":40,
            "num":0
         },[new BaseRewardVO(10300,1),new BaseRewardVO(10816,1),new BaseRewardVO(10300,2),new BaseRewardVO(10533,2),new BaseRewardVO(10534,2),new BaseRewardVO(10535,2)],"一艘年代久远的战船停靠在幽谷湖泊边上，据说里面的幽魂守卫着稀有宝物。<br><br>" + "使用鼠标收服幽魂；");
         this._list[this._list.length] = new FubenNormalVO(401,7,{
            "level":50,
            "num":10
         },[new BaseRewardVO(10308,2),new BaseRewardVO(10921,2),new BaseRewardVO(10308,3),new BaseRewardVO(10380,1),new BaseRewardVO(10380,1),new BaseRewardVO(10380,1),new BaseRewardVO(10921,1),new BaseRewardVO(10418,2),new BaseRewardVO(10419,1)],"白虎是远古神话传说中的神兽，即四象之一。<br><br>" + "副本需出战10名武将；");
         this._list[this._list.length] = new FubenNormalVO(402,8,{
            "level":55,
            "num":0
         },[new BaseRewardVO(18611,1),new BaseRewardVO(18611,1),new BaseRewardVO(10151,2),new BaseRewardVO(10152,1),new BaseRewardVO(10153,1),new BaseRewardVO(10154,1),new BaseRewardVO(10151,2),new BaseRewardVO(10152,2),new BaseRewardVO(10153,2),new BaseRewardVO(10154,2),new BaseRewardVO(10309,1)],"王大头独身一人前往白虎洞探寻秘密，被一条暗河挡住了去路。<br><br>");
         this._list[this._list.length] = new FubenNormalVO(403,9,{
            "level":60,
            "num":0
         },[new BaseRewardVO(10381,1),new BaseRewardVO(10381,1),new BaseRewardVO(10381,1),new BaseRewardVO(10310,2),new BaseRewardVO(10310,3),new BaseRewardVO(10922,2),new BaseRewardVO(10922,1),new BaseRewardVO(10419,2),new BaseRewardVO(10419,2)],"青龙是远古神话传说中的神兽，即四象之一。<br><br>" + "青龙蛋可被攻击（物攻、技能）；");
         this._list[this._list.length] = new FubenNormalVO(404,10,{
            "level":65,
            "num":0
         },[new BaseRewardVO(18601,1),new BaseRewardVO(18601,1),new BaseRewardVO(10155,2),new BaseRewardVO(10156,1),new BaseRewardVO(10157,1),new BaseRewardVO(10158,1),new BaseRewardVO(10155,2),new BaseRewardVO(10156,2),new BaseRewardVO(10157,2),new BaseRewardVO(10158,2),new BaseRewardVO(10309,1)],"王大头又安奈不住，去寻青龙宝藏，在青龙阵中，苦寻离开的办法。<br><br>" + "可使用鼠标攻击青龙蛋；");
         this._list[this._list.length] = new FubenNormalVO(405,11,{
            "level":70,
            "num":0
         },[new BaseRewardVO(10382,1),new BaseRewardVO(10382,1),new BaseRewardVO(10382,1),new BaseRewardVO(10313,2),new BaseRewardVO(10313,3),new BaseRewardVO(10923,2),new BaseRewardVO(10923,1)],"玄武是远古神话传说中的神兽，即四象之一。<br><br>");
         this._list[this._list.length] = new FubenNormalVO(406,12,{
            "level":75,
            "num":0
         },[new BaseRewardVO(18621,1),new BaseRewardVO(18621,1),new BaseRewardVO(10161,2),new BaseRewardVO(10162,1),new BaseRewardVO(10163,1),new BaseRewardVO(10164,1),new BaseRewardVO(10161,2),new BaseRewardVO(10162,2),new BaseRewardVO(10163,2),new BaseRewardVO(10164,2),new BaseRewardVO(10309,1)],"听说沼泽深处时有虹光闪烁，王大头决定一探究竟。<br><br>" + "该副本无法出战武将；");
         this._list[this._list.length] = new FubenNormalVO(407,16,{
            "level":80,
            "num":0
         },[new BaseRewardVO(10383,1),new BaseRewardVO(10383,1),new BaseRewardVO(10383,1),new BaseRewardVO(10314,2),new BaseRewardVO(10314,3),new BaseRewardVO(10924,2),new BaseRewardVO(10924,1)],"朱雀是远古神话传说中的神兽，即四象之一。<br><br>" + "副本需出战10名武将；");
         this._list[this._list.length] = new FubenNormalVO(408,17,{
            "level":85,
            "num":0
         },[new BaseRewardVO(18631,1),new BaseRewardVO(18631,1),new BaseRewardVO(10165,2),new BaseRewardVO(10166,1),new BaseRewardVO(10167,1),new BaseRewardVO(10168,1),new BaseRewardVO(10165,2),new BaseRewardVO(10166,2),new BaseRewardVO(10167,2),new BaseRewardVO(10168,2),new BaseRewardVO(10309,1)],"王大头独自一人来到朱雀岛深处的朱雀山，寻求其中的宝藏。<br><br>" + "该副本无法出战武将；");
         this._list[this._list.length] = new FubenTZQLVO(409,{
            "level":90,
            "num":0
         });
         this._list[this._list.length] = new FubenTZBZVO(410,{
            "level":120,
            "num":0
         });
         this._list[this._list.length] = new FubenNormalVO(601,13,{
            "level":70,
            "num":0
         },[new BaseRewardVO(10000,1),new BaseRewardVO(10802,1),new BaseRewardVO(10807,1),new BaseRewardVO(10812,1),new BaseRewardVO(10812,1),new BaseRewardVO(10817,1),new BaseRewardVO(10822,1)],"荒原看似荒芜，实则暗藏凶险。<br><br>" + "副本需出战8名武将；");
         this._list[this._list.length] = new FubenNormalVO(602,14,{
            "level":75,
            "num":0
         },[new BaseRewardVO(10000,1),new BaseRewardVO(10554,1),new BaseRewardVO(10555,1),new BaseRewardVO(10554,1),new BaseRewardVO(10555,1)],"王大头在多斯高原与匈奴正面对决。<br><br>" + "使用鼠标攻击冒火木桶；");
         this._list[this._list.length] = new FubenNormalVO(603,15,{
            "level":80,
            "num":0
         },[new BaseRewardVO(10000,1),new BaseRewardVO(10556,1),new BaseRewardVO(10557,1),new BaseRewardVO(10556,1),new BaseRewardVO(10557,1)],"进军经过呼伦途中，王大头遭遇到埋伏。<br><br>");
         this._list[this._list.length] = new FubenNormalVO(604,18,{
            "level":85,
            "num":0
         },[new BaseRewardVO(10000,1),new BaseRewardVO(10420,1),new BaseRewardVO(10421,1),new BaseRewardVO(10420,2),new BaseRewardVO(10421,2)],"大军经过漠北，沙漠远处发现匈奴正在抓村民，王大头决定前去营救。<br><br>" + "副本需出战8名武将；");
         this._list[this._list.length] = new FubenNormalVO(605,19,{
            "level":95,
            "num":0
         },[new BaseRewardVO(10000,1),new BaseRewardVO(10567,1),new BaseRewardVO(10568,1),new BaseRewardVO(10567,1),new BaseRewardVO(10568,1)],"莫窟迷路，遭遇匈奴！<br><br>" + "副本需出战8名武将；");
         this._list[this._list.length] = new FubenNormalVO(606,20,{
            "level":100,
            "num":0
         },[new BaseRewardVO(10000,1),new BaseRewardVO(10569,1),new BaseRewardVO(10570,1),new BaseRewardVO(10569,1),new BaseRewardVO(10570,1)],"阿泰尔山脉探险！<br><br>" + "副本需出战8名武将；");
      }
      
      public function findFuben(param1:int) : FubenVO
      {
         var _loc2_:FubenVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.fubenID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function showDrops(param1:Array) : Array
      {
         var _loc3_:BaseRewardVO = null;
         var _loc2_:Array = [];
         for each(_loc3_ in param1)
         {
            if(!this.checkMulit(_loc3_,_loc2_))
            {
               _loc2_[_loc2_.length] = new BaseRewardVO(_loc3_.constGood.id,1);
            }
         }
         return _loc2_;
      }
      
      private function checkMulit(param1:BaseRewardVO, param2:Array) : Boolean
      {
         var _loc3_:BaseRewardVO = null;
         for each(_loc3_ in param2)
         {
            if(_loc3_.constGood.id == param1.constGood.id)
            {
               return true;
            }
         }
         return false;
      }
   }
}

