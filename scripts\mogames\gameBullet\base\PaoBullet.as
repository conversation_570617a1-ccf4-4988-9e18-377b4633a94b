package mogames.gameBullet.base
{
   import com.greensock.TweenMax;
   import com.greensock.easing.Linear;
   import flash.geom.Point;
   import mogames.Layers;
   
   public class PaoBullet extends BaseBullet
   {
      
      private var _start:Point;
      
      private var _end:Point;
      
      private var _center:Point;
      
      public function PaoBullet()
      {
         super();
         this._start = new Point();
         this._end = new Point();
         this._center = new Point();
      }
      
      override public function start(param1:int, param2:int) : void
      {
         this._start.setTo(param1,param2);
         this._end.setTo(_target.x,_target.y - _target.height * 0.5);
         if(Point.distance(this._start,this._end) <= 50)
         {
            handlerEnd();
         }
         else
         {
            this.startPao();
         }
      }
      
      private function startPao() : void
      {
         this._center.x = (this._start.x + this._end.x) * 0.5;
         this._center.y = this._start.y > this._end.y ? this._end.y : this._start.y;
         var _loc1_:int = Math.abs(this._end.x - this._start.x);
         if(Math.abs(this._start.y - this._end.y) < _loc1_ * 0.5)
         {
            this._center.y -= _loc1_ * 0.5;
         }
         _bmc.setLocation(this._start.x,this._start.y);
         TweenMax.to(_bmc,_loc1_ / _speed * 0.03,{
            "x":this._end.x,
            "y":this._end.y,
            "bezierThrough":[{
               "x":this._center.x,
               "y":this._center.y
            }],
            "orientToBezier":true,
            "ease":Linear.easeNone,
            "onComplete":handlerEnd
         });
         Layers.render.add(this.updateLocation);
      }
      
      override protected function updateLocation() : void
      {
         if(canHurt)
         {
            handlerEnd();
         }
      }
      
      override public function recyle() : void
      {
         TweenMax.killTweensOf(_bmc);
         super.recyle();
      }
   }
}

