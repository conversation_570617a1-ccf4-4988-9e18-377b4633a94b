package file
{
   import mogames.gameData.base.func.ZBaoHCVO;
   import mogames.gameData.base.vo.NeedVO;
   
   public class ZBaoHCConfig
   {
      
      private static var _instance:ZBaoHCConfig;
      
      private var _list:Array;
      
      public var stoneNeed:NeedVO;
      
      public function ZBaoHCConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : ZBaoHCConfig
      {
         if(!_instance)
         {
            _instance = new ZBaoHCConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this.stoneNeed = new NeedVO(10981,1);
         this._list = [];
         this._list[this._list.length] = new ZBaoHCVO(30050,888888,666,500,[new NeedVO(10831,30)]);
         this._list[this._list.length] = new ZBaoHCVO(30034,888888,666,500,[new NeedVO(10831,30)]);
         this._list[this._list.length] = new ZBaoHCVO(30003,888888,500,1000,[new NeedVO(10915,20)]);
         this._list[this._list.length] = new ZBaoHCVO(30005,100000,100,1000,[new NeedVO(10550,20)]);
         this._list[this._list.length] = new ZBaoHCVO(30016,200000,100,1000,[new NeedVO(10558,20)]);
         this._list[this._list.length] = new ZBaoHCVO(30018,500000,300,1000,[new NeedVO(10563,20)]);
         this._list[this._list.length] = new ZBaoHCVO(30001,888888,500,500,[new NeedVO(10831,10)]);
         this._list[this._list.length] = new ZBaoHCVO(30027,800000,2000,500,[new NeedVO(10577,20),new NeedVO(10578,20),new NeedVO(10579,20)]);
         this._list[this._list.length] = new ZBaoHCVO(30028,800000,3000,500,[new NeedVO(10585,30),new NeedVO(10586,30),new NeedVO(10587,30)]);
         this._list[this._list.length] = new ZBaoHCVO(30030,800000,3000,500,[new NeedVO(10594,30),new NeedVO(10595,30),new NeedVO(10596,30)]);
         this._list[this._list.length] = new ZBaoHCVO(30152,888888,300,700,[new NeedVO(10831,50)]);
         this._list[this._list.length] = new ZBaoHCVO(30151,888888,300,700,[new NeedVO(10831,50)]);
         this._list[this._list.length] = new ZBaoHCVO(30032,800000,500,400,[new NeedVO(10831,20)]);
         this._list[this._list.length] = new ZBaoHCVO(30033,800000,500,400,[new NeedVO(10831,20)]);
         this._list[this._list.length] = new ZBaoHCVO(30035,500000,300,700,[new NeedVO(10831,10)]);
         this._list[this._list.length] = new ZBaoHCVO(30029,500000,300,700,[new NeedVO(10831,10)]);
         this._list[this._list.length] = new ZBaoHCVO(30046,500000,300,700,[new NeedVO(10831,10)]);
         this._list[this._list.length] = new ZBaoHCVO(30045,500000,300,700,[new NeedVO(10831,10)]);
         this._list[this._list.length] = new ZBaoHCVO(30006,50000,100,500,[new NeedVO(10510,5)]);
         this._list[this._list.length] = new ZBaoHCVO(30011,50000,100,500,[new NeedVO(10530,10),new NeedVO(10531,10),new NeedVO(10532,10)]);
         this._list[this._list.length] = new ZBaoHCVO(30012,50000,100,500,[new NeedVO(10541,30)]);
         this._list[this._list.length] = new ZBaoHCVO(30013,50000,100,500,[new NeedVO(10542,10),new NeedVO(10543,10),new NeedVO(10544,10)]);
         this._list[this._list.length] = new ZBaoHCVO(30015,50000,100,500,[new NeedVO(10547,6),new NeedVO(10548,6),new NeedVO(10549,6)]);
         this._list[this._list.length] = new ZBaoHCVO(30019,100000,100,500,[new NeedVO(10553,20)]);
         this._list[this._list.length] = new ZBaoHCVO(30002,50000,100,700,[new NeedVO(10501,5)]);
         this._list[this._list.length] = new ZBaoHCVO(30004,50000,100,700,[new NeedVO(10503,5)]);
         this._list[this._list.length] = new ZBaoHCVO(30010,50000,100,700,[new NeedVO(10505,5)]);
         this._list[this._list.length] = new ZBaoHCVO(30014,50000,100,700,[new NeedVO(10539,5)]);
         this._list[this._list.length] = new ZBaoHCVO(30007,30000,60,900,[new NeedVO(10521,8),new NeedVO(10522,8)]);
         this._list[this._list.length] = new ZBaoHCVO(30008,30000,60,900,[new NeedVO(10523,8),new NeedVO(10524,8)]);
         this._list[this._list.length] = new ZBaoHCVO(30009,30000,60,900,[new NeedVO(10525,8),new NeedVO(10526,8)]);
      }
      
      public function get list() : Array
      {
         return this._list;
      }
   }
}

