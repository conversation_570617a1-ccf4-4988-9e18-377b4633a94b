package mogames.gameData.achieve.vo
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import mogames.gameData.achieve.base.AchieveLockVO;
   import mogames.gameData.mission.MissionProxy;
   
   public class TownAchieveVO extends AchieveLockVO
   {
      
      private var _mid:Oint = new Oint();
      
      public function TownAchieveVO(param1:int, param2:int, param3:Array, param4:String, param5:String)
      {
         super(param1,param3,param4,param5);
         MathUtil.saveINT(this._mid,param2);
      }
      
      override public function checkOpen(param1:int = 1) : void
      {
         if(isOpen)
         {
            return;
         }
         MathUtil.saveINT(_open,int(MissionProxy.instance().isFinish(MathUtil.loadINT(this._mid))));
         dispatchTip();
      }
   }
}

