package mogames.gameData.good.bag.base
{
   import com.mogames.event.GameSignal;
   import com.mogames.event.SignalManager;
   import mogames.gameData.good.base.ConstBagVO;
   import mogames.gameData.good.vo.GameGoodVO;
   
   public class GameBagVO extends GameGoodVO
   {
      
      protected var _constBag:ConstBagVO;
      
      public function GameBagVO(param1:ConstBagVO)
      {
         super(param1);
         this._constBag = param1;
      }
      
      public function handerUse(param1:Function, param2:Function) : void
      {
         if(param1 != null)
         {
            param1();
         }
         SignalManager.signalReq.dispatchEvent({"signal":GameSignal.USE_BAG});
      }
      
      override public function get infor() : String
      {
         return _constVO.infor + "<br><b" + "冷却时间：" + this.cd + "秒<br>";
      }
      
      public function get cd() : Number
      {
         return this._constBag.constCD;
      }
   }
}

