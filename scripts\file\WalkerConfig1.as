package file
{
   import flash.geom.Point;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.main.vo.WalkerVO;
   
   public class WalkerConfig1
   {
      
      private static var _instance:WalkerConfig1;
      
      private var _walkers:Array;
      
      private var _paths:Array;
      
      private var _temp:Array;
      
      private var _rewards:Array;
      
      public function WalkerConfig1()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : WalkerConfig1
      {
         if(!_instance)
         {
            _instance = new WalkerConfig1();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._walkers = [];
         this._walkers[this._walkers.length] = new WalkerVO(201,["城里的乱党被一波不明来历的人给灭了。","大都原来的主公其实也不怎么样。","年纪大了，多走动走动有好处。"]);
         this._walkers[this._walkers.length] = new WalkerVO(202,["年纪大了，走会路都喘的厉害。","哎？我这是要去哪里来着？","我儿子可孝顺了，经常给我做好吃的。"]);
         this._walkers[this._walkers.length] = new WalkerVO(203,["不知道新来的城主怎样？","别看我年级大，再娶个媳妇也没问题！咳咳！","在这边歇歇脚，明天再赶路！"]);
         this._walkers[this._walkers.length] = new WalkerVO(204,["看出来了么，我是个郎中。","病从口入，吃东西一定要干净。","每逢佳节胖三斤，陈胖子就是例子！"]);
         this._walkers[this._walkers.length] = new WalkerVO(205,["还是杜康的酒好喝！","走开，别档我道，哪来的土鳖，一边去！","老板娘酿的酒真烈，够味！"]);
         this._walkers[this._walkers.length] = new WalkerVO(206,["6年没回来，这次回来都认不出了！","来来来，喝一杯，不醉不归。","我有个逗比弟弟朝雾也住在大都！"]);
         this._walkers[this._walkers.length] = new WalkerVO(207,["我和我双胞胎大哥都住在这里。","在外头漂久了，回到大都感觉变化真大！","我是不是看着不像本地人？"]);
         this._walkers[this._walkers.length] = new WalkerVO(208,["听说以前有个叫炯爷的可厉害了！","我的理想是当一名惩奸除恶的女侠！","我可是会武功的哦！","老苏做的大饼可好吃了！人更帅，嘻嘻！"]);
         this._paths = [];
         this._paths[this._paths.length] = [new Point(829,521),new Point(494,340),new Point(387,464),new Point(336,445)];
         this._paths[this._paths.length] = [new Point(829,521),new Point(494,340),new Point(387,464),new Point(435,546),new Point(506,553)];
         this._paths[this._paths.length] = [new Point(829,521),new Point(656,435),new Point(469,374),new Point(387,464),new Point(257,500),new Point(282,446)];
         this._paths[this._paths.length] = [new Point(829,521),new Point(656,435),new Point(469,374),new Point(387,464),new Point(257,500),new Point(186,562),new Point(306,583)];
         this._paths[this._paths.length] = [new Point(829,521),new Point(656,435),new Point(469,374),new Point(387,464),new Point(257,500),new Point(186,562),new Point(57,584),new Point(80,554)];
         this._paths[this._paths.length] = [new Point(829,521),new Point(542,328),new Point(607,292)];
         this._paths[this._paths.length] = [new Point(829,521),new Point(542,328),new Point(739,286),new Point(661,291)];
         this._paths[this._paths.length] = [new Point(829,521),new Point(542,328),new Point(739,286),new Point(828,277)];
         this._paths[this._paths.length] = [new Point(829,521),new Point(542,328),new Point(739,286),new Point(764,371),new Point(829,395)];
         this._rewards = [new BaseRewardVO(10024,1),new BaseRewardVO(10025,1),new BaseRewardVO(10026,1),new BaseRewardVO(10000,111),new BaseRewardVO(10000,222),new BaseRewardVO(10000,333),new BaseRewardVO(10000,178),new BaseRewardVO(10000,231),new BaseRewardVO(10000,1000),new BaseRewardVO(10000,366),new BaseRewardVO(10000,320),new BaseRewardVO(10000,130),new BaseRewardVO(10000,188),new BaseRewardVO(10000,267),new BaseRewardVO(10000,368),new BaseRewardVO(10000,288),new BaseRewardVO(10000,388),new BaseRewardVO(10000,888)];
      }
      
      public function get rewardVO() : BaseRewardVO
      {
         return this._rewards[int(Math.random() * this._rewards.length)];
      }
      
      public function get pathsList() : Array
      {
         var _loc1_:Array = [];
         var _loc2_:int = 0;
         var _loc3_:int = int(this._paths.length);
         while(_loc2_ < _loc3_)
         {
            _loc1_[_loc2_] = _loc2_;
            _loc2_++;
         }
         return _loc1_;
      }
      
      public function get walkerList() : Array
      {
         var _loc2_:WalkerVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._walkers)
         {
            _loc1_[_loc1_.length] = _loc2_.id;
         }
         return _loc1_;
      }
      
      public function findVO(param1:int) : WalkerVO
      {
         var _loc2_:WalkerVO = null;
         for each(_loc2_ in this._walkers)
         {
            if(_loc2_.id == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function findPaths(param1:int) : Array
      {
         return this._paths[param1].slice();
      }
   }
}

