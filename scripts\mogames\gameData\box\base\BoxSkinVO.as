package mogames.gameData.box.base
{
   public class BoxSkinVO
   {
      
      private var _id:int;
      
      private var _width:int;
      
      private var _height:int;
      
      private var _skin:String;
      
      public function BoxSkinVO(param1:int, param2:int, param3:int, param4:String)
      {
         super();
         this._id = param1;
         this._width = param2;
         this._height = param3;
         this._skin = param4;
      }
      
      public function get id() : int
      {
         return this._id;
      }
      
      public function get width() : int
      {
         return this._width;
      }
      
      public function get height() : int
      {
         return this._height;
      }
      
      public function get standSkin() : String
      {
         return this._skin + "_STAND";
      }
      
      public function get openSkin() : String
      {
         return this._skin + "_OPEN";
      }
   }
}

