package mogames.gameData.good.bag
{
   import com.mogames.event.GameSignal;
   import com.mogames.event.SignalManager;
   import mogames.gameData.good.bag.base.GameBagVO;
   import mogames.gameData.good.base.ConstBagVO;
   import mogames.gameData.mission.BattleProxy;
   import mogames.gameEffect.EffectManager;
   
   public class ZanTingBagVO extends GameBagVO
   {
      
      public function ZanTingBagVO(param1:ConstBagVO)
      {
         super(param1);
      }
      
      override public function handerUse(param1:Function, param2:Function) : void
      {
         if(BattleProxy.instance().dataWave.lastWave)
         {
            EffectManager.addPureText("敌方已全部出战！");
            if(param2 != null)
            {
               param2();
            }
            return;
         }
         super.handerUse(param1,param2);
         SignalManager.signalBag.dispatchEvent({
            "signal":GameSignal.BAG_PAUSE,
            "time":20
         });
      }
   }
}

