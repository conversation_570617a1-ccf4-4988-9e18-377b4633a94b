package mogames.gameData.flag.base
{
   public class RoleFlagVO
   {
      
      public var roleID:int;
      
      public var isOpen:int;
      
      public var isSave:Boolean;
      
      public function RoleFlagVO(param1:int, param2:Boolean)
      {
         super();
         this.roleID = param1;
         this.isSave = param2;
         if(!this.isSave)
         {
            this.isOpen = 1;
         }
      }
   }
}

