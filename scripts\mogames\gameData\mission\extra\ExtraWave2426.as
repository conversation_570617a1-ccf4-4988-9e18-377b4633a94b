package mogames.gameData.mission.extra
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ExtraWave2426
   {
      
      public function ExtraWave2426()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2426);
         _loc1_.limitBR = new WaveLimitVO(99999999,0.8,0.8);
         _loc1_.zhuBoss = new BossArgVO(783,77000000,288000,140000,80,80,300,90,new BossSkillData0(150,{"hurt":250000},5),1010,0);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(772,1350000,135000,17000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(781,12500000,130000,30000,50,80,150,80,new BossSkillData0(250,{"hurt":200000},3),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(773,1350000,135000,17000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(13,new RoleArgVO(771,1350000,135000,17000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(771,1350000,135000,17000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(772,1350000,135000,17000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(771,1350000,135000,17000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(782,12500000,130000,30000,50,80,150,80,new BossSkillData1(10,{"hurt":200000},3),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(771,1350000,135000,17000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(773,1350000,135000,17000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(771,1350000,135000,17000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(13,new RoleArgVO(771,1350000,135000,17000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(772,1350000,135000,17000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(783,12500000,130000,30000,50,80,150,80,new BossSkillData1(12,{
            "hurt":250000,
            "roleNum":20
         },3),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(771,1350000,135000,17000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(13,new RoleArgVO(773,1350000,135000,17000,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(771,1350000,135000,17000,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(773,1350000,135000,17000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(783,12500000,130000,30000,50,80,150,80,new BossSkillData1(12,{
            "hurt":250000,
            "roleNum":20
         },3),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(773,1350000,135000,17000,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(782,12500000,130000,30000,50,80,150,80,new BossSkillData0(250,{"hurt":200000},3),1008,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

