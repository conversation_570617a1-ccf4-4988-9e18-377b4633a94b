package mogames.gameData.achieve.vo
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import mogames.gameData.achieve.base.AchieveLockVO;
   import mogames.gameData.mission.MissionProxy;
   
   public class StarAchieveVO extends AchieveLockVO
   {
      
      private var _area:Oint = new Oint();
      
      public function StarAchieveVO(param1:int, param2:int, param3:Array, param4:String, param5:String)
      {
         super(param1,param3,param4,param5);
         MathUtil.saveINT(this._area,param2);
      }
      
      override public function checkOpen(param1:int = 1) : void
      {
         if(isOpen)
         {
            return;
         }
         this.countOpen();
         dispatchTip();
      }
      
      private function countOpen() : void
      {
         var _loc1_:int = MathUtil.loadINT(this._area);
         var _loc2_:int = MissionProxy.instance().curStar(_loc1_);
         var _loc3_:int = MissionProxy.instance().totalStar(_loc1_);
         MathUtil.saveINT(_open,int(_loc2_ >= _loc3_));
      }
   }
}

