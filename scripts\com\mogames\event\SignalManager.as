package com.mogames.event
{
   public class SignalManager
   {
      
      public static const signalUI:GameSignal = new GameSignal();
      
      public static const signalCheat:GameSignal = new GameSignal();
      
      public static const signalMaster:GameSignal = new GameSignal();
      
      public static const signalGuide:GameSignal = new GameSignal();
      
      public static const signalRole:GameSignal = new GameSignal();
      
      public static const signalRecyle:GameSignal = new GameSignal();
      
      public static const signalRes:GameSignal = new GameSignal();
      
      public static const signalScroll:GameSignal = new GameSignal();
      
      public static const signalCasern:GameSignal = new GameSignal();
      
      public static const signalWave:GameSignal = new GameSignal();
      
      public static const signalSkill:GameSignal = new GameSignal();
      
      public static const signalBullet:GameSignal = new GameSignal();
      
      public static const signalBag:GameSignal = new GameSignal();
      
      public static const signalReq:GameSignal = new GameSignal();
      
      public static const signalTD:GameSignal = new GameSignal();
      
      public function SignalManager()
      {
         super();
      }
      
      public static function removeAll() : void
      {
         signalUI.removeAll();
         signalCheat.removeAll();
         signalMaster.removeAll();
         signalGuide.removeAll();
         signalRole.removeAll();
         signalRecyle.removeAll();
         signalRes.removeAll();
         signalScroll.removeAll();
         signalCasern.removeAll();
         signalWave.removeAll();
         signalSkill.removeAll();
      }
   }
}

