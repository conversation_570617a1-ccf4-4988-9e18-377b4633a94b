package mogames.gameData.flag.vo
{
   import com.mogames.utils.MathUtil;
   import mogames.gameData.flag.base.FlagVO;
   import mogames.gameData.vip.VipProxy;
   
   public class FlagZhuanVO extends FlagVO
   {
      
      public function FlagZhuanVO(param1:int)
      {
         super(param1,3,false,true,true);
      }
      
      override public function get total() : int
      {
         var _loc1_:int = MathUtil.loadINT(_total);
         if(VipProxy.instance().hasFunc(117))
         {
            _loc1_++;
         }
         if(VipProxy.instance().hasFunc(118))
         {
            _loc1_++;
         }
         return _loc1_;
      }
   }
}

