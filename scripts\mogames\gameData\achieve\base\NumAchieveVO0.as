package mogames.gameData.achieve.base
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   
   public class NumAchieveVO0 extends BaseAchieveVO
   {
      
      protected var _curNum:Oint = new Oint();
      
      protected var _maxNum:Oint = new Oint();
      
      public function NumAchieveVO0(param1:int, param2:int, param3:Array, param4:String, param5:String)
      {
         super(param1,param3,param4,param5);
         MathUtil.saveINT(this._maxNum,param2);
         MathUtil.saveINT(this._curNum,0);
      }
      
      override public function checkOpen(param1:int = 1) : void
      {
         if(this.isOpen)
         {
            return;
         }
         MathUtil.saveINT(this._curNum,this.curNum + param1);
         super.checkOpen();
      }
      
      override public function reset() : void
      {
         if(this.isOpen)
         {
            return;
         }
         MathUtil.saveINT(this._curNum,0);
      }
      
      override public function get isOpen() : <PERSON><PERSON>an
      {
         return this.curNum >= this.maxNum || isGet;
      }
      
      override public function get infor() : String
      {
         if(isGet)
         {
            return _infor;
         }
         return _infor + "（" + this.curNum + "/" + this.maxNum + "）";
      }
      
      private function get curNum() : int
      {
         return MathUtil.loadINT(this._curNum);
      }
      
      private function get maxNum() : int
      {
         return MathUtil.loadINT(this._maxNum);
      }
      
      override public function get saveData() : String
      {
         return [id,MathUtil.loadINT(_get)].join("H");
      }
      
      override public function set loadData(param1:Array) : void
      {
         if(!param1 || param1.length <= 0)
         {
            return;
         }
         MathUtil.saveINT(_get,int(param1[1]));
      }
   }
}

