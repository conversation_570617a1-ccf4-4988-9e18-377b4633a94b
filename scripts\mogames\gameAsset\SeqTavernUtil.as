package mogames.gameAsset
{
   import com.mogames.utils.ConvertUtil;
   import file.WalkerConfig1;
   
   public class SeqTavernUtil
   {
      
      private static var _skins:Array = ["_STAND","_MOVE","_DRINK","_SLEEP","_GETUP"];
      
      public function SeqTavernUtil()
      {
         super();
      }
      
      public static function newSeq(param1:int) : Array
      {
         var _loc4_:String = null;
         var _loc2_:Array = [];
         var _loc3_:Array = newSkins("TAVERN_WALKER" + param1);
         for each(_loc4_ in _loc3_)
         {
            _loc2_.push(ConvertUtil.convertMC(AssetManager.newMCRes(_loc4_)));
         }
         return _loc2_;
      }
      
      private static function newSkins(param1:String) : Array
      {
         var _loc3_:String = null;
         var _loc2_:Array = [];
         for each(_loc3_ in _skins)
         {
            _loc2_.push(param1 + _loc3_);
         }
         return _loc2_;
      }
      
      public static function convertAsset() : void
      {
         var _loc2_:int = 0;
         var _loc1_:Array = WalkerConfig1.instance().walkerList;
         for each(_loc2_ in _loc1_)
         {
            AssetManager.findTavernSeq(_loc2_);
         }
      }
   }
}

