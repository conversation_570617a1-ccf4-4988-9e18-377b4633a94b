package mogames.gameData.base.func
{
   public class TDTileVO
   {
      
      private var _row:int;
      
      private var _col:int;
      
      private var _maxRow:int;
      
      private var _maxCol:int;
      
      private var _dict:Array;
      
      private var _type:int;
      
      private var _selected:Boolean;
      
      public function TDTileVO(param1:int, param2:int)
      {
         super();
         this._row = param1;
         this._col = param2;
      }
      
      public function createDict(param1:int, param2:int, param3:Array) : void
      {
         this._maxRow = param1;
         this._maxCol = param2;
         this._dict = param3;
      }
      
      public function get row() : int
      {
         return this._row;
      }
      
      public function get col() : int
      {
         return this._col;
      }
      
      public function get top() : TDTileVO
      {
         var _loc1_:int = this._row - 1;
         if(_loc1_ < 0)
         {
            return null;
         }
         return this.findTile(_loc1_,this._col);
      }
      
      public function get btm() : TDTileVO
      {
         var _loc1_:int = this._row + 1;
         if(_loc1_ >= this._maxRow)
         {
            return null;
         }
         return this.findTile(_loc1_,this._col);
      }
      
      public function get right() : TDTileVO
      {
         var _loc1_:int = this._col + 1;
         if(_loc1_ >= this._maxCol)
         {
            return null;
         }
         return this.findTile(this._row,_loc1_);
      }
      
      public function get left() : TDTileVO
      {
         var _loc1_:int = this._col - 1;
         if(_loc1_ < 0)
         {
            return null;
         }
         return this.findTile(this._row,_loc1_);
      }
      
      public function get aroundNum() : int
      {
         var _loc1_:int = 0;
         var _loc2_:TDTileVO = this.top;
         if(Boolean(_loc2_) && _loc2_.selected)
         {
            _loc1_++;
         }
         _loc2_ = this.btm;
         if(Boolean(_loc2_) && _loc2_.selected)
         {
            _loc1_++;
         }
         _loc2_ = this.right;
         if(Boolean(_loc2_) && _loc2_.selected)
         {
            _loc1_++;
         }
         _loc2_ = this.left;
         if(Boolean(_loc2_) && _loc2_.selected)
         {
            _loc1_++;
         }
         return _loc1_;
      }
      
      public function get tileType() : int
      {
         return this._type;
      }
      
      public function set tileType(param1:int) : void
      {
         this._type = param1;
      }
      
      public function set selected(param1:Boolean) : void
      {
         this._selected = param1;
      }
      
      public function get selected() : Boolean
      {
         return this._selected;
      }
      
      private function findTile(param1:int, param2:int) : TDTileVO
      {
         var _loc3_:TDTileVO = null;
         for each(_loc3_ in this._dict)
         {
            if(_loc3_.row == param1 && _loc3_.col == param2)
            {
               return _loc3_;
            }
         }
         return null;
      }
   }
}

