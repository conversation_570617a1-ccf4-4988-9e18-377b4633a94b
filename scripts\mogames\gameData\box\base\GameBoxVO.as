package mogames.gameData.box.base
{
   import com.mogames.data.Onum;
   import com.mogames.sound.SoundManager;
   import com.mogames.utils.MathUtil;
   import com.mogames.utils.ObjUtil;
   import com.mogames.utils.TxtUtil;
   import file.BoxConfig;
   
   public class GameBoxVO
   {
      
      private var _time:Onum = new Onum();
      
      private var _baseArg:Object;
      
      private var _skin:BoxSkinVO;
      
      public function GameBoxVO(param1:Number, param2:int, param3:Object)
      {
         super();
         MathUtil.saveNUM(this._time,param1);
         this._skin = BoxConfig.instance().findSkin(param2);
         this._baseArg = ObjUtil.saveObj(param3);
      }
      
      public function handlerOpen() : String
      {
         SoundManager.instance().playAudio("AUDIO_FAIL");
         return TxtUtil.setColor("大爷，是空的！","FFFF00");
      }
      
      public function get time() : Number
      {
         return MathUtil.loadNUM(this._time);
      }
      
      public function findArg(param1:String) : Number
      {
         return MathUtil.loadNUM(this._baseArg[param1]);
      }
      
      public function get skinVO() : BoxSkinVO
      {
         return this._skin;
      }
   }
}

