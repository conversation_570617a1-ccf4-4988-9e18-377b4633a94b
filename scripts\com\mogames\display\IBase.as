package com.mogames.display
{
   import flash.geom.Point;
   
   public interface IBase extends IDestroy
   {
      
      function get location() : Point;
      
      function get x() : Number;
      
      function set x(param1:Number) : void;
      
      function set y(param1:Number) : void;
      
      function get y() : Number;
      
      function get width() : Number;
      
      function get height() : Number;
   }
}

