package mogames.gameData.good
{
   import mogames.gameData.good.vo.GameGoodVO;
   
   public class GoodUtil
   {
      
      public function GoodUtil()
      {
         super();
      }
      
      public static function addGoodToList(param1:Array, param2:GameGoodVO) : void
      {
         if(!param2.constGood.isPile)
         {
            param1[param1.length] = param2;
            return;
         }
         var _loc3_:GameGoodVO = checkSameGood(param1,param2.goodID);
         if(_loc3_)
         {
            _loc3_.amount += param2.amount;
         }
         else
         {
            param1[param1.length] = param2;
         }
      }
      
      public static function checkSameGood(param1:Array, param2:int) : GameGoodVO
      {
         var _loc3_:GameGoodVO = null;
         for each(_loc3_ in param1)
         {
            if(_loc3_.goodID == param2)
            {
               return _loc3_;
            }
         }
         return null;
      }
   }
}

