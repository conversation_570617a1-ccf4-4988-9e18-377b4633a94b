package mogames.gameData.base.vo
{
   import file.GoodConfig;
   import mogames.gameData.depot.DepotProxy;
   import mogames.gameData.game.LackVO;
   
   public class ZhiBaoNeedVO extends NeedVO
   {
      
      public function ZhiBaoNeedVO(param1:int)
      {
         super(param1,1);
      }
      
      override protected function create(param1:int, param2:int) : void
      {
         _needGood = GoodConfig.instance().newGameEquip(param1,3);
         _needGood.amount = 1;
      }
      
      override public function get lackVO() : LackVO
      {
         if(DepotProxy.instance().findNum(needID) == 0)
         {
            return new LackVO("国库中未找到【" + needName + "】");
         }
         return null;
      }
   }
}

