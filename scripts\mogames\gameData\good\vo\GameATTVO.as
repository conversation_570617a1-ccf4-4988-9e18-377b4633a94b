package mogames.gameData.good.vo
{
   import mogames.gameData.good.base.ConstATTVO;
   import mogames.gameData.good.base.ConstGoodVO;
   
   public class GameATTVO extends GameGoodVO
   {
      
      private var _constATT:ConstATTVO;
      
      public function GameATTVO(param1:ConstATTVO)
      {
         super(param1 as ConstGoodVO);
         this._constATT = param1;
      }
      
      public function addEquipATT(param1:Object) : void
      {
         if(this._constATT.attATK)
         {
            if(this._constATT.attATK.isPer)
            {
               param1.perATK += this._constATT.attATK.value;
            }
            else
            {
               param1.extraATK += this._constATT.attATK.value;
            }
         }
         if(this._constATT.attWIT)
         {
            if(this._constATT.attWIT.isPer)
            {
               param1.perWIT += this._constATT.attWIT.value;
            }
            else
            {
               param1.extraWIT += this._constATT.attWIT.value;
            }
         }
         if(this._constATT.attHP)
         {
            if(this._constATT.attHP.isPer)
            {
               param1.perHP += this._constATT.attHP.value;
            }
            else
            {
               param1.extraHP += this._constATT.attHP.value;
            }
         }
         if(this._constATT.attDEF)
         {
            if(this._constATT.attDEF.isPer)
            {
               param1.perDEF += this._constATT.attDEF.value;
            }
            else
            {
               param1.extraDEF += this._constATT.attDEF.value;
            }
         }
         if(this._constATT.attMISS)
         {
            param1.extraMISS += this._constATT.attMISS.value;
         }
         if(this._constATT.attCRIT)
         {
            param1.extraCRIT += this._constATT.attCRIT.value;
         }
         if(this._constATT.attBEI)
         {
            param1.extraBEI += this._constATT.attBEI.value;
         }
         if(this._constATT.attSPD)
         {
            param1.extraSPD += this._constATT.attSPD.value;
         }
      }
      
      public function get constATT() : ConstATTVO
      {
         return this._constATT;
      }
   }
}

