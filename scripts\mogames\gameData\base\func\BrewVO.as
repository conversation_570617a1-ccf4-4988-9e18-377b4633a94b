package mogames.gameData.base.func
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import mogames.gameData.base.RewardProxy;
   import mogames.gameData.base.UseProxy;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.base.vo.NeedVO;
   import mogames.gameData.game.LackVO;
   import mogames.gameData.master.MasterProxy;
   
   public class BrewVO
   {
      
      private var _source:NeedVO;
      
      private var _reward:BaseRewardVO;
      
      private var _gold:Oint = new Oint();
      
      private var _yin:Oint = new Oint();
      
      private var _list:Array;
      
      public function BrewVO(param1:NeedVO, param2:BaseRewardVO, param3:int, param4:int, param5:Array)
      {
         super();
         this._source = param1;
         this._reward = param2;
         MathUtil.saveINT(this._gold,param3);
         MathUtil.saveINT(this._yin,param4);
         this._list = param5;
      }
      
      public function checkLack() : LackVO
      {
         var _loc1_:LackVO = null;
         _loc1_ = MasterProxy.instance().checkValue(10000,this.gold);
         if(_loc1_ != null)
         {
            return _loc1_;
         }
         _loc1_ = MasterProxy.instance().checkValue(10006,this.yin);
         if(_loc1_ != null)
         {
            return _loc1_;
         }
         _loc1_ = UseProxy.instance().checkLack(this._list.concat(this._source));
         if(_loc1_ != null)
         {
            return _loc1_;
         }
         return null;
      }
      
      public function handlerBrew() : void
      {
         MasterProxy.instance().changeValue(10000,-this.gold);
         MasterProxy.instance().changeValue(10006,-this.yin);
         UseProxy.instance().useStuff(this._list.concat(this._source),null);
         RewardProxy.instance().addGiftReward([this._reward]);
      }
      
      public function get source() : NeedVO
      {
         return this._source;
      }
      
      public function get reward() : BaseRewardVO
      {
         return this._reward;
      }
      
      public function get gold() : int
      {
         return MathUtil.loadINT(this._gold);
      }
      
      public function get yin() : int
      {
         return MathUtil.loadINT(this._yin);
      }
      
      public function get needList() : Array
      {
         return this._list;
      }
   }
}

