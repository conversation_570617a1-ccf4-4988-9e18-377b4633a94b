package mogames.gameData.box.vo
{
   import com.mogames.sound.SoundManager;
   import com.mogames.utils.MathUtil;
   import com.mogames.utils.TxtUtil;
   import mogames.gameData.box.base.GameBoxVO;
   import mogames.gameData.mission.BattleProxy;
   
   public class ResBoxVO extends GameBoxVO
   {
      
      public function ResBoxVO(param1:Number, param2:int, param3:Object)
      {
         super(param1,param2,param3);
      }
      
      override public function handlerOpen() : String
      {
         var _loc1_:int = int(MathUtil.checkOdds(500));
         var _loc2_:int = MathUtil.randomNum(findArg("min"),findArg("max"));
         if(_loc1_ == 0)
         {
            BattleProxy.instance().dataRes.changeWood(_loc2_);
         }
         else
         {
            BattleProxy.instance().dataRes.changeFood(_loc2_);
         }
         SoundManager.instance().playAudio("AUDIO_REWARD");
         if(_loc1_ == 0)
         {
            return TxtUtil.setColor("获得木头X" + _loc2_,"99FF00");
         }
         return TxtUtil.setColor("获得食物X" + _loc2_,"99FF00");
      }
   }
}

