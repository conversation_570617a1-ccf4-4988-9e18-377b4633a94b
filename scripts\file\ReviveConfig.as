package file
{
   import mogames.ConstData;
   import mogames.gameData.base.vo.NeedVO;
   
   public class ReviveConfig
   {
      
      private static var _instance:ReviveConfig;
      
      private var _needs:Array;
      
      public function ReviveConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : ReviveConfig
      {
         if(!_instance)
         {
            _instance = new ReviveConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._needs = [];
         this._needs[this._needs.length] = [new NeedVO(10000,20000),new NeedVO(10110,12)];
         this._needs[this._needs.length] = [new NeedVO(10000,30000),new NeedVO(10110,12),new NeedVO(10111,16)];
         this._needs[this._needs.length] = [new NeedVO(10000,40000),new NeedVO(10111,16),new NeedVO(10112,20)];
         this._needs[this._needs.length] = [new NeedVO(10000,50000),new NeedVO(10112,20),new NeedVO(10113,24)];
      }
      
      public function findNeed(param1:int) : Array
      {
         if(param1 > ConstData.INT3.v)
         {
            return this._needs[3];
         }
         return this._needs[param1];
      }
   }
}

