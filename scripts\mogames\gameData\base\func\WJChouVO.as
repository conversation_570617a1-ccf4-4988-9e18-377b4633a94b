package mogames.gameData.base.func
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import file.RoleConfig;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.role.hero.HeroInfoVO;
   
   public class WJChouVO
   {
      
      private var _frame:int;
      
      private var _infor:HeroInfoVO;
      
      private var _rate:Oint = new Oint();
      
      private var _direct:Oint = new Oint();
      
      private var _rewardVO:BaseRewardVO;
      
      private var _index:Oint = new Oint();
      
      public function WJChouVO(param1:int, param2:int, param3:int, param4:int, param5:BaseRewardVO)
      {
         super();
         this._frame = param1;
         this._infor = RoleConfig.instance().findInfo(param2) as HeroInfoVO;
         MathUtil.saveINT(this._rate,param3);
         MathUtil.saveINT(this._direct,param4);
         this._rewardVO = param5;
      }
      
      public function get isOK() : <PERSON><PERSON>an
      {
         return MathUtil.checkOdds(MathUtil.loadINT(this._rate)) || this.curIndex >= MathUtil.loadINT(this._direct);
      }
      
      public function get frame() : int
      {
         return this._frame;
      }
      
      public function set curIndex(param1:int) : void
      {
         MathUtil.saveINT(this._index,param1);
      }
      
      public function get curIndex() : int
      {
         return MathUtil.loadINT(this._index);
      }
      
      public function get infor() : HeroInfoVO
      {
         return this._infor;
      }
      
      public function get rewardVO() : BaseRewardVO
      {
         return this._rewardVO;
      }
      
      public function startNew() : void
      {
         MathUtil.saveINT(this._index,0);
      }
      
      public function get saveData() : String
      {
         return this._infor.roleID + "H" + this.curIndex;
      }
      
      public function set loadData(param1:Array) : void
      {
         MathUtil.saveINT(this._index,param1[1]);
      }
   }
}

