package mogames.gameData.master.vo
{
   import com.mogames.data.Oint;
   import com.mogames.data.Onum;
   import com.mogames.utils.MathUtil;
   
   public class GatherVO
   {
      
      private var _trunkSPD:Onum = new Onum();
      
      private var _trunkNUM:Oint = new Oint();
      
      private var _fruitSPD:Onum = new Onum();
      
      private var _fruitNUM:Oint = new Oint();
      
      private var _beastSPD:Onum = new Onum();
      
      private var _beastNUM:Oint = new Oint();
      
      private var _mouseSPD:Onum = new Onum();
      
      private var _mouseATK:Oint = new Oint();
      
      public function GatherVO()
      {
         super();
         MathUtil.saveNUM(this._trunkSPD,0.25);
         MathUtil.saveINT(this._trunkNUM,10);
         MathUtil.saveNUM(this._fruitSPD,0.25);
         MathUtil.saveINT(this._fruitNUM,10);
         MathUtil.saveNUM(this._beastSPD,0.25);
         MathUtil.saveINT(this._beastNUM,6);
         MathUtil.saveNUM(this._mouseSPD,0.3);
         MathUtil.saveINT(this._mouseATK,28);
      }
      
      public function get trunkSPD() : Number
      {
         return MathUtil.loadNUM(this._trunkSPD);
      }
      
      public function get trunkNUM() : int
      {
         return MathUtil.loadINT(this._trunkNUM);
      }
      
      public function get fruitSPD() : Number
      {
         return MathUtil.loadNUM(this._fruitSPD);
      }
      
      public function get fruitNUM() : int
      {
         return MathUtil.loadINT(this._fruitNUM);
      }
      
      public function get beastSPD() : Number
      {
         return MathUtil.loadNUM(this._beastSPD);
      }
      
      public function get beastNUM() : int
      {
         return MathUtil.loadINT(this._beastNUM);
      }
      
      public function get mouseSPD() : Number
      {
         return MathUtil.loadNUM(this._mouseSPD);
      }
      
      public function get mouseATK() : int
      {
         return MathUtil.loadINT(this._mouseATK);
      }
   }
}

