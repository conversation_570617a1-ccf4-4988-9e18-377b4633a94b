package mogames.gameData.cheat
{
   import mogames.gameData.ServerProxy;
   import mogames.gameData.master.MasterProxy;
   import mogames.gameNet.MoneyProxy;
   import mogames.gameNet.SaveManager;
   import mogames.gameUI.tip.TipUtil;
   
   public class MoneyChecker
   {
      
      public function MoneyChecker()
      {
         super();
      }
      
      public function check(param1:Boolean = false) : void
      {
         this.checkMoney0();
         this.checkMoney1();
         if(param1 && Boolean(ServerProxy.instance().checkCode))
         {
            SaveManager.instance().saveAuto();
         }
         if(ServerProxy.instance().checkCode)
         {
            TipUtil.handlerLock();
         }
      }
      
      private function checkMoney0() : void
      {
         if(MoneyProxy.instance().fakeMoney == MoneyProxy.instance().money && MoneyProxy.instance().fakePay == MoneyProxy.instance().payNum && MoneyProxy.instance().fakeSpend == MoneyProxy.instance().spendNum)
         {
            return;
         }
         ServerProxy.instance().checkCode = 104;
         TipUtil.handlerLock();
      }
      
      private function checkMoney1() : void
      {
         if(MoneyProxy.instance().payNum == MasterProxy.instance().findValue(10011))
         {
            return;
         }
         if(MoneyProxy.instance().spendNum == MasterProxy.instance().findValue(10012))
         {
            return;
         }
         ServerProxy.instance().checkCode = 105;
         TipUtil.handlerLock();
      }
   }
}

