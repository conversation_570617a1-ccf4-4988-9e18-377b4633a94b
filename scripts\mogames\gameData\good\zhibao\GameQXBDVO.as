package mogames.gameData.good.zhibao
{
   import com.mogames.utils.MathUtil;
   import mogames.Layers;
   import mogames.gameData.good.base.ConstEquipVO;
   import mogames.gameData.good.equip.GameZhiBaoVO;
   import mogames.gameData.role.battle.HurtData;
   import mogames.gameEffect.EffectManager;
   import mogames.gameRole.base.IRole;
   
   public class GameQXBDVO extends GameZhiBaoVO
   {
      
      public function GameQXBDVO(param1:ConstEquipVO)
      {
         super(param1);
      }
      
      override public function handlerATK(param1:IRole) : void
      {
         if(!MathUtil.checkOdds(100) || param1.isDead)
         {
            return;
         }
         var _loc2_:HurtData = param1.roleVO.hurtData;
         param1.roleVO.changeHP(_loc2_.hurtValue * 0.5);
         EffectManager.addHeadWord("生命回复",param1.x,param1.y - param1.height);
         EffectManager.addBMC("SEQ_BUFF_XI_XUE_CLIP",Layers.frontLayer,param1.x,param1.y);
      }
   }
}

