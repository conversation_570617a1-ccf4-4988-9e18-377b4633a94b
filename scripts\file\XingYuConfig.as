package file
{
   import com.mogames.utils.MathUtil;
   import mogames.ConstData;
   import mogames.gameData.base.func.XYTowerVO;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.master.MasterProxy;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class XingYuConfig
   {
      
      private static var _instance:XingYuConfig;
      
      public var activeVO:XYTowerVO;
      
      public var openIndex:int;
      
      public var waves:Array;
      
      private var _args:Array;
      
      private var _list:Array;
      
      public function XingYuConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : XingYuConfig
      {
         if(!_instance)
         {
            _instance = new XingYuConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this.openIndex = 9;
         this._list = [];
         this._list[this._list.length] = new XYTowerVO(0,[new BaseRewardVO(17001,1),new BaseRewardVO(17002,1),new BaseRewardVO(17003,1),new BaseRewardVO(17004,1),new BaseRewardVO(17005,2),new BaseRewardVO(17006,2)]);
         this._list[this._list.length] = new XYTowerVO(1,[new BaseRewardVO(17011,1),new BaseRewardVO(17012,1),new BaseRewardVO(17013,1),new BaseRewardVO(17014,1),new BaseRewardVO(17015,2),new BaseRewardVO(17016,2)]);
         this._list[this._list.length] = new XYTowerVO(2,[new BaseRewardVO(17021,1),new BaseRewardVO(17022,1),new BaseRewardVO(17023,1),new BaseRewardVO(17024,1),new BaseRewardVO(17025,2),new BaseRewardVO(17026,2)]);
         this._list[this._list.length] = new XYTowerVO(3,[new BaseRewardVO(17031,1),new BaseRewardVO(17032,1),new BaseRewardVO(17033,1),new BaseRewardVO(17034,1),new BaseRewardVO(17035,2),new BaseRewardVO(17036,2)]);
         this._list[this._list.length] = new XYTowerVO(4,[new BaseRewardVO(17041,1),new BaseRewardVO(17042,1),new BaseRewardVO(17043,1),new BaseRewardVO(17044,1),new BaseRewardVO(17045,2),new BaseRewardVO(17046,2)]);
         this._list[this._list.length] = new XYTowerVO(5,[new BaseRewardVO(17051,1),new BaseRewardVO(17052,1),new BaseRewardVO(17053,1),new BaseRewardVO(17054,1),new BaseRewardVO(17055,2),new BaseRewardVO(17056,2)]);
         this._list[this._list.length] = new XYTowerVO(6,[new BaseRewardVO(17061,1),new BaseRewardVO(17062,1),new BaseRewardVO(17063,1),new BaseRewardVO(17064,1),new BaseRewardVO(17065,2),new BaseRewardVO(17066,2)]);
         this._list[this._list.length] = new XYTowerVO(7,[new BaseRewardVO(17071,1),new BaseRewardVO(17072,1),new BaseRewardVO(17073,1),new BaseRewardVO(17074,1),new BaseRewardVO(17075,2),new BaseRewardVO(17076,2)]);
         this._list[this._list.length] = new XYTowerVO(8,[new BaseRewardVO(17081,1),new BaseRewardVO(17082,1),new BaseRewardVO(17083,1),new BaseRewardVO(17084,1),new BaseRewardVO(17085,2),new BaseRewardVO(17086,2)]);
         this.waves = [];
         this.waves[this.waves.length] = {
            "time":2,
            "num":5
         };
         this.waves[this.waves.length] = {
            "time":15,
            "num":5
         };
         this.waves[this.waves.length] = {
            "time":15,
            "num":6
         };
         this.waves[this.waves.length] = {
            "time":15,
            "num":6
         };
         this.waves[this.waves.length] = {
            "time":20,
            "num":7
         };
         this.waves[this.waves.length] = {
            "time":20,
            "num":7
         };
         this.waves[this.waves.length] = {
            "time":20,
            "num":8
         };
         this.waves[this.waves.length] = {
            "time":25,
            "num":8
         };
         this.waves[this.waves.length] = {
            "time":25,
            "num":9
         };
         this.waves[this.waves.length] = {
            "time":25,
            "num":10
         };
         this._args = [];
         this._args[0] = [new BossArgVO(265,20000,1000,450,40,40,150,150,new BossSkillData0(100,{
            "hurt":3000,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,25000,1100,450,40,40,150,150,new BossSkillData0(100,{"hurt":2500},2),1012,0),new BossArgVO(269,25000,1200,450,40,40,150,150,new BossSkillData0(100,{
            "hurt":3500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(265,20000,1000,450,40,40,150,150,new BossSkillData0(100,{
            "hurt":3000,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,25000,1100,450,40,40,150,150,new BossSkillData0(100,{"hurt":2500},2),1012,0),new BossArgVO(269,20000,1200,450,40,40,150,150,new BossSkillData0(100,{
            "hurt":3500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(265,25000,1000,450,40,40,150,150,new BossSkillData0(100,{
            "hurt":3000,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,25000,1100,450,40,40,150,150,new BossSkillData0(100,{"hurt":2500},2),1012,0),new BossArgVO(269,20000,1200,450,40,40,150,150,new BossSkillData0(100,{
            "hurt":2500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(265,25000,1000,450,40,40,150,150,new BossSkillData0(100,{
            "hurt":3000,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,25000,1100,450,40,40,150,150,new BossSkillData0(100,{"hurt":2500},2),1012,0),new BossArgVO(269,20000,1200,450,40,40,150,150,new BossSkillData0(100,{
            "hurt":2500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(266,45000,1300,450,40,40,150,150,new BossSkillData0(100,{"hurt":3500},2),1018,0),new BossArgVO(268,45000,1400,550,70,70,200,150,new BossSkillData1(10,{
            "hurt":3000,
            "spdPer":80,
            "keepTime":5
         },3),1031,0),new BossArgVO(270,45000,1500,550,70,70,200,150,new BossSkillData1(11,{
            "hurt":4000,
            "roleNum":25,
            "keepTime":2
         },3),1036,0),new BossArgVO(271,45000,1600,550,70,70,200,150,new BossSkillData1(12,{
            "hurt":3000,
            "roleNum":20,
            "defPer":5,
            "keepTime":5
         },3),1039,0)];
         this._args[1] = [new BossArgVO(265,20000,1500,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":3500,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,25000,1600,550,40,40,150,150,new BossSkillData0(100,{"hurt":3000},2),1012,0),new BossArgVO(269,25000,1700,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":3500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(265,20000,1500,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":3000,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,25000,1600,550,40,40,150,150,new BossSkillData0(100,{"hurt":3500},2),1012,0),new BossArgVO(269,20000,1700,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":3500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(265,25000,1500,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":3000,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,25000,1600,550,40,40,150,150,new BossSkillData0(100,{"hurt":3500},2),1012,0),new BossArgVO(269,20000,1700,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":3500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(265,25000,1500,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":3000,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,25000,1600,550,40,40,150,150,new BossSkillData0(100,{"hurt":3500},2),1012,0),new BossArgVO(269,20000,1700,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":3500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(266,25000,1800,550,40,40,150,150,new BossSkillData0(100,{"hurt":3500},2),1018,0),new BossArgVO(268,45000,1900,650,70,70,200,150,new BossSkillData1(10,{
            "hurt":4000,
            "spdPer":80,
            "keepTime":5
         },3),1031,0),new BossArgVO(270,45000,2000,650,70,70,200,150,new BossSkillData1(11,{
            "hurt":5000,
            "roleNum":25,
            "keepTime":2
         },3),1036,0),new BossArgVO(271,45000,2100,650,70,70,200,150,new BossSkillData1(12,{
            "hurt":6000,
            "roleNum":20,
            "defPer":5,
            "keepTime":5
         },3),1039,0)];
         this._args[2] = [new BossArgVO(265,30000,1500,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":4500,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,35000,1600,550,40,40,150,150,new BossSkillData0(100,{"hurt":4000},2),1012,0),new BossArgVO(269,35000,1700,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":4500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(265,30000,1500,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":4000,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,35000,1600,550,40,40,150,150,new BossSkillData0(100,{"hurt":4500},2),1012,0),new BossArgVO(269,30000,1700,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":4500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(265,35000,1500,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":4000,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,35000,1600,550,40,40,150,150,new BossSkillData0(100,{"hurt":4500},2),1012,0),new BossArgVO(269,30000,1700,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":4500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(265,35000,1500,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":4000,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,35000,1600,550,40,40,150,150,new BossSkillData0(100,{"hurt":4500},2),1012,0),new BossArgVO(269,30000,1700,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":4500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(266,55000,1800,550,40,40,150,150,new BossSkillData0(100,{"hurt":4500},2),1018,0),new BossArgVO(268,55000,1900,650,70,70,200,150,new BossSkillData1(10,{
            "hurt":4000,
            "spdPer":80,
            "keepTime":5
         },3),1031,0),new BossArgVO(270,55000,2000,650,70,70,200,150,new BossSkillData1(11,{
            "hurt":5000,
            "roleNum":25,
            "keepTime":2
         },3),1036,0),new BossArgVO(271,55000,2100,650,70,70,200,150,new BossSkillData1(12,{
            "hurt":6000,
            "roleNum":20,
            "defPer":5,
            "keepTime":5
         },3),1039,0)];
         this._args[3] = [new BossArgVO(265,40000,2000,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":5500,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,45000,2200,550,40,40,150,150,new BossSkillData0(100,{"hurt":5000},2),1012,0),new BossArgVO(269,45000,2000,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":5500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(265,40000,2200,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":5000,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,45000,2000,550,40,40,150,150,new BossSkillData0(100,{"hurt":5500},2),1012,0),new BossArgVO(269,40000,2200,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":5500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(265,45000,2000,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":5000,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,45000,2200,550,40,40,150,150,new BossSkillData0(100,{"hurt":5500},2),1012,0),new BossArgVO(269,40000,2000,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":5500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(265,45000,2200,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":5000,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,45000,2000,550,40,40,150,150,new BossSkillData0(100,{"hurt":5500},2),1012,0),new BossArgVO(269,40000,2200,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":5500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(266,45000,2000,550,40,40,150,150,new BossSkillData0(100,{"hurt":5500},2),1018,0),new BossArgVO(268,65000,2900,650,70,70,200,150,new BossSkillData1(10,{
            "hurt":5000,
            "spdPer":80,
            "keepTime":5
         },3),1031,0),new BossArgVO(270,65000,3000,650,70,70,200,150,new BossSkillData1(11,{
            "hurt":6000,
            "roleNum":25,
            "keepTime":2
         },3),1036,0),new BossArgVO(271,65000,3100,650,70,70,200,150,new BossSkillData1(12,{
            "hurt":7000,
            "roleNum":20,
            "defPer":5,
            "keepTime":5
         },3),1039,0)];
         this._args[4] = [new BossArgVO(265,50000,2500,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":8500,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,55000,2600,550,40,40,150,150,new BossSkillData0(100,{"hurt":8000},2),1012,0),new BossArgVO(269,55000,2700,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":8500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(265,50000,2500,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":8000,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,55000,2600,550,40,40,150,150,new BossSkillData0(100,{"hurt":8500},2),1012,0),new BossArgVO(269,50000,2700,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":8500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(265,55000,2500,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":8000,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,55000,2600,550,40,40,150,150,new BossSkillData0(100,{"hurt":8500},2),1012,0),new BossArgVO(269,50000,2700,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":8500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(265,55000,2500,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":8000,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,55000,2600,550,40,40,150,150,new BossSkillData0(100,{"hurt":8500},2),1012,0),new BossArgVO(269,50000,2700,550,40,40,150,150,new BossSkillData0(100,{
            "hurt":8500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(266,55000,2800,550,40,40,150,150,new BossSkillData0(100,{"hurt":8500},2),1018,0),new BossArgVO(268,75000,5900,650,70,70,200,150,new BossSkillData1(10,{
            "hurt":9000,
            "spdPer":80,
            "keepTime":5
         },3),1031,0),new BossArgVO(270,75000,6000,650,70,70,200,150,new BossSkillData1(11,{
            "hurt":9000,
            "roleNum":25,
            "keepTime":2
         },3),1036,0),new BossArgVO(271,75000,5100,650,70,70,200,150,new BossSkillData1(12,{
            "hurt":9000,
            "roleNum":20,
            "defPer":5,
            "keepTime":5
         },3),1039,0)];
         this._args[5] = [new BossArgVO(265,60000,3500,1250,40,40,150,150,new BossSkillData0(100,{
            "hurt":15500,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,65000,3600,1250,40,40,150,150,new BossSkillData0(100,{"hurt":15000},2),1012,0),new BossArgVO(269,65000,3700,1250,40,40,150,150,new BossSkillData0(100,{
            "hurt":15500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(265,60000,3500,1250,40,40,150,150,new BossSkillData0(100,{
            "hurt":15000,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,65000,3600,1250,40,40,150,150,new BossSkillData0(100,{"hurt":15500},2),1012,0),new BossArgVO(269,60000,3700,1250,40,40,150,150,new BossSkillData0(100,{
            "hurt":15500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(265,65000,3500,1250,40,40,150,150,new BossSkillData0(100,{
            "hurt":15000,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,65000,3600,1250,40,40,150,150,new BossSkillData0(100,{"hurt":15500},2),1012,0),new BossArgVO(269,60000,3700,1250,40,40,150,150,new BossSkillData0(100,{
            "hurt":15500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(265,65000,3500,1250,40,40,150,150,new BossSkillData0(100,{
            "hurt":15000,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,65000,3600,1250,40,40,150,150,new BossSkillData0(100,{"hurt":15500},2),1012,0),new BossArgVO(269,60000,3700,1250,40,40,150,150,new BossSkillData0(100,{
            "hurt":15500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(266,65000,3800,1250,40,40,150,150,new BossSkillData0(100,{"hurt":15500},2),1018,0),new BossArgVO(268,100000,7900,1650,70,70,200,150,new BossSkillData1(10,{
            "hurt":20000,
            "spdPer":80,
            "keepTime":5
         },3),1031,0),new BossArgVO(270,100000,8000,1650,70,70,200,150,new BossSkillData1(11,{
            "hurt":20000,
            "roleNum":25,
            "keepTime":2
         },3),1036,0),new BossArgVO(271,100000,7100,1650,70,70,200,150,new BossSkillData1(12,{
            "hurt":20000,
            "roleNum":20,
            "defPer":5,
            "keepTime":5
         },3),1039,0)];
         this._args[6] = [new BossArgVO(265,90000,5500,1250,40,40,150,150,new BossSkillData0(100,{
            "hurt":25500,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,95000,5600,1250,40,40,150,150,new BossSkillData0(100,{"hurt":25000},2),1012,0),new BossArgVO(269,95000,5700,1250,40,40,150,150,new BossSkillData0(100,{
            "hurt":25500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(265,90000,5500,1250,40,40,150,150,new BossSkillData0(100,{
            "hurt":25000,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,95000,5600,1250,40,40,150,150,new BossSkillData0(100,{"hurt":25500},2),1012,0),new BossArgVO(269,90000,5700,1250,40,40,150,150,new BossSkillData0(100,{
            "hurt":25500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(265,95000,5500,1250,40,40,150,150,new BossSkillData0(100,{
            "hurt":25000,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,95000,5600,1250,40,40,150,150,new BossSkillData0(100,{"hurt":25500},2),1012,0),new BossArgVO(269,90000,5700,1250,40,40,150,150,new BossSkillData0(100,{
            "hurt":25500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(265,95000,5500,1250,40,40,150,150,new BossSkillData0(100,{
            "hurt":25000,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,95000,5600,1250,40,40,150,150,new BossSkillData0(100,{"hurt":25500},2),1012,0),new BossArgVO(269,90000,5700,1250,40,40,150,150,new BossSkillData0(100,{
            "hurt":25500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(266,95000,5800,1250,40,40,150,150,new BossSkillData0(100,{"hurt":25500},2),1018,0),new BossArgVO(268,150000,8900,1650,70,70,200,150,new BossSkillData1(10,{
            "hurt":25000,
            "spdPer":80,
            "keepTime":5
         },3),1031,0),new BossArgVO(270,150000,9000,1650,70,70,200,150,new BossSkillData1(11,{
            "hurt":25000,
            "roleNum":25,
            "keepTime":2
         },3),1036,0),new BossArgVO(271,150000,8100,1650,70,70,200,150,new BossSkillData1(12,{
            "hurt":25000,
            "roleNum":20,
            "defPer":5,
            "keepTime":5
         },3),1039,0)];
         this._args[7] = [new BossArgVO(265,120000,7500,1250,40,40,150,150,new BossSkillData0(100,{
            "hurt":35500,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,125000,7600,1250,40,40,150,150,new BossSkillData0(100,{"hurt":35000},2),1012,0),new BossArgVO(269,125000,7700,1250,40,40,150,150,new BossSkillData0(100,{
            "hurt":35500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(265,120000,7500,1250,40,40,150,150,new BossSkillData0(100,{
            "hurt":35000,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,125000,7600,1250,40,40,150,150,new BossSkillData0(100,{"hurt":35500},2),1012,0),new BossArgVO(269,120000,7700,1250,40,40,150,150,new BossSkillData0(100,{
            "hurt":35500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(265,125000,7500,1250,40,40,150,150,new BossSkillData0(100,{
            "hurt":35000,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,125000,7600,1250,40,40,150,150,new BossSkillData0(100,{"hurt":35500},2),1012,0),new BossArgVO(269,120000,7700,1250,40,40,150,150,new BossSkillData0(100,{
            "hurt":35500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(265,125000,7500,1250,40,40,150,150,new BossSkillData0(100,{
            "hurt":35000,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,125000,7600,1250,40,40,150,150,new BossSkillData0(100,{"hurt":35500},2),1012,0),new BossArgVO(269,125000,7700,1250,40,40,150,150,new BossSkillData0(100,{
            "hurt":35500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(266,125000,7800,1250,40,40,150,150,new BossSkillData0(100,{"hurt":35500},2),1018,0),new BossArgVO(268,200000,9900,1650,70,70,200,150,new BossSkillData1(10,{
            "hurt":45000,
            "spdPer":80,
            "keepTime":5
         },10),1031,0),new BossArgVO(270,200000,10000,1650,70,70,200,150,new BossSkillData1(11,{
            "hurt":45000,
            "roleNum":25,
            "keepTime":2
         },10),1036,0),new BossArgVO(271,200000,9100,1650,70,70,200,150,new BossSkillData1(12,{
            "hurt":45000,
            "roleNum":20,
            "defPer":5,
            "keepTime":5
         },10),1039,0)];
         this._args[8] = [new BossArgVO(265,150000,9500,2250,40,40,150,150,new BossSkillData0(100,{
            "hurt":45500,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,150000,9600,2250,40,40,150,150,new BossSkillData0(100,{"hurt":45000},2),1012,0),new BossArgVO(269,150000,9700,2250,40,40,150,150,new BossSkillData0(100,{
            "hurt":45500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(265,150000,9500,2250,40,40,150,150,new BossSkillData0(100,{
            "hurt":45000,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,150000,9600,2250,40,40,150,150,new BossSkillData0(100,{"hurt":45500},2),1012,0),new BossArgVO(269,150000,9700,2250,40,40,150,150,new BossSkillData0(100,{
            "hurt":45500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(265,150000,9500,2250,40,40,150,150,new BossSkillData0(100,{
            "hurt":45000,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,150000,9600,2250,40,40,150,150,new BossSkillData0(100,{"hurt":45500},2),1012,0),new BossArgVO(269,150000,9700,2250,40,40,150,150,new BossSkillData0(100,{
            "hurt":45500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(265,150000,9500,2250,40,40,150,150,new BossSkillData0(100,{
            "hurt":45000,
            "roleNum":4
         },2),1004,0),new BossArgVO(267,150000,9600,2250,40,40,150,150,new BossSkillData0(100,{"hurt":45500},2),1012,0),new BossArgVO(269,150000,9700,2250,40,40,150,150,new BossSkillData0(100,{
            "hurt":45500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(266,150000,9800,2250,40,40,150,150,new BossSkillData0(100,{"hurt":45500},2),1018,0),new BossArgVO(268,250000,11900,2650,70,70,200,150,new BossSkillData1(10,{
            "hurt":65000,
            "spdPer":80,
            "keepTime":5
         },10),1031,0),new BossArgVO(270,250000,11000,2650,70,70,200,150,new BossSkillData1(11,{
            "hurt":65000,
            "roleNum":25,
            "keepTime":2
         },10),1036,0),new BossArgVO(271,250000,11100,2650,70,70,200,150,new BossSkillData1(12,{
            "hurt":65000,
            "roleNum":20,
            "defPer":5,
            "keepTime":5
         },10),1039,0)];
      }
      
      public function newWaves(param1:int) : Array
      {
         var _loc5_:int = 0;
         var _loc2_:int = int(this.waves[param1].num);
         var _loc3_:Array = this._args[this.activeVO.index].slice();
         var _loc4_:Array = [];
         var _loc6_:int = 0;
         while(_loc6_ < _loc2_)
         {
            _loc5_ = Math.random() * _loc3_.length;
            _loc4_[_loc6_] = _loc3_[_loc5_];
            _loc3_.splice(_loc5_,1);
            _loc6_++;
         }
         return _loc4_;
      }
      
      public function get list() : Array
      {
         return this._list;
      }
      
      public function newReward(param1:Boolean) : Array
      {
         if(!param1)
         {
            return this.loseReward;
         }
         return this.winReward;
      }
      
      public function get enterTip() : String
      {
         if(this.isOpen)
         {
            return "";
         }
         return "开启条件：主公等级达到80级";
      }
      
      public function get isOpen() : Boolean
      {
         if(Sanx.isLocal)
         {
            return true;
         }
         return MasterProxy.instance().masterVO.level >= 80;
      }
      
      private function get winReward() : Array
      {
         var _loc4_:int = 0;
         var _loc1_:Array = [];
         var _loc2_:int = ConstData.INT2.v;
         if(MathUtil.checkOdds(600))
         {
            _loc2_ = ConstData.INT3.v;
         }
         var _loc3_:Array = this.activeVO.rewards.slice();
         var _loc5_:int = 0;
         while(_loc5_ < _loc2_)
         {
            _loc4_ = Math.random() * _loc3_.length;
            _loc1_[_loc1_.length] = _loc3_[_loc4_];
            _loc3_.splice(_loc4_,1);
            _loc5_++;
         }
         return _loc1_;
      }
      
      private function get loseReward() : Array
      {
         var _loc1_:int = Math.random() * this.activeVO.rewards.length;
         return [this.activeVO.rewards[_loc1_]];
      }
   }
}

