package mogames.gameData.box.vo
{
   import com.mogames.sound.SoundManager;
   import com.mogames.utils.TxtUtil;
   import mogames.gameData.box.base.GameBoxVO;
   import mogames.gameData.mission.BattleProxy;
   
   public class MPBoxVO extends GameBoxVO
   {
      
      public function MPBoxVO(param1:Number, param2:int, param3:Object)
      {
         super(param1,param2,param3);
      }
      
      override public function handlerOpen() : String
      {
         var _loc1_:int = findArg("mpNum");
         BattleProxy.instance().dataSkill.changeMP(_loc1_);
         SoundManager.instance().playAudio("AUDIO_REWARD");
         return TxtUtil.setColor("法力回复" + _loc1_ + "点！","33CCFF");
      }
   }
}

