package file
{
   import flash.utils.Dictionary;
   import mogames.gameData.trick.HeroTrickVO;
   
   public class TrickConfig
   {
      
      private static var _instance:TrickConfig;
      
      private var _results:Dictionary;
      
      private var _heros:Array;
      
      private var _default:HeroTrickVO;
      
      public function TrickConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : TrickConfig
      {
         if(!_instance)
         {
            _instance = new TrickConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._results = new Dictionary();
         this._results["TRICK1"] = [0,-1,1];
         this._results["TRICK2"] = [1,0,-1];
         this._results["TRICK3"] = [-1,1,0];
         this._default = new HeroTrickVO(0,6,6,6);
         this._heros = [];
         this._heros[this._heros.length] = new HeroTrickVO(300,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(301,3,3,2);
         this._heros[this._heros.length] = new HeroTrickVO(302,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(303,2,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(304,3,5,3);
         this._heros[this._heros.length] = new HeroTrickVO(305,3,3,2);
         this._heros[this._heros.length] = new HeroTrickVO(306,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(307,2,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(308,4,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(309,3,3,2);
         this._heros[this._heros.length] = new HeroTrickVO(310,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(311,2,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(312,3,3,5);
         this._heros[this._heros.length] = new HeroTrickVO(313,3,3,2);
         this._heros[this._heros.length] = new HeroTrickVO(314,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(315,2,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(316,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(317,3,3,2);
         this._heros[this._heros.length] = new HeroTrickVO(318,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(319,5,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(320,2,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(321,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(322,3,3,2);
         this._heros[this._heros.length] = new HeroTrickVO(323,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(324,2,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(325,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(326,3,3,2);
         this._heros[this._heros.length] = new HeroTrickVO(327,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(328,2,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(329,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(330,3,3,2);
         this._heros[this._heros.length] = new HeroTrickVO(331,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(332,2,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(333,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(334,3,3,2);
         this._heros[this._heros.length] = new HeroTrickVO(335,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(336,2,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(337,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(338,3,3,2);
         this._heros[this._heros.length] = new HeroTrickVO(340,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(341,3,4,3);
         this._heros[this._heros.length] = new HeroTrickVO(342,2,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(343,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(344,3,3,2);
         this._heros[this._heros.length] = new HeroTrickVO(345,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(346,2,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(347,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(348,3,3,2);
         this._heros[this._heros.length] = new HeroTrickVO(349,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(350,2,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(351,3,3,4);
         this._heros[this._heros.length] = new HeroTrickVO(352,3,4,3);
         this._heros[this._heros.length] = new HeroTrickVO(353,4,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(354,3,4,3);
         this._heros[this._heros.length] = new HeroTrickVO(355,3,3,4);
         this._heros[this._heros.length] = new HeroTrickVO(356,5,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(357,3,5,3);
         this._heros[this._heros.length] = new HeroTrickVO(358,3,3,5);
         this._heros[this._heros.length] = new HeroTrickVO(359,3,5,3);
         this._heros[this._heros.length] = new HeroTrickVO(360,5,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(361,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(362,3,3,2);
         this._heros[this._heros.length] = new HeroTrickVO(363,3,4,3);
         this._heros[this._heros.length] = new HeroTrickVO(364,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(365,2,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(366,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(367,3,3,2);
         this._heros[this._heros.length] = new HeroTrickVO(368,3,5,3);
         this._heros[this._heros.length] = new HeroTrickVO(369,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(370,2,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(371,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(372,4,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(373,3,3,2);
         this._heros[this._heros.length] = new HeroTrickVO(374,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(375,3,4,3);
         this._heros[this._heros.length] = new HeroTrickVO(376,2,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(377,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(378,3,3,5);
         this._heros[this._heros.length] = new HeroTrickVO(379,3,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(380,3,3,4);
         this._heros[this._heros.length] = new HeroTrickVO(381,3,3,2);
         this._heros[this._heros.length] = new HeroTrickVO(382,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(383,2,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(384,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(385,3,3,2);
         this._heros[this._heros.length] = new HeroTrickVO(386,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(387,2,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(388,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(389,3,3,2);
         this._heros[this._heros.length] = new HeroTrickVO(390,5,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(391,3,5,3);
         this._heros[this._heros.length] = new HeroTrickVO(392,3,3,5);
         this._heros[this._heros.length] = new HeroTrickVO(393,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(394,2,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(395,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(396,3,4,3);
         this._heros[this._heros.length] = new HeroTrickVO(397,3,3,2);
         this._heros[this._heros.length] = new HeroTrickVO(398,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(399,2,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(400,4,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(401,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(402,3,3,2);
         this._heros[this._heros.length] = new HeroTrickVO(403,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(404,2,3,5);
         this._heros[this._heros.length] = new HeroTrickVO(405,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(406,3,3,2);
         this._heros[this._heros.length] = new HeroTrickVO(407,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(408,3,3,4);
         this._heros[this._heros.length] = new HeroTrickVO(409,2,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(410,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(411,3,3,2);
         this._heros[this._heros.length] = new HeroTrickVO(412,3,5,3);
         this._heros[this._heros.length] = new HeroTrickVO(413,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(414,2,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(415,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(416,3,3,2);
         this._heros[this._heros.length] = new HeroTrickVO(417,3,4,3);
         this._heros[this._heros.length] = new HeroTrickVO(418,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(419,2,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(420,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(421,3,3,2);
         this._heros[this._heros.length] = new HeroTrickVO(422,5,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(423,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(424,2,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(425,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(426,3,3,2);
         this._heros[this._heros.length] = new HeroTrickVO(427,4,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(428,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(429,2,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(430,3,2,3);
         this._heros[this._heros.length] = new HeroTrickVO(431,3,3,2);
         this._heros[this._heros.length] = new HeroTrickVO(432,5,3,3);
         this._heros[this._heros.length] = new HeroTrickVO(433,3,5,3);
      }
      
      public function countResult(param1:int, param2:int) : int
      {
         return this._results["TRICK" + param1][param2 - 1];
      }
      
      public function findTrickVO(param1:int) : HeroTrickVO
      {
         var _loc2_:HeroTrickVO = null;
         for each(_loc2_ in this._heros)
         {
            if(_loc2_.heroID == param1)
            {
               return _loc2_;
            }
         }
         return this._default;
      }
   }
}

