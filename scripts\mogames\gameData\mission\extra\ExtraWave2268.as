package mogames.gameData.mission.extra
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ExtraWave2268
   {
      
      public function ExtraWave2268()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2268);
         _loc1_.limitBR = new WaveLimitVO(99999999,0.85,0.85);
         _loc1_.zhuBoss = new BossArgVO(761,4500000,40000,5000,80,80,300,90,new BossSkillData0(150,{
            "hurt":56200,
            "hurtCount":5
         },5),1002,0);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(751,200000,10000,100,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(751,200000,10000,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(751,200000,10000,100,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(751,200000,10000,100,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(761,850000,15600,450,50,80,150,80,new BossSkillData1(10,{"hurt":38450},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(17,new RoleArgVO(751,200000,10000,100,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(761,850000,15600,450,50,80,150,80,new BossSkillData1(12,{"hurt":38450},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(751,200000,10000,100,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(751,200000,10000,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(751,200000,10000,100,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(751,200000,10000,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(751,200000,10000,100,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(751,200000,10000,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(751,200000,10000,100,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(761,850000,15600,450,50,80,150,80,new BossSkillData1(11,{"hurt":38450},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(751,200000,10000,100,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(751,200000,10000,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(751,200000,10000,100,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(751,200000,10000,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(17,new RoleArgVO(751,200000,10000,100,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(761,850000,15600,450,50,80,150,80,new BossSkillData1(12,{"hurt":38450},2),1011,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

