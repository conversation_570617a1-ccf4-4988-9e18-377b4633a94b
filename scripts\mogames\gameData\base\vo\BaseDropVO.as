package mogames.gameData.base.vo
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   
   public class BaseDropVO
   {
      
      protected var _rate:Oint = new Oint();
      
      protected var _rewardVO:BaseRewardVO;
      
      public function BaseDropVO(param1:int, param2:BaseRewardVO)
      {
         super();
         MathUtil.saveINT(this._rate,param1);
         this._rewardVO = param2;
      }
      
      public function get isDrop() : Boolean
      {
         return MathUtil.checkOdds(this.rate);
      }
      
      public function get rewardVO() : BaseRewardVO
      {
         return this._rewardVO;
      }
      
      public function get rate() : int
      {
         return MathUtil.loadINT(this._rate);
      }
   }
}

