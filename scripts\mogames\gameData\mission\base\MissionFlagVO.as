package mogames.gameData.mission.base
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import mogames.gameData.town.TownProxy;
   
   public class MissionFlagVO
   {
      
      private var _constVO:MissionConstVO;
      
      private var _finish:Oint = new Oint();
      
      private var _star:Oint = new Oint();
      
      public function MissionFlagVO(param1:MissionConstVO)
      {
         super();
         this._constVO = param1;
         MathUtil.saveINT(this._finish,0);
         MathUtil.saveINT(this._star,0);
      }
      
      public function setFinish() : void
      {
         MathUtil.saveINT(this._finish,1);
         TownProxy.instance().addTown(this._constVO.mid);
      }
      
      public function get isFinish() : Boolean
      {
         return MathUtil.loadINT(this._finish) == 1;
      }
      
      public function get isExtraFinish() : Boolean
      {
         return this.starNum > 0;
      }
      
      public function get starNum() : int
      {
         return MathUtil.loadINT(this._star);
      }
      
      public function setStar(param1:int) : void
      {
         if(param1 <= this.starNum)
         {
            return;
         }
         MathUtil.saveINT(this._star,param1);
      }
      
      public function get mid() : int
      {
         return this._constVO.mid;
      }
      
      public function get constVO() : MissionConstVO
      {
         return this._constVO;
      }
      
      public function setFinishValue(param1:int) : void
      {
         MathUtil.saveINT(this._finish,param1);
      }
      
      public function get saveData() : String
      {
         return [this.mid,MathUtil.loadINT(this._finish),this.starNum].join("H");
      }
      
      public function set loadData(param1:Array) : void
      {
         MathUtil.saveINT(this._finish,param1[1]);
         MathUtil.saveINT(this._star,param1[2]);
      }
   }
}

