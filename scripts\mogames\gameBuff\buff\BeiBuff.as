package mogames.gameBuff.buff
{
   import mogames.gameBuff.base.TimeRoleBuff;
   import mogames.gameData.buff.ConstBuffVO;
   
   public class BeiBuff extends TimeRoleBuff
   {
      
      private var _value:int;
      
      public function BeiBuff(param1:ConstBuffVO)
      {
         super(param1);
      }
      
      override protected function createData() : void
      {
         this._value = _buffVO.args.value;
         _owner.roleVO.skillBEI += this._value;
         _owner.roleVO.updateBEI();
      }
      
      override protected function onCleanRole() : void
      {
         _owner.roleVO.skillBEI -= this._value;
         _owner.roleVO.updateBEI();
      }
   }
}

