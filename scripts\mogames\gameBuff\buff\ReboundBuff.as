package mogames.gameBuff.buff
{
   import mogames.gameBuff.base.TimeRoleBuff;
   import mogames.gameData.buff.ConstBuffVO;
   import mogames.gameData.role.battle.HurtData;
   import mogames.gameRole.base.IRole;
   
   public class ReboundBuff extends TimeRoleBuff
   {
      
      private var _hurtData:HurtData;
      
      public function ReboundBuff(param1:ConstBuffVO)
      {
         super(param1);
         this._hurtData = new HurtData();
      }
      
      override protected function createData() : void
      {
         _owner.signal.add(this.listenHurt);
      }
      
      override protected function onCleanRole() : void
      {
         super.onCleanRole();
         this._hurtData.destroy();
         this._hurtData = null;
         _owner.signal.remove(this.listenHurt);
      }
      
      private function listenHurt(param1:Object) : void
      {
         if(param1.type != "ROLE_HURT")
         {
            return;
         }
         var _loc2_:HurtData = param1.hurtData;
         var _loc3_:IRole = _loc2_.source;
         if(!_loc2_.isSkill || !_loc3_ || _loc3_.isDead)
         {
            return;
         }
         this._hurtData.createHurt(param1.hurtValue * _buffVO.args.per * 0.01,true,false);
         _loc3_.setHurt(this._hurtData,"SEQ_MINI_BOOM_CLIP","AUDIO_NIU_HURT");
      }
   }
}

