package mogames.gameData.mission.extra
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ExtraWave2235
   {
      
      public function ExtraWave2235()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2235);
         _loc1_.limitBR = new WaveLimitVO(99999999,0.8,0.8);
         _loc1_.zhuBoss = new BossArgVO(763,3100000,13000,1800,80,80,300,90,new BossSkillData0(150,{
            "hurt":36000,
            "keepTime":8,
            "hurtCount":6
         },3),1015,0);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(753,80000,4250,120,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(752,80000,4250,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(751,80000,4250,120,50,80,200,120,0)));
         _loc2_.addFu(new BossArgVO(764,650000,5600,450,50,80,150,80,new BossSkillData0(250,{
            "hurt":8000,
            "roleNum":5
         },2),1016,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(753,80000,4250,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(755,80000,4250,120,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(752,80000,4250,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(753,80000,4250,120,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(753,80000,4250,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(755,80000,4250,120,50,80,200,120,0)));
         _loc2_.addFu(new BossArgVO(762,650000,5600,450,50,80,150,80,new BossSkillData1(10,{
            "hurt":8000,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(753,80000,4250,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(751,80000,4250,120,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(752,80000,4250,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(755,80000,4250,120,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(753,80000,4250,120,50,80,200,120,0)));
         _loc2_.addFu(new BossArgVO(761,650000,5600,450,50,80,150,80,new BossSkillData1(10,{"hurt":8000},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(752,80000,4250,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(755,80000,4250,120,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(753,80000,4250,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(751,80000,4250,120,50,80,200,120,0)));
         _loc2_.addFu(new BossArgVO(765,650000,5600,450,50,80,150,80,new BossSkillData0(250,{
            "hurt":8000,
            "roleNum":5
         },2),1016,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

