package file
{
   import flash.utils.Dictionary;
   import mogames.gameData.base.func.ForgeVO;
   import mogames.gameData.base.vo.NeedVO;
   import mogames.gameData.good.equip.GameEquipVO;
   
   public class ForgeConfig
   {
      
      private static var _instance:ForgeConfig;
      
      private var _list:Array;
      
      private var _dict:Dictionary;
      
      public function ForgeConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : ForgeConfig
      {
         if(!_instance)
         {
            _instance = new ForgeConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = [];
         this._list[this._list.length] = new ForgeVO(1,[new NeedVO(10901,3),new NeedVO(10000,3000)]);
         this._list[this._list.length] = new ForgeVO(2,[new NeedVO(10902,3),new NeedVO(10000,9000)]);
         this._list[this._list.length] = new ForgeVO(3,[new NeedVO(10903,3),new NeedVO(10000,15000)]);
         this._list[this._list.length] = new ForgeVO(4,[new NeedVO(10904,4),new NeedVO(10000,25000)]);
         this._list[this._list.length] = new ForgeVO(5,[new NeedVO(10905,4),new NeedVO(10000,35000)]);
         this._list[this._list.length] = new ForgeVO(6,[new NeedVO(10906,4),new NeedVO(10000,45000)]);
         this._list[this._list.length] = new ForgeVO(7,[new NeedVO(10907,4),new NeedVO(10000,55000)]);
         this._dict = new Dictionary();
         this._dict[20051] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[21051] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[22051] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[23051] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[24051] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[20151] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[21151] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[22151] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[23151] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[24151] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[20251] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[21251] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[22251] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[23251] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[24251] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[20351] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[21351] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[22351] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[23351] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[24351] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[20451] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[21451] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[22451] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[23451] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[24451] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[20052] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[21052] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[22052] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[23052] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[24052] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[20152] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[21152] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[22152] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[23152] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[24152] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[20252] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[21252] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[22252] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[23252] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[24252] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[20352] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[21352] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[22352] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[23352] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[24352] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[20452] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[21452] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[22452] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[23452] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[24452] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,50000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,100000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,150000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,200000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,300000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,400000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,500000)])];
         this._dict[20053] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21053] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22053] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23053] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24053] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20153] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21153] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22153] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23153] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24153] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20253] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21253] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22253] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23253] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24253] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20353] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21353] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22353] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23353] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24353] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20453] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21453] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22453] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23453] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24453] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20054] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21054] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22054] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23054] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24054] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20154] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21154] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22154] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23154] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24154] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20254] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21254] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22254] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23254] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24254] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20354] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21354] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22354] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23354] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24354] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20454] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21454] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22454] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23454] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24454] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20055] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21055] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22055] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23055] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24055] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20155] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21155] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22155] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23155] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24155] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20255] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21255] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22255] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23255] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24255] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20355] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21355] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22355] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23355] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24355] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20455] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21455] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22455] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23455] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24455] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20056] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21056] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22056] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23056] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24056] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20156] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21156] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22156] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23156] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24156] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20256] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21256] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22256] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23256] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24256] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20356] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21356] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22356] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23356] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24356] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20456] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21456] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22456] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23456] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24456] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20057] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21057] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22057] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23057] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24057] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20157] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21157] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22157] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23157] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24157] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20257] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21257] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22257] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23257] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24257] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20357] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21357] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22357] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23357] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24357] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20457] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21457] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22457] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23457] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24457] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20058] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21058] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22058] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23058] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24058] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20158] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21158] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22158] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23158] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24158] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20258] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21258] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22258] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23258] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24258] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20358] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21358] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22358] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23358] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24358] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20458] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21458] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22458] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23458] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24458] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20059] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21059] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22059] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23059] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24059] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20159] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21159] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22159] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23159] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24159] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20259] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21259] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22259] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23259] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24259] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20359] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21359] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22359] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23359] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24359] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20459] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[21459] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[22459] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[23459] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[24459] = [new ForgeVO(1,[new NeedVO(10931,5),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,5),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,5),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,5),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,5),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,5),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,5),new NeedVO(10000,600000)])];
         this._dict[20060] = [new ForgeVO(1,[new NeedVO(10931,20),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,20),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,20),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,20),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,20),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,20),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,20),new NeedVO(10000,600000)])];
         this._dict[21060] = [new ForgeVO(1,[new NeedVO(10931,20),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,20),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,20),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,20),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,20),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,20),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,20),new NeedVO(10000,600000)])];
         this._dict[22060] = [new ForgeVO(1,[new NeedVO(10931,20),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,20),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,20),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,20),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,20),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,20),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,20),new NeedVO(10000,600000)])];
         this._dict[23060] = [new ForgeVO(1,[new NeedVO(10931,20),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,20),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,20),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,20),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,20),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,20),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,20),new NeedVO(10000,600000)])];
         this._dict[24060] = [new ForgeVO(1,[new NeedVO(10931,20),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,20),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,20),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,20),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,20),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,20),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,20),new NeedVO(10000,600000)])];
         this._dict[20160] = [new ForgeVO(1,[new NeedVO(10931,20),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,20),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,20),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,20),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,20),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,20),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,20),new NeedVO(10000,600000)])];
         this._dict[21160] = [new ForgeVO(1,[new NeedVO(10931,20),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,20),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,20),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,20),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,20),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,20),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,20),new NeedVO(10000,600000)])];
         this._dict[22160] = [new ForgeVO(1,[new NeedVO(10931,20),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,20),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,20),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,20),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,20),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,20),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,20),new NeedVO(10000,600000)])];
         this._dict[23160] = [new ForgeVO(1,[new NeedVO(10931,20),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,20),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,20),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,20),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,20),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,20),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,20),new NeedVO(10000,600000)])];
         this._dict[24160] = [new ForgeVO(1,[new NeedVO(10931,20),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,20),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,20),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,20),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,20),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,20),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,20),new NeedVO(10000,600000)])];
         this._dict[20260] = [new ForgeVO(1,[new NeedVO(10931,20),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,20),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,20),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,20),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,20),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,20),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,20),new NeedVO(10000,600000)])];
         this._dict[21260] = [new ForgeVO(1,[new NeedVO(10931,20),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,20),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,20),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,20),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,20),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,20),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,20),new NeedVO(10000,600000)])];
         this._dict[22260] = [new ForgeVO(1,[new NeedVO(10931,20),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,20),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,20),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,20),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,20),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,20),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,20),new NeedVO(10000,600000)])];
         this._dict[23260] = [new ForgeVO(1,[new NeedVO(10931,20),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,20),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,20),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,20),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,20),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,20),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,20),new NeedVO(10000,600000)])];
         this._dict[24260] = [new ForgeVO(1,[new NeedVO(10931,20),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,20),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,20),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,20),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,20),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,20),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,20),new NeedVO(10000,600000)])];
         this._dict[20360] = [new ForgeVO(1,[new NeedVO(10931,20),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,20),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,20),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,20),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,20),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,20),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,20),new NeedVO(10000,600000)])];
         this._dict[21360] = [new ForgeVO(1,[new NeedVO(10931,20),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,20),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,20),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,20),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,20),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,20),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,20),new NeedVO(10000,600000)])];
         this._dict[22360] = [new ForgeVO(1,[new NeedVO(10931,20),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,20),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,20),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,20),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,20),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,20),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,20),new NeedVO(10000,600000)])];
         this._dict[23360] = [new ForgeVO(1,[new NeedVO(10931,20),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,20),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,20),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,20),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,20),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,20),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,20),new NeedVO(10000,600000)])];
         this._dict[24360] = [new ForgeVO(1,[new NeedVO(10931,20),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,20),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,20),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,20),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,20),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,20),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,20),new NeedVO(10000,600000)])];
         this._dict[20460] = [new ForgeVO(1,[new NeedVO(10931,20),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,20),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,20),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,20),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,20),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,20),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,20),new NeedVO(10000,600000)])];
         this._dict[21460] = [new ForgeVO(1,[new NeedVO(10931,20),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,20),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,20),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,20),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,20),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,20),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,20),new NeedVO(10000,600000)])];
         this._dict[22460] = [new ForgeVO(1,[new NeedVO(10931,20),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,20),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,20),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,20),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,20),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,20),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,20),new NeedVO(10000,600000)])];
         this._dict[23460] = [new ForgeVO(1,[new NeedVO(10931,20),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,20),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,20),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,20),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,20),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,20),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,20),new NeedVO(10000,600000)])];
         this._dict[24460] = [new ForgeVO(1,[new NeedVO(10931,20),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,20),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,20),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,20),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,20),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,20),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,20),new NeedVO(10000,600000)])];
         this._dict[20061] = [new ForgeVO(1,[new NeedVO(10931,30),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,30),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,30),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,30),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,30),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,30),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,30),new NeedVO(10000,600000)])];
         this._dict[21061] = [new ForgeVO(1,[new NeedVO(10931,30),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,30),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,30),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,30),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,30),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,30),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,30),new NeedVO(10000,600000)])];
         this._dict[22061] = [new ForgeVO(1,[new NeedVO(10931,30),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,30),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,30),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,30),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,30),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,30),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,30),new NeedVO(10000,600000)])];
         this._dict[23061] = [new ForgeVO(1,[new NeedVO(10931,30),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,30),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,30),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,30),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,30),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,30),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,30),new NeedVO(10000,600000)])];
         this._dict[24061] = [new ForgeVO(1,[new NeedVO(10931,30),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,30),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,30),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,30),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,30),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,30),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,30),new NeedVO(10000,600000)])];
         this._dict[20161] = [new ForgeVO(1,[new NeedVO(10931,30),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,30),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,30),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,30),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,30),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,30),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,30),new NeedVO(10000,600000)])];
         this._dict[21161] = [new ForgeVO(1,[new NeedVO(10931,30),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,30),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,30),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,30),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,30),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,30),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,30),new NeedVO(10000,600000)])];
         this._dict[22161] = [new ForgeVO(1,[new NeedVO(10931,30),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,30),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,30),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,30),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,30),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,30),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,30),new NeedVO(10000,600000)])];
         this._dict[23161] = [new ForgeVO(1,[new NeedVO(10931,30),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,30),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,30),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,30),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,30),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,30),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,30),new NeedVO(10000,600000)])];
         this._dict[24161] = [new ForgeVO(1,[new NeedVO(10931,30),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,30),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,30),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,30),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,30),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,30),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,30),new NeedVO(10000,600000)])];
         this._dict[20261] = [new ForgeVO(1,[new NeedVO(10931,30),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,30),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,30),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,30),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,30),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,30),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,30),new NeedVO(10000,600000)])];
         this._dict[21261] = [new ForgeVO(1,[new NeedVO(10931,30),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,30),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,30),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,30),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,30),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,30),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,30),new NeedVO(10000,600000)])];
         this._dict[22261] = [new ForgeVO(1,[new NeedVO(10931,30),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,30),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,30),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,30),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,30),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,30),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,30),new NeedVO(10000,600000)])];
         this._dict[23261] = [new ForgeVO(1,[new NeedVO(10931,30),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,30),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,30),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,30),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,30),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,30),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,30),new NeedVO(10000,600000)])];
         this._dict[24261] = [new ForgeVO(1,[new NeedVO(10931,30),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,30),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,30),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,30),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,30),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,30),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,30),new NeedVO(10000,600000)])];
         this._dict[20361] = [new ForgeVO(1,[new NeedVO(10931,30),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,30),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,30),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,30),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,30),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,30),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,30),new NeedVO(10000,600000)])];
         this._dict[21361] = [new ForgeVO(1,[new NeedVO(10931,30),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,30),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,30),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,30),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,30),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,30),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,30),new NeedVO(10000,600000)])];
         this._dict[22361] = [new ForgeVO(1,[new NeedVO(10931,30),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,30),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,30),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,30),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,30),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,30),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,30),new NeedVO(10000,600000)])];
         this._dict[23361] = [new ForgeVO(1,[new NeedVO(10931,30),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,30),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,30),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,30),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,30),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,30),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,30),new NeedVO(10000,600000)])];
         this._dict[24361] = [new ForgeVO(1,[new NeedVO(10931,30),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,30),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,30),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,30),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,30),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,30),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,30),new NeedVO(10000,600000)])];
         this._dict[20461] = [new ForgeVO(1,[new NeedVO(10931,30),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,30),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,30),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,30),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,30),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,30),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,30),new NeedVO(10000,600000)])];
         this._dict[21461] = [new ForgeVO(1,[new NeedVO(10931,30),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,30),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,30),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,30),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,30),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,30),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,30),new NeedVO(10000,600000)])];
         this._dict[22461] = [new ForgeVO(1,[new NeedVO(10931,30),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,30),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,30),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,30),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,30),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,30),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,30),new NeedVO(10000,600000)])];
         this._dict[23461] = [new ForgeVO(1,[new NeedVO(10931,30),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,30),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,30),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,30),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,30),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,30),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,30),new NeedVO(10000,600000)])];
         this._dict[24461] = [new ForgeVO(1,[new NeedVO(10931,30),new NeedVO(10000,150000)]),new ForgeVO(2,[new NeedVO(10932,30),new NeedVO(10000,200000)]),new ForgeVO(3,[new NeedVO(10933,30),new NeedVO(10000,250000)]),new ForgeVO(4,[new NeedVO(10934,30),new NeedVO(10000,300000)]),new ForgeVO(5,[new NeedVO(10935,30),new NeedVO(10000,400000)]),new ForgeVO(6,[new NeedVO(10936,30),new NeedVO(10000,500000)]),new ForgeVO(7,[new NeedVO(10937,30),new NeedVO(10000,600000)])];
      }
      
      public function findForge(param1:GameEquipVO) : ForgeVO
      {
         var _loc3_:ForgeVO = null;
         var _loc2_:Array = this._dict[param1.constEquip.id];
         if(_loc2_ == null)
         {
            _loc2_ = this._list;
         }
         for each(_loc3_ in _loc2_)
         {
            if(_loc3_.level == param1.level + 1)
            {
               return _loc3_;
            }
         }
         return null;
      }
   }
}

