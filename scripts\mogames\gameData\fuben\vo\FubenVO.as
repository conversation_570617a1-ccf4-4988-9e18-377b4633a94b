package mogames.gameData.fuben.vo
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import file.FubenConfig;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.flag.base.FlagVO;
   import mogames.gameNet.SaveManager;
   
   public class FubenVO
   {
      
      private var _fid:Oint = new Oint();
      
      private var _numID:Oint = new Oint();
      
      private var _winID:Oint = new Oint();
      
      private var _drops:Array;
      
      private var _rewards:Array;
      
      private var _infor:String;
      
      private var _frame:int;
      
      public function FubenVO(param1:int, param2:int, param3:int, param4:int, param5:Array, param6:String)
      {
         super();
         MathUtil.saveINT(this._fid,param1);
         MathUtil.saveINT(this._numID,param2);
         MathUtil.saveINT(this._winID,param3);
         this._drops = param5;
         this._infor = param6;
         this._frame = param4;
      }
      
      public function checkEnter(param1:Function) : void
      {
         param1();
      }
      
      public function handlerBuy(param1:Function) : void
      {
      }
      
      public function handlerEnter(param1:Boolean = true) : void
      {
         this.numFlag.changeValue(1);
         if(param1)
         {
            SaveManager.instance().saveAuto();
         }
      }
      
      public function get isOpen() : Boolean
      {
         return true;
      }
      
      public function get fubenID() : int
      {
         return MathUtil.loadINT(this._fid);
      }
      
      public function get frame() : int
      {
         return this._frame;
      }
      
      public function get numFlag() : FlagVO
      {
         return FlagProxy.instance().fubenFlag.findFlag(MathUtil.loadINT(this._numID));
      }
      
      public function get winFlag() : FlagVO
      {
         return FlagProxy.instance().fubenFlag.findFlag(MathUtil.loadINT(this._winID));
      }
      
      public function get drops() : Array
      {
         return this._drops;
      }
      
      public function get rewards() : Array
      {
         if(!this._rewards)
         {
            this._rewards = FubenConfig.instance().showDrops(this._drops);
         }
         return this._rewards;
      }
      
      public function get infor() : String
      {
         return this._infor;
      }
      
      public function get leftInfor() : String
      {
         return "今日挑战剩余：" + this.numFlag.left + "次";
      }
      
      public function get enterTip() : String
      {
         return this.leftInfor;
      }
      
      public function get buyTip() : String
      {
         return "";
      }
   }
}

