package mogames.gameData.box.vo
{
   import com.mogames.event.GameSignal;
   import com.mogames.event.SignalManager;
   import com.mogames.sound.SoundManager;
   import com.mogames.utils.MathUtil;
   import com.mogames.utils.TxtUtil;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.box.base.GameBoxVO;
   import mogames.gameData.mission.BattleProxy;
   import mogames.gameData.role.battle.BattleFriendVO;
   import mogames.gameData.role.battle.BattleHeroVO;
   import mogames.gameData.role.hero.HeroGameVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameRole.RoleFactory;
   import mogames.gameRole.TargetUtil;
   import mogames.gameRole.base.IRole;
   import mogames.gameRole.view.RoleBaseView;
   
   public class ArmyBoxVO extends GameBoxVO
   {
      
      public function ArmyBoxVO(param1:Number, param2:int, param3:Object)
      {
         super(param1,param2,param3);
      }
      
      override public function handlerOpen() : String
      {
         var _loc6_:BattleFriendVO = null;
         var _loc7_:IRole = null;
         var _loc1_:Array = TargetUtil.friendHeros();
         if(_loc1_.length <= 0)
         {
            SoundManager.instance().playAudio("AUDIO_FAIL");
            return TxtUtil.setColor("空空如也","FFFF00");
         }
         var _loc2_:HeroGameVO = (_loc1_[int(Math.random() * _loc1_.length)].roleVO as BattleHeroVO).heroVO;
         var _loc3_:Rectangle = BattleProxy.instance().dataMove.rectFar;
         var _loc4_:int = MathUtil.randomNum(findArg("min"),findArg("max"));
         var _loc5_:int = 0;
         while(_loc5_ < _loc4_)
         {
            _loc6_ = new BattleFriendVO();
            _loc6_.createData(_loc2_,0);
            _loc7_ = RoleFactory.newRole(_loc2_.heroInfo.armyID);
            _loc7_.createVO(_loc6_,BattleProxy.instance().dataMove.findFriendRect(_loc6_.skinVO.isFar));
            _loc7_.createView(new RoleBaseView(),1,1);
            _loc7_.createOut(null);
            _loc7_.x = _loc3_.x + Math.random() * _loc3_.width;
            _loc7_.y = _loc3_.y + Math.random() * _loc3_.height;
            Layers.obLayer.addChild(_loc7_.view);
            EffectManager.addBMC("SEQ_COME_UP_CLIP",Layers.frontLayer,_loc7_.x,_loc7_.y);
            SignalManager.signalRole.dispatchEvent({
               "signal":GameSignal.ROLE_ADD,
               "type":"add_friend_army",
               "role":_loc7_
            });
            _loc5_++;
         }
         SoundManager.instance().playAudio("AUDIO_BING_FU");
         return TxtUtil.setColor("召集" + _loc4_ + "名士兵","99FF00");
      }
   }
}

