package mogames.gameAsset
{
   import com.mogames.utils.ConvertUtil;
   
   public class SeqBeastUtil
   {
      
      private static var _skins:Array = ["_STAND1","_STAND2","_MOVE","_ACTIVE","_DEAD"];
      
      public function SeqBeastUtil()
      {
         super();
      }
      
      public static function newSeq(param1:String) : Array
      {
         var _loc4_:String = null;
         var _loc2_:Array = [];
         var _loc3_:Array = newSkins(param1);
         for each(_loc4_ in _loc3_)
         {
            _loc2_.push(ConvertUtil.convertMC(AssetManager.newMCRes(_loc4_)));
         }
         return _loc2_;
      }
      
      private static function newSkins(param1:String) : Array
      {
         var _loc3_:String = null;
         var _loc2_:Array = [];
         for each(_loc3_ in _skins)
         {
            _loc2_.push(param1 + _loc3_);
         }
         return _loc2_;
      }
   }
}

