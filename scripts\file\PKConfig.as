package file
{
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.pk.RankHonorProxy;
   import mogames.gameData.pk.vo.LianShengVO;
   import mogames.gameData.pk.vo.PKModeVO;
   import mogames.gameData.pk.vo.PKRewardVO;
   
   public class PKConfig
   {
      
      private static var _instance:PKConfig;
      
      private var _lian:Array;
      
      private var _modes:Array;
      
      private var _ranks:Array;
      
      private var _normals:Array;
      
      public function PKConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : PKConfig
      {
         if(!_instance)
         {
            _instance = new PKConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._lian = [];
         this._lian[this._lian.length] = new LianShengVO(205,3,[new BaseRewardVO(10000,10000),new BaseRewardVO(10304,5)]);
         this._lian[this._lian.length] = new LianShengVO(206,6,[new BaseRewardVO(10000,15000),new BaseRewardVO(11005,3),new BaseRewardVO(10305,3)]);
         this._lian[this._lian.length] = new LianShengVO(207,10,[new BaseRewardVO(10000,20000),new BaseRewardVO(10852,1),new BaseRewardVO(10303,1)]);
         this._modes = [];
         this._modes[this._modes.length] = new PKModeVO(0,333,{
            "arg0":0.03,
            "arg1":88,
            "arg2":33,
            "arg3":66
         });
         this._modes[this._modes.length] = new PKModeVO(1,334,{
            "arg0":0.03,
            "arg1":88,
            "arg2":33,
            "arg3":66
         });
         this._modes[this._modes.length] = new PKModeVO(2,335,{
            "arg0":0.03,
            "arg1":88,
            "arg2":33,
            "arg3":66
         });
         this._ranks = [];
         this._ranks[this._ranks.length] = new PKRewardVO([1],[new BaseRewardVO(10940,3),new BaseRewardVO(10833,3),new BaseRewardVO(10316,8)]);
         this._ranks[this._ranks.length] = new PKRewardVO([2],[new BaseRewardVO(10940,2),new BaseRewardVO(10833,2),new BaseRewardVO(10455,6)]);
         this._ranks[this._ranks.length] = new PKRewardVO([3],[new BaseRewardVO(10940,1),new BaseRewardVO(10833,1),new BaseRewardVO(10456,6)]);
         this._ranks[this._ranks.length] = new PKRewardVO({
            "min":4,
            "max":10
         },[new BaseRewardVO(10930,1),new BaseRewardVO(10455,2),new BaseRewardVO(10456,2)]);
         this._ranks[this._ranks.length] = new PKRewardVO([20,40,60,80],[new BaseRewardVO(10930,1),new BaseRewardVO(10832,1),new BaseRewardVO(10455,1)]);
         this._ranks[this._ranks.length] = new PKRewardVO([30,50,70,90],[new BaseRewardVO(10914,1),new BaseRewardVO(10832,1),new BaseRewardVO(10456,1)]);
         this._normals = [];
         this._normals[this._normals.length] = new PKRewardVO({
            "min":1,
            "max":10
         },[new BaseRewardVO(10833,2),new BaseRewardVO(10832,4),new BaseRewardVO(10831,6)]);
         this._normals[this._normals.length] = new PKRewardVO({
            "min":11,
            "max":50
         },[new BaseRewardVO(50006,4),new BaseRewardVO(10914,2),new BaseRewardVO(10831,6)]);
         this._normals[this._normals.length] = new PKRewardVO({
            "min":51,
            "max":100
         },[new BaseRewardVO(50007,4),new BaseRewardVO(10852,4),new BaseRewardVO(10831,4)]);
         this._normals[this._normals.length] = new PKRewardVO({
            "min":101,
            "max":500
         },[new BaseRewardVO(50009,4),new BaseRewardVO(10851,4),new BaseRewardVO(10831,2)]);
         this._normals[this._normals.length] = new PKRewardVO({
            "min":501,
            "max":1000
         },[new BaseRewardVO(11005,8),new BaseRewardVO(11151,4),new BaseRewardVO(10300,20)]);
         this._normals[this._normals.length] = new PKRewardVO({
            "min":1001,
            "max":5000
         },[new BaseRewardVO(11005,6),new BaseRewardVO(11157,6),new BaseRewardVO(10300,16)]);
         this._normals[this._normals.length] = new PKRewardVO({
            "min":5001,
            "max":10000
         },[new BaseRewardVO(11005,4),new BaseRewardVO(11003,10),new BaseRewardVO(10300,12)]);
      }
      
      public function findMode(param1:int) : PKModeVO
      {
         return this._modes[param1];
      }
      
      public function countReward() : Object
      {
         var _loc1_:int = RankHonorProxy.instance().myRankNum;
         var _loc2_:Object = new Object();
         _loc2_.rank = this.findRewards(_loc1_,this._ranks);
         _loc2_.normal = this.findRewards(_loc1_,this._normals);
         if(!_loc2_.rank && !_loc2_.normal)
         {
            return null;
         }
         return _loc2_;
      }
      
      private function findRewards(param1:int, param2:Array) : PKRewardVO
      {
         var _loc3_:PKRewardVO = null;
         for each(_loc3_ in param2)
         {
            if(_loc3_.isArea(param1))
            {
               return _loc3_;
            }
         }
         return null;
      }
      
      public function get lianshengs() : Array
      {
         return this._lian;
      }
      
      public function get rankRewards() : Array
      {
         return this._ranks;
      }
      
      public function get normalRewards() : Array
      {
         return this._normals;
      }
   }
}

