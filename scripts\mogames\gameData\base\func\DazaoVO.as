package mogames.gameData.base.func
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import file.GoodConfig;
   import mogames.gameData.base.RewardProxy;
   import mogames.gameData.base.UseProxy;
   import mogames.gameData.base.vo.EquipRewardVO;
   import mogames.gameData.base.vo.NeedVO;
   import mogames.gameData.game.LackVO;
   import mogames.gameData.good.base.ConstEquipVO;
   import mogames.gameData.master.MasterProxy;
   
   public class DazaoVO
   {
      
      private var _paper:NeedVO;
      
      private var _reward:EquipRewardVO;
      
      private var _equipID:Oint = new Oint();
      
      private var _gold:Oint = new Oint();
      
      private var _yin:Oint = new Oint();
      
      private var _list:Array;
      
      public function DazaoVO(param1:NeedVO, param2:int, param3:EquipRewardVO, param4:int, param5:int, param6:Array)
      {
         super();
         this._paper = param1;
         this._reward = param3;
         MathUtil.saveINT(this._equipID,param2);
         MathUtil.saveINT(this._gold,param4);
         MathUtil.saveINT(this._yin,param5);
         this._list = param6;
      }
      
      public function checkLack() : LackVO
      {
         var _loc1_:LackVO = null;
         _loc1_ = MasterProxy.instance().checkValue(10000,this.gold);
         if(_loc1_ != null)
         {
            return _loc1_;
         }
         _loc1_ = MasterProxy.instance().checkValue(10006,this.yin);
         if(_loc1_ != null)
         {
            return _loc1_;
         }
         _loc1_ = UseProxy.instance().checkLack(this._list.concat(this._paper));
         if(_loc1_ != null)
         {
            return _loc1_;
         }
         return null;
      }
      
      public function handlerDazao(param1:int) : void
      {
         var _loc2_:EquipRewardVO = null;
         MasterProxy.instance().changeValue(10000,-this.gold);
         MasterProxy.instance().changeValue(10006,-this.yin);
         UseProxy.instance().useStuff(this._list.concat(this._paper),null);
         if((this._reward.constGood as ConstEquipVO).lvLimit >= 50)
         {
            _loc2_ = new EquipRewardVO(this._reward.constGood.id,param1);
            RewardProxy.instance().addGiftReward([_loc2_]);
         }
         else
         {
            RewardProxy.instance().addGiftReward([this._reward]);
         }
      }
      
      public function get paper() : NeedVO
      {
         return this._paper;
      }
      
      public function get reward() : EquipRewardVO
      {
         return this._reward;
      }
      
      public function get needEquip() : ConstEquipVO
      {
         return GoodConfig.instance().findConstGood(this.needEquipID) as ConstEquipVO;
      }
      
      public function get needEquipID() : int
      {
         return MathUtil.loadINT(this._equipID);
      }
      
      public function get gold() : int
      {
         return MathUtil.loadINT(this._gold);
      }
      
      public function get yin() : int
      {
         return MathUtil.loadINT(this._yin);
      }
      
      public function get needList() : Array
      {
         return this._list;
      }
   }
}

