package mogames.gameData.mall.vo
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.game.LackVO;
   
   public class MallBaseVO
   {
      
      private var _price:Oint = new Oint();
      
      private var _type:Oint = new Oint();
      
      private var _rewardVO:BaseRewardVO;
      
      public function MallBaseVO(param1:int, param2:int, param3:BaseRewardVO)
      {
         super();
         MathUtil.saveINT(this._price,param1);
         MathUtil.saveINT(this._type,param2);
         this._rewardVO = param3;
      }
      
      public function checkLack(param1:int) : LackVO
      {
         return null;
      }
      
      public function handlerBuy(param1:int, param2:Function) : void
      {
      }
      
      public function askBuy(param1:int) : String
      {
         return "";
      }
      
      public function get price() : int
      {
         return MathUtil.loadINT(this._price);
      }
      
      public function get type() : int
      {
         return MathUtil.loadINT(this._type);
      }
      
      public function get rewardVO() : BaseRewardVO
      {
         return this._rewardVO;
      }
   }
}

