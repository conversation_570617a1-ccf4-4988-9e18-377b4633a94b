package file
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class CBZZConfig
   {
      
      private static var _instance:CBZZConfig;
      
      public var ccData:BossArgVO;
      
      public var fuDatas:Array;
      
      public var waveData:WaveDataVO;
      
      public var yuanbings:Array;
      
      public var showTime:int;
      
      public var needTime:int;
      
      public var speed:int;
      
      public function CBZZConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : CBZZConfig
      {
         if(!_instance)
         {
            _instance = new CBZZConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         var _loc1_:OneWaveVO = null;
         this.showTime = 18;
         this.needTime = 4;
         this.speed = 100;
         this.ccData = new BossArgVO(312,350000,1100,400,50,50,150,150,new BossSkillData1(80,{
            "hurt":3000,
            "killPer":20
         },3),0,0);
         this.fuDatas = [];
         this.fuDatas[this.fuDatas.length] = new BossArgVO(201,25000,400,110,50,50,150,150,new BossSkillData1(100,{"hurt":2500},2),1006,0);
         this.fuDatas[this.fuDatas.length] = new BossArgVO(202,25000,400,110,50,50,150,150,new BossSkillData1(100,{"hurt":1850},2),1018,0);
         this.fuDatas[this.fuDatas.length] = new BossArgVO(208,25000,400,110,50,50,150,150,new BossSkillData1(100,{
            "hurt":3500,
            "hurtCount":3
         },2),1019,0);
         this.fuDatas[this.fuDatas.length] = new BossArgVO(204,25000,400,110,50,50,150,150,new BossSkillData1(100,{"hurt":2500},2),1011,0);
         this.fuDatas[this.fuDatas.length] = new BossArgVO(205,25000,400,110,50,50,150,150,new BossSkillData1(100,{"hurt":3000},2),1008,0);
         this.fuDatas[this.fuDatas.length] = new BossArgVO(210,25000,400,110,50,50,150,150,new BossSkillData1(100,{"hurt":3000},2),1012,0);
         this.fuDatas[this.fuDatas.length] = new BossArgVO(211,25000,400,110,50,50,150,150,new BossSkillData1(100,{
            "hurt":1500,
            "hurtCount":5
         },2),1017,0);
         this.fuDatas[this.fuDatas.length] = new BossArgVO(212,25000,400,110,50,50,150,150,new BossSkillData1(100,{
            "hurt":3000,
            "keepTime":4
         },2),1020,0);
         this.yuanbings = [];
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(2,new RoleArgVO(108,6000,600,200,30,25,150,100,{
            "rate":100,
            "hurtPer":30,
            "keepTime":3
         }));
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(2,new RoleArgVO(110,6000,600,200,30,25,150,100,{
            "rate":150,
            "hurtBei":3
         }));
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(2,new RoleArgVO(112,6000,600,200,30,25,150,100,{
            "rate":150,
            "hurtBei":1.8
         }));
         this.waveData = new WaveDataVO(0);
         this.waveData.limitBR = new WaveLimitVO(5000,1.2,1.3);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(101,1500,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(102,1500,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(103,1500,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(104,1500,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(105,1500,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(106,1500,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(107,1500,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(108,1500,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(109,1500,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(110,1500,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(111,1500,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(112,1500,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
      }
   }
}

