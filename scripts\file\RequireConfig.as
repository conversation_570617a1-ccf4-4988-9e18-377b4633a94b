package file
{
   import mogames.gameData.require.base.ConstReqVO;
   
   public class RequireConfig
   {
      
      private static var _instance:RequireConfig;
      
      private var _list:Vector.<ConstReqVO>;
      
      public function RequireConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : RequireConfig
      {
         if(!_instance)
         {
            _instance = new RequireConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = new Vector.<ConstReqVO>();
         this._list[this._list.length] = new ConstReqVO(101,"我方没有武将败阵。");
         this._list[this._list.length] = new ConstReqVO(102,"@param0内获得胜利。");
         this._list[this._list.length] = new ConstReqVO(103,"不使用@param0武将。");
         this._list[this._list.length] = new ConstReqVO(104,"平民阵亡数不超过@param0名。");
         this._list[this._list.length] = new ConstReqVO(105,"不释放武将技能。");
         this._list[this._list.length] = new ConstReqVO(106,"只使用@param0次武将技能。");
         this._list[this._list.length] = new ConstReqVO(107,"不使用主公技能。");
         this._list[this._list.length] = new ConstReqVO(108,"只使用@param0次主公技能。");
         this._list[this._list.length] = new ConstReqVO(109,"胜利时剩余木头数量超过@param0。");
         this._list[this._list.length] = new ConstReqVO(110,"胜利时剩余食物数量超过@param0。");
         this._list[this._list.length] = new ConstReqVO(111,"胜利时剩余木头数量少于@param0。");
         this._list[this._list.length] = new ConstReqVO(112,"胜利时剩余食物数量少于@param0。");
         this._list[this._list.length] = new ConstReqVO(113,"不使用行囊物品。");
         this._list[this._list.length] = new ConstReqVO(114,"木头采集总量超过@param0。");
         this._list[this._list.length] = new ConstReqVO(115,"食物采集总量超过@param0。");
         this._list[this._list.length] = new ConstReqVO(116,"不采集任何资源。");
         this._list[this._list.length] = new ConstReqVO(117,"士兵阵亡数量少于@param0名。");
         this._list[this._list.length] = new ConstReqVO(118,"只训练@param0。");
         this._list[this._list.length] = new ConstReqVO(119,"只训练近战兵种。");
         this._list[this._list.length] = new ConstReqVO(120,"只训练远程兵种。");
         this._list[this._list.length] = new ConstReqVO(121,"只出战@param0武将。");
         this._list[this._list.length] = new ConstReqVO(122,"只出战@param0名武将。");
         this._list[this._list.length] = new ConstReqVO(123,"不使用换将令。");
      }
      
      public function findConstVO(param1:int) : ConstReqVO
      {
         var _loc2_:ConstReqVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.rid == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

