package mogames.gameData.base
{
   import com.mogames.utils.MathUtil;
   import file.ChouConfig;
   import mogames.gameData.base.func.WJChouVO;
   
   public class ChouProxy
   {
      
      private static var _instance:ChouProxy;
      
      private var _list:Array;
      
      public function ChouProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this._list = ChouConfig.instance().list;
      }
      
      public static function instance() : ChouProxy
      {
         if(!_instance)
         {
            _instance = new ChouProxy();
         }
         return _instance;
      }
      
      public function startNew() : void
      {
         var _loc1_:WJChouVO = null;
         for each(_loc1_ in this._list)
         {
            _loc1_.startNew();
         }
      }
      
      public function set loadData(param1:Array) : void
      {
         var _loc2_:Array = null;
         var _loc3_:WJChouVO = null;
         var _loc4_:String = null;
         for each(_loc4_ in param1)
         {
            _loc2_ = MathUtil.arrStringToNum(_loc4_.split("H"));
            _loc3_ = ChouConfig.instance().findVO(_loc2_[0]);
            if(_loc3_)
            {
               _loc3_.loadData = _loc2_;
            }
         }
      }
      
      public function get saveData() : Array
      {
         var _loc2_:WJChouVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._list)
         {
            _loc1_[_loc1_.length] = _loc2_.saveData;
         }
         return _loc1_;
      }
   }
}

