package file
{
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.mall.vo.MallFuncVO;
   import mogames.gameData.mall.vo.MallMoneyVO;
   import mogames.gameData.mall.vo.MoneyFuncVO;
   
   public class MoneyFuncConfig
   {
      
      private static var _instance:MoneyFuncConfig;
      
      public var buyTaverns:Array;
      
      public var buyPalaces:Array;
      
      public var buyLiangs:Array;
      
      public var buyCells:Array;
      
      public var buyZQZP:Array;
      
      public var buyDepot:MoneyFuncVO;
      
      public var buyBag:MoneyFuncVO;
      
      public var buyBuQian:MoneyFuncVO;
      
      public var buyRevive:MoneyFuncVO;
      
      public var buySecret:MoneyFuncVO;
      
      public var buyUnion:MoneyFuncVO;
      
      public var buyHeishi:MoneyFuncVO;
      
      public var buySiFang:MoneyFuncVO;
      
      public var buySiLiao:MoneyFuncVO;
      
      public var buyDan:MoneyFuncVO;
      
      public var buyChouOne:MoneyFuncVO;
      
      public var buyChouTen:MoneyFuncVO;
      
      public var buyTower:MoneyFuncVO;
      
      public var buyFuben:MallFuncVO;
      
      public var buyShiQi:MallMoneyVO;
      
      public var buyGuwu:MoneyFuncVO;
      
      public var buyTZQLGuwu:MoneyFuncVO;
      
      public var buyTZQL:MallFuncVO;
      
      public function MoneyFuncConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : MoneyFuncConfig
      {
         if(!_instance)
         {
            _instance = new MoneyFuncConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this.buyTaverns = [];
         this.buyTaverns[this.buyTaverns.length] = new MoneyFuncVO(3408,5);
         this.buyTaverns[this.buyTaverns.length] = new MoneyFuncVO(3409,10);
         this.buyTaverns[this.buyTaverns.length] = new MoneyFuncVO(3410,15);
         this.buyTaverns[this.buyTaverns.length] = new MoneyFuncVO(3411,20);
         this.buyPalaces = [];
         this.buyPalaces[this.buyPalaces.length] = new MoneyFuncVO(3412,10);
         this.buyPalaces[this.buyPalaces.length] = new MoneyFuncVO(3413,20);
         this.buyPalaces[this.buyPalaces.length] = new MoneyFuncVO(3414,30);
         this.buyPalaces[this.buyPalaces.length] = new MoneyFuncVO(3415,40);
         this.buyLiangs = [];
         this.buyLiangs[this.buyLiangs.length] = new MoneyFuncVO(3416,20);
         this.buyLiangs[this.buyLiangs.length] = new MoneyFuncVO(3417,30);
         this.buyLiangs[this.buyLiangs.length] = new MoneyFuncVO(3418,40);
         this.buyLiangs[this.buyLiangs.length] = new MoneyFuncVO(3419,50);
         this.buyCells = [];
         this.buyCells[this.buyCells.length] = new MoneyFuncVO(3422,100);
         this.buyCells[this.buyCells.length] = new MoneyFuncVO(3422,100);
         this.buyZQZP = [];
         this.buyZQZP[this.buyZQZP.length] = new MoneyFuncVO(3713,29);
         this.buyZQZP[this.buyZQZP.length] = new MoneyFuncVO(3714,59);
         this.buyZQZP[this.buyZQZP.length] = new MoneyFuncVO(3715,99);
         this.buyZQZP[this.buyZQZP.length] = new MoneyFuncVO(3716,199);
         this.buyZQZP[this.buyZQZP.length] = new MoneyFuncVO(3717,299);
         this.buyZQZP[this.buyZQZP.length] = new MoneyFuncVO(3718,399);
         this.buyDepot = new MoneyFuncVO(3423,100);
         this.buyBag = new MoneyFuncVO(3424,100);
         this.buyBuQian = new MoneyFuncVO(3426,10);
         this.buyRevive = new MoneyFuncVO(3582,30);
         this.buySecret = new MoneyFuncVO(3594,50);
         this.buyUnion = new MoneyFuncVO(3610,10);
         this.buyHeishi = new MoneyFuncVO(3663,15);
         this.buySiFang = new MoneyFuncVO(3711,8);
         this.buySiLiao = new MoneyFuncVO(3711,8);
         this.buyDan = new MoneyFuncVO(3419,50);
         this.buyChouOne = new MoneyFuncVO(3781,8);
         this.buyChouTen = new MoneyFuncVO(3782,80);
         this.buyTower = new MoneyFuncVO(3784,15);
         this.buyGuwu = new MoneyFuncVO(3896,20);
         this.buyFuben = new MallFuncVO(3412,10,new BaseRewardVO(10015,1));
         this.buyShiQi = new MallMoneyVO(3575,2,new BaseRewardVO(10304,1));
         this.buyTZQL = new MallFuncVO(3901,20,new BaseRewardVO(10015,1));
         this.buyTZQLGuwu = new MoneyFuncVO(3901,20);
      }
   }
}

