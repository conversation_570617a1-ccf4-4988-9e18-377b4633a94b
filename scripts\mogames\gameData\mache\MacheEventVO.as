package mogames.gameData.mache
{
   import com.mogames.utils.TxtUtil;
   import mogames.gameData.base.RewardProxy;
   import mogames.gameData.base.UseProxy;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.base.vo.NeedVO;
   
   public class MacheEventVO
   {
      
      protected var _reward:Boolean;
      
      protected var _infor:String;
      
      protected var _list:Array;
      
      public function MacheEventVO(param1:<PERSON>olean, param2:String, param3:Array)
      {
         super();
         this._reward = param1;
         this._infor = param2;
         this._list = param3;
      }
      
      public function handlerEvent() : String
      {
         if(this._reward)
         {
            return this.handlerAdd();
         }
         return this.handlerReduce();
      }
      
      protected function handlerReduce() : String
      {
         var _loc2_:Object = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._list)
         {
            _loc1_[_loc1_.length] = new NeedVO(_loc2_.id,_loc2_.value.randomValue);
         }
         UseProxy.instance().useWithCachet(_loc1_);
         return this._infor + TxtUtil.setColor(UseProxy.instance().countactName2(_loc1_,"-"));
      }
      
      protected function handlerAdd() : String
      {
         var _loc2_:Object = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._list)
         {
            _loc1_[_loc1_.length] = new BaseRewardVO(_loc2_.id,_loc2_.value.randomValue);
         }
         RewardProxy.instance().addGiftReward(_loc1_,null,false,false);
         return this._infor + TxtUtil.setColor(RewardProxy.instance().parseName2(_loc1_,"+"),"99ff00");
      }
   }
}

