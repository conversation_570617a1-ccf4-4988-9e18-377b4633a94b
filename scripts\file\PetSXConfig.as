package file
{
   import mogames.gameData.base.func.PetSXVO;
   import mogames.gameData.base.vo.NeedVO;
   
   public class PetSXConfig
   {
      
      private static var _instance:PetSXConfig;
      
      private var _dict:Array;
      
      public function PetSXConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : PetSXConfig
      {
         if(!_instance)
         {
            _instance = new PetSXConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._dict = [];
         this._dict[this._dict.length] = new PetSXVO(1000,[900,800,700,600,500,400,300,200,100],[[new NeedVO(17001,15),new NeedVO(17005,10),new NeedVO(17006,10)],[new NeedVO(17011,15),new NeedVO(17015,10),new NeedVO(17016,10)],[new NeedVO(17021,15),new NeedVO(17025,10),new NeedVO(17026,10)],[new NeedVO(17031,15),new NeedVO(17035,10),new NeedVO(17036,10)],[new NeedVO(17041,15),new NeedVO(17045,10),new NeedVO(17046,10)],[new NeedVO(17051,15),new NeedVO(17055,10),new NeedVO(17056,10)],[new NeedVO(17061,15),new NeedVO(17065,10),new NeedVO(17066,10)],[new NeedVO(17071,15),new NeedVO(17075,10),new NeedVO(17076,10)],[new NeedVO(17081,15),new NeedVO(17085,10),new NeedVO(17086,10)]]);
         this._dict[this._dict.length] = new PetSXVO(1001,[900,800,700,600,500,400,300,200,100],[[new NeedVO(17002,15),new NeedVO(17005,10),new NeedVO(17006,10)],[new NeedVO(17012,15),new NeedVO(17015,10),new NeedVO(17016,10)],[new NeedVO(17022,15),new NeedVO(17025,10),new NeedVO(17026,10)],[new NeedVO(17032,15),new NeedVO(17035,10),new NeedVO(17036,10)],[new NeedVO(17042,15),new NeedVO(17045,10),new NeedVO(17046,10)],[new NeedVO(17052,15),new NeedVO(17055,10),new NeedVO(17056,10)],[new NeedVO(17062,15),new NeedVO(17065,10),new NeedVO(17066,10)],[new NeedVO(17072,15),new NeedVO(17075,10),new NeedVO(17076,10)],[new NeedVO(17082,15),new NeedVO(17085,10),new NeedVO(17086,10)]]);
         this._dict[this._dict.length] = new PetSXVO(1002,[900,800,700,600,500,400,300,200,100],[[new NeedVO(17003,15),new NeedVO(17005,10),new NeedVO(17006,10)],[new NeedVO(17013,15),new NeedVO(17015,10),new NeedVO(17016,10)],[new NeedVO(17023,15),new NeedVO(17025,10),new NeedVO(17026,10)],[new NeedVO(17033,15),new NeedVO(17035,10),new NeedVO(17036,10)],[new NeedVO(17043,15),new NeedVO(17045,10),new NeedVO(17046,10)],[new NeedVO(17053,15),new NeedVO(17055,10),new NeedVO(17056,10)],[new NeedVO(17063,15),new NeedVO(17065,10),new NeedVO(17066,10)],[new NeedVO(17073,15),new NeedVO(17075,10),new NeedVO(17076,10)],[new NeedVO(17083,15),new NeedVO(17085,10),new NeedVO(17086,10)]]);
         this._dict[this._dict.length] = new PetSXVO(1003,[900,800,700,600,500,400,300,200,100],[[new NeedVO(17004,15),new NeedVO(17005,10),new NeedVO(17006,10)],[new NeedVO(17014,15),new NeedVO(17015,10),new NeedVO(17016,10)],[new NeedVO(17024,15),new NeedVO(17025,10),new NeedVO(17026,10)],[new NeedVO(17034,15),new NeedVO(17035,10),new NeedVO(17036,10)],[new NeedVO(17044,15),new NeedVO(17045,10),new NeedVO(17046,10)],[new NeedVO(17054,15),new NeedVO(17055,10),new NeedVO(17056,10)],[new NeedVO(17064,15),new NeedVO(17065,10),new NeedVO(17066,10)],[new NeedVO(17074,15),new NeedVO(17075,10),new NeedVO(17076,10)],[new NeedVO(17084,15),new NeedVO(17085,10),new NeedVO(17086,10)]]);
         this._dict[this._dict.length] = new PetSXVO(1004,[900,800,700,600,500,400,300,200,100],[[new NeedVO(17001,10),new NeedVO(17002,10),new NeedVO(17003,10),new NeedVO(17004,10)],[new NeedVO(17011,10),new NeedVO(17012,10),new NeedVO(17013,10),new NeedVO(17014,10)],[new NeedVO(17021,10),new NeedVO(17022,10),new NeedVO(17023,10),new NeedVO(17024,10)],[new NeedVO(17031,10),new NeedVO(17032,10),new NeedVO(17033,10),new NeedVO(17034,10)],[new NeedVO(17041,10),new NeedVO(17042,10),new NeedVO(17043,10),new NeedVO(17044,10)],[new NeedVO(17051,10),new NeedVO(17052,10),new NeedVO(17053,10),new NeedVO(17054,10)],[new NeedVO(17061,20),new NeedVO(17062,20),new NeedVO(17063,20),new NeedVO(17064,20)],[new NeedVO(17071,20),new NeedVO(17072,20),new NeedVO(17073,20),new NeedVO(17074,20)],[new NeedVO(17081,20),new NeedVO(17082,20),new NeedVO(17083,20),new NeedVO(17084,20)]]);
         this._dict[this._dict.length] = new PetSXVO(1005,[900,800,700,600,500,400,300,200,100],[[new NeedVO(17001,20),new NeedVO(17002,20),new NeedVO(17003,20),new NeedVO(17004,20)],[new NeedVO(17011,20),new NeedVO(17012,20),new NeedVO(17013,20),new NeedVO(17014,20)],[new NeedVO(17021,20),new NeedVO(17022,20),new NeedVO(17023,20),new NeedVO(17024,20)],[new NeedVO(17031,20),new NeedVO(17032,20),new NeedVO(17033,20),new NeedVO(17034,20)],[new NeedVO(17041,20),new NeedVO(17042,20),new NeedVO(17043,20),new NeedVO(17044,20)],[new NeedVO(17051,20),new NeedVO(17052,20),new NeedVO(17053,20),new NeedVO(17054,20)],[new NeedVO(17061,30),new NeedVO(17062,30),new NeedVO(17063,30),new NeedVO(17064,30)],[new NeedVO(17071,30),new NeedVO(17072,30),new NeedVO(17073,30),new NeedVO(17074,30)],[new NeedVO(17081,30),new NeedVO(17082,30),new NeedVO(17083,30),new NeedVO(17084,30)]]);
      }
      
      public function findSXVO(param1:int) : PetSXVO
      {
         var _loc2_:PetSXVO = null;
         for each(_loc2_ in this._dict)
         {
            if(_loc2_.petID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

