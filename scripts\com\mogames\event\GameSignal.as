package com.mogames.event
{
   import org.osflash.signals.Signal;
   
   public class GameSignal
   {
      
      public static const GUIDE_BATTLE:String = "GUIDE_BATTLE";
      
      public static const GUIDE_MAIN:String = "GUIDE_MAIN";
      
      public static const GUIDE_BAG_BOOK:String = "G<PERSON><PERSON>_BAG_BOOK";
      
      public static const UI_SAVE_ITEM:String = "UI_SAVE_ITEM";
      
      public static const BLOOD_BAR:String = "BLOOD_BAR";
      
      public static const DEL_ROLE_POINT:String = "DEL_ROLE_POINT";
      
      public static const DEL_MAP_POINT:String = "DEL_MAP_POINT";
      
      public static const REFRESH_MP:String = "REFRESH_MP";
      
      public static const TOWN_EXTRA_ITEM:String = "TOWN_EXTRA_ITEM";
      
      public static const TOWN_NORMAL_ITEM:String = "TOWN_NORMAL_ITEM";
      
      public static const UI_CHANGE_MODE:String = "UI_CHANGE_MODE";
      
      public static const SELECT_MAIN_HERO:String = "SELECT_MAIN_HERO";
      
      public static const MAYOR_HIRE_ITEM:String = "MAYOR_HIRE_ITEM";
      
      public static const TOWN_LEVY_ITEM:String = "TOWN_LEVY_ITEM";
      
      public static const TOWN_TASK_ITEM:String = "TOWN_TASK_ITEM";
      
      public static const TOWN_TAX_ITEM:String = "TOWN_TAX_ITEM";
      
      public static const MALL_ITEM:String = "MALL_ITEM";
      
      public static const MAYOR_SELECT_ITEM:String = "MAYOR_SELECT_ITEM";
      
      public static const HERO_STATUS_MENU:String = "HERO_STATUS_MENU";
      
      public static const PALACE_TIP:String = "PALACE_TIP";
      
      public static const TASK_ITEM:String = "TASK_ITEM";
      
      public static const REFRESH_UI_TIP:String = "REFRESH_UI_TIP";
      
      public static const HY_REWARD_ITEM:String = "HY_REWARD_ITEM";
      
      public static const SHOP_ITEM:String = "SHOP_ITEM";
      
      public static const FORGE_ITEM:String = "FORGE_ITEM";
      
      public static const ACHIEVE_ITEM:String = "ACHIEVE_ITEM";
      
      public static const CELL_UPGRADE_ITEM:String = "CELL_UPGRADE_ITEM";
      
      public static const BOOK_SKILL_ITEM:String = "BOOK_SKILL_ITEM";
      
      public static const SHANG_ITEM:String = "SHANG_ITEM";
      
      public static const TI_SHEN_ITEM:String = "TI_SHEN_ITEM";
      
      public static const SECRET_ITEM:String = "SECRET_ITEM";
      
      public static const CHARM_ITEM:String = "CHARM_ITEM";
      
      public static const PAY_REWARD_ITEM:String = "PAY_REWARD_ITEM";
      
      public static const BATTLE_HERO_ITEM:String = "BATTLE_HERO_ITEM";
      
      public static const REFRESH_DEPOT:String = "REFRESH_DEPOT";
      
      public static const KPXQ_ITEM:String = "KPXQ_ITEM";
      
      public static const LS_REWARD_ITEM:String = "LS_REWARD_ITEM";
      
      public static const RANK_HONOR_ITEM:String = "RANK_HONOR_ITEM";
      
      public static const RANK_UNION_ITEM:String = "RANK_UNION_ITEM";
      
      public static const UNION_EXAM_ITEM:String = "UNION_EXAM_ITEM";
      
      public static const UNION_MEMBER_ITEM:String = "UNION_MEMBER_ITEM";
      
      public static const UNION_SKILL_ITEM:String = "UNION_SKILL_ITEM";
      
      public static const UNION_TASK_ITEM:String = "UNION_TASK_ITEM";
      
      public static const UNION_GIVE_ITEM:String = "UNION_GIVE_ITEM";
      
      public static const SECRET_DICT_ITEM:String = "SECRET_DICT_ITEM";
      
      public static const GQ_PAY_ITEM:String = "GQ_PAY_ITEM";
      
      public static const HEI_SHI_ITEM:String = "HEI_SHI_ITEM";
      
      public static const MACHE_ITEM:String = "MACHE_ITEM";
      
      public static const ZJD_ITEM:String = "ZJD_ITEM";
      
      public static const PET_EQUIP_ITEM:String = "PET_EQUIP_ITEM";
      
      public static const PET_XS_ITEM:String = "PET_XS_ITEM";
      
      public static const REFRESH_VALUE:String = "REFRESH_VALUE";
      
      public static const ROLE_NEW:String = "ROLE_NEW";
      
      public static const ROLE_ADD:String = "ROLE_ADD";
      
      public static const ROLE_DIE:String = "ROLE_DIE";
      
      public static const HERO_DIE:String = "HERO_DIE";
      
      public static const BOSS_DIE:String = "BOSS_DIE";
      
      public static const HERO_DIE_END:String = "HERO_DIE_END";
      
      public static const REFRESH_RES:String = "REFRESH_RES";
      
      public static const GATHER_WOOD:String = "GATHER_WOOD";
      
      public static const GATHER_FOOD:String = "GATHER_FOOD";
      
      public static const DESTROY_RES:String = "DESTROY_RES";
      
      public static const ADD_RES:String = "ADD_RES";
      
      public static const RECYLE_CLIP:String = "RECYLE_CLIP";
      
      public static const RECYLE_ROLE:String = "RECYLE_ROLE";
      
      public static const RECYLE_BULLET:String = "RECYLE_BULLET";
      
      public static const SCROLL_LAYER:String = "SCROLL_LAYER";
      
      public static const SCROLL_MAP:String = "SCROLL_MAP";
      
      public static const CLICK_CASERN:String = "CLICK_CASERN";
      
      public static const START_TRAIN:String = "START_TRAIN";
      
      public static const HUAN_CASERN_HERO:String = "HUAN_CASERN_HERO";
      
      public static const WAVE_UPDATE:String = "UPDATE_WAVE";
      
      public static const WAVE_PAUSE_LOOP:String = "WAVE_PAUSE_LOOP";
      
      public static const WAVE_PAUSE_END:String = "WAVE_PAUSE_END";
      
      public static const WAVE_ROLE_ADD:String = "WAVE_ROLE_ADD";
      
      public static const WAVE_ROLE_END:String = "WAVE_ROLE_END";
      
      public static const BULLET_ADD:String = "BULLET_ADD";
      
      public static const BULLET_DEL:String = "BULLET_DEL";
      
      public static const BAG_CHAOFENG:String = "BAG_CHAOFENG";
      
      public static const BAG_PAUSE:String = "BAG_PAUSE";
      
      public static const USE_BAG:String = "USE_BAG";
      
      public static const USE_HERO_SKILL:String = "USE_HERO_SKILL";
      
      public static const USE_LEAD_SKILL:String = "USE_LEAD_SKILL";
      
      public static const USE_HUAN_JIANG:String = "USE_HUAN_JIANG";
      
      public static const TD_ESCAPE:String = "TD_ESCAPE";
      
      public static const TD_KILL:String = "TD_KILL";
      
      private var _signal:Signal = new Signal(Object);
      
      public function GameSignal()
      {
         super();
      }
      
      public function dispatchEvent(param1:Object = null) : void
      {
         this._signal.dispatch(param1);
      }
      
      public function addListener(param1:Function) : void
      {
         this._signal.add(param1);
      }
      
      public function removeListener(param1:Function) : void
      {
         this._signal.remove(param1);
      }
      
      public function removeAll() : void
      {
         this._signal.removeAll();
      }
   }
}

