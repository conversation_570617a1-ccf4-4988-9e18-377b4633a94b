package mogames.gameData.buff.base
{
   import com.mogames.data.Onum;
   import com.mogames.utils.MathUtil;
   import file.BuffConfig;
   import mogames.gameData.buff.ConstBuffVO;
   
   public class BuffVO
   {
      
      private var _args:Object;
      
      private var _constVO:ConstBuffVO;
      
      private var _time:Onum = new Onum();
      
      public function BuffVO(param1:int, param2:Number, param3:Object)
      {
         super();
         MathUtil.saveNUM(this._time,param2);
         this._args = param3;
         this._constVO = BuffConfig.instance().findConstBuff(param1);
      }
      
      public function get time() : Number
      {
         return MathUtil.loadNUM(this._time);
      }
      
      public function get args() : Object
      {
         return this._args;
      }
      
      public function get constVO() : ConstBuffVO
      {
         return this._constVO;
      }
      
      public function get isDebuff() : Boolean
      {
         return String(this.buffID).charAt(0) == "2";
      }
      
      public function get buffID() : int
      {
         return this._constVO.buffID;
      }
   }
}

