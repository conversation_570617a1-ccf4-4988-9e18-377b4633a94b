package file
{
   import com.mogames.utils.MathUtil;
   import flash.geom.Rectangle;
   import mogames.ConstData;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.fuben.vo.FubenVO;
   import mogames.gameData.master.MasterProxy;
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class XWDConfig
   {
      
      private static var _instance:XWDConfig;
      
      public var waveData:WaveDataVO;
      
      public var bossArg:Object;
      
      public function XWDConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : XWDConfig
      {
         if(!_instance)
         {
            _instance = new XWDConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         var _loc1_:OneWaveVO = null;
         this.bossArg = new Object();
         this.bossArg.rect = new Rectangle(6,330,1724,264);
         this.bossArg.enemies = [new RoleArgVO(270,100000,800,500,50,25,150,150,null),new RoleArgVO(270,100000,400,800,50,25,150,150,null),new RoleArgVO(270,100000,600,500,50,25,150,150,null),new RoleArgVO(270,100000,800,800,50,25,150,150,null)];
         this.waveData = new WaveDataVO(0);
         this.waveData.limitBR = new WaveLimitVO(9000,1.2,1.3);
         this.waveData.zhuBoss = new BossArgVO(1002,400000,2500,900,50,80,300,150,new BossSkillData1(15,{
            "hurt":3000,
            "def0":15,
            "def1":20,
            "jian":30,
            "time0":5,
            "time1":5
         },5),4003,0);
         _loc1_ = new OneWaveVO(20);
         _loc1_.addEnemy(new WaveEnemyVO(16,new RoleArgVO(269,2000,400,50,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
      }
      
      public function newReward(param1:Boolean) : Array
      {
         if(!param1)
         {
            return this.loseReward;
         }
         return this.winReward;
      }
      
      private function get winReward() : Array
      {
         var _loc5_:int = 0;
         var _loc1_:Array = [];
         var _loc2_:int = ConstData.INT2.v;
         if(MathUtil.checkOdds(300))
         {
            _loc2_ = ConstData.INT3.v;
         }
         var _loc3_:FubenVO = FubenConfig.instance().findFuben(405);
         var _loc4_:Array = _loc3_.drops.slice(1);
         var _loc6_:int = 0;
         while(_loc6_ < _loc2_)
         {
            _loc5_ = Math.random() * _loc4_.length;
            _loc1_[_loc1_.length] = _loc4_[_loc5_];
            _loc4_.splice(_loc5_,1);
            _loc6_++;
         }
         return _loc1_;
      }
      
      private function get loseReward() : Array
      {
         var _loc1_:int = MasterProxy.instance().masterVO.level * MathUtil.randomNum(300,450);
         return [new BaseRewardVO(10000,_loc1_)];
      }
   }
}

