package mogames.gameBuff.debuff
{
   import mogames.gameBuff.base.TimeRoleBuff;
   import mogames.gameData.buff.ConstBuffVO;
   
   public class NoskillBuff extends TimeRoleBuff
   {
      
      public function NoskillBuff(param1:ConstBuffVO)
      {
         super(param1);
      }
      
      override protected function createData() : void
      {
         _owner.noSkill = true;
      }
      
      override protected function onCleanRole() : void
      {
         _owner.noSkill = false;
      }
   }
}

