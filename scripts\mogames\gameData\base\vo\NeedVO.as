package mogames.gameData.base.vo
{
   import com.mogames.utils.TxtUtil;
   import file.GoodConfig;
   import mogames.ConstData;
   import mogames.gameData.depot.DepotProxy;
   import mogames.gameData.game.LackVO;
   import mogames.gameData.good.base.ConstVirtualVO;
   import mogames.gameData.good.vo.GameGoodVO;
   import mogames.gameData.master.MasterProxy;
   
   public class NeedVO
   {
      
      protected var _needGood:GameGoodVO;
      
      public function NeedVO(param1:int, param2:int)
      {
         super();
         this.create(param1,param2);
      }
      
      protected function create(param1:int, param2:int) : void
      {
         this._needGood = GoodConfig.instance().newGameGood(param1);
         this._needGood.amount = param2;
      }
      
      public function get lackVO() : LackVO
      {
         if(this.isVirtual)
         {
            return MasterProxy.instance().checkValue(this.needID,this.needNum);
         }
         var _loc1_:int = DepotProxy.instance().findNum(this.needID);
         if(_loc1_ < this.needNum)
         {
            return new LackVO(this.needName + "不足，当前拥有数为：" + _loc1_);
         }
         return null;
      }
      
      public function get needStr() : String
      {
         var _loc1_:int = this.isVirtual ? MasterProxy.instance().findValue(this.needID) : int(DepotProxy.instance().findNum(this.needID));
         var _loc2_:String = this.isVirtual ? this.needNum + "" : _loc1_ + "/" + this.needNum;
         if(_loc1_ < this._needGood.amount)
         {
            return TxtUtil.setColor(_loc2_,"ff0000");
         }
         return TxtUtil.setColor(_loc2_,"99ff00");
      }
      
      public function get needNameStr() : String
      {
         var _loc1_:int = this.isVirtual ? MasterProxy.instance().findValue(this.needID) : int(DepotProxy.instance().findNum(this.needID));
         if(_loc1_ < this._needGood.amount)
         {
            return TxtUtil.setColor(this.needName + "X" + this.needNum,"ff0000");
         }
         return TxtUtil.setColor(this.needName + "X" + this.needNum,"99ff00");
      }
      
      public function get askStr() : String
      {
         return TxtUtil.setColor(this._needGood.constGood.name + "X" + this._needGood.amount,ConstData.GOOD_COLOR1[this._needGood.quality]);
      }
      
      public function get needName() : String
      {
         return this._needGood.constGood.name;
      }
      
      public function get needID() : int
      {
         return this._needGood.constGood.id;
      }
      
      public function get needNum() : int
      {
         return this._needGood.amount;
      }
      
      public function get needGood() : GameGoodVO
      {
         return this._needGood;
      }
      
      public function get isVirtual() : Boolean
      {
         return this._needGood.constGood is ConstVirtualVO;
      }
      
      public function get isCachet() : Boolean
      {
         return this._needGood.constGood.id == 10001;
      }
   }
}

