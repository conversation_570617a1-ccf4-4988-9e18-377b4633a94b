package mogames.gameData.mall.vo
{
   import mogames.gameData.base.RewardProxy;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.good.vo.GameGoodVO;
   import mogames.gameData.master.MasterProxy;
   import mogames.gameNet.MulitProxy;
   import mogames.gameNet.SaveManager;
   import mogames.gameUI.prompt.LockMessage;
   
   public class MallFuncVO extends MallMoneyVO
   {
      
      public function MallFuncVO(param1:int, param2:int, param3:BaseRewardVO)
      {
         super(param1,param2,param3);
      }
      
      override public function handlerBuy(param1:int, param2:Function) : void
      {
         var sendBuy:Function = null;
         var buyNum:int = param1;
         var okFunc:Function = param2;
         sendBuy = function():void
         {
            var func:Function = null;
            func = function():void
            {
               MasterProxy.instance().changeValue(10010,-price * buyNum);
               LockMessage.instance().destroy();
               if(okFunc != null)
               {
                  okFunc();
               }
               SaveManager.instance().saveAuto(false);
            };
            handlerMoney(buyNum,func);
         };
         var vo:GameGoodVO = rewardVO.newGood();
         if(buyNum > 1)
         {
            vo.amount *= buyNum;
         }
         if(RewardProxy.instance().checkLack([vo]))
         {
            return;
         }
         LockMessage.instance().showMsg("正在处理购买数据，请稍等！");
         MulitProxy.instance().checkMulit(sendBuy,LockMessage.instance().destroy);
      }
   }
}

