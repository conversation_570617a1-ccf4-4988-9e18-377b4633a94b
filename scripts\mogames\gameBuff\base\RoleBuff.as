package mogames.gameBuff.base
{
   import com.mogames.display.SysBMC;
   import com.mogames.system.ActiveObject;
   import com.mogames.system.GameTimer;
   import mogames.gameAsset.AssetManager;
   import mogames.gameData.buff.ConstBuffVO;
   import mogames.gameData.buff.base.BuffVO;
   import mogames.gameRole.base.IRole;
   
   public class RoleBuff extends ActiveObject
   {
      
      protected var _constVO:ConstBuffVO;
      
      protected var _timer:GameTimer;
      
      protected var _buffVO:BuffVO;
      
      protected var _owner:IRole;
      
      protected var _bmc:SysBMC;
      
      protected var _clean:Boolean;
      
      public function RoleBuff(param1:ConstBuffVO)
      {
         super();
         this._constVO = param1;
         this._timer = new GameTimer();
         this._clean = false;
      }
      
      public function get constVO() : ConstBuffVO
      {
         return this._constVO;
      }
      
      public function start(param1:IRole, param2:BuffVO) : void
      {
         this._owner = param1;
         this._buffVO = param2;
         this.createSkin();
      }
      
      protected function handlerEnd() : void
      {
         if(!this._owner)
         {
            return;
         }
         if(!this._owner.buffList)
         {
            return;
         }
         var _loc1_:int = int(this._owner.buffList.indexOf(this));
         if(_loc1_ != -1)
         {
            this._owner.buffList.splice(_loc1_,1);
         }
         destroy();
      }
      
      protected function onCleanRole() : void
      {
      }
      
      private function cleanRole() : void
      {
         if(this._clean || !this.ownEnabled)
         {
            return;
         }
         this._clean = true;
         this.onCleanRole();
      }
      
      protected function createSkin() : void
      {
         var _loc1_:Object = null;
         if(Boolean(this._buffVO.args) && Boolean(this._buffVO.args.skin))
         {
            _loc1_ = this._buffVO.args.skin;
         }
         else if(this._constVO.skin)
         {
            _loc1_ = this._constVO.skin;
         }
         if(!_loc1_)
         {
            return;
         }
         this._bmc = new SysBMC();
         this._bmc.setSequences(AssetManager.findAniRes(_loc1_.name));
         if(_loc1_.align == "TOP")
         {
            this._bmc.setLocation(0,-this._owner.height - 20);
         }
         else if(_loc1_.align == "CENTER")
         {
            this._bmc.setLocation(0,-this._owner.height * 0.5);
         }
         this._owner.view.addBuff(this._bmc);
      }
      
      protected function get ownEnabled() : Boolean
      {
         return Boolean(this._owner) && Boolean(this._owner.view) && Boolean(this._owner.roleVO);
      }
      
      public function get buffVO() : BuffVO
      {
         return this._buffVO;
      }
      
      override protected function clean() : void
      {
         this.cleanRole();
         this.destroyBuff();
      }
      
      public function destroyBuff() : void
      {
         if(this._bmc)
         {
            this._bmc.destroy();
         }
         this._bmc = null;
         this._timer.destroy();
         this._timer = null;
         this._buffVO = null;
         this._owner = null;
      }
   }
}

