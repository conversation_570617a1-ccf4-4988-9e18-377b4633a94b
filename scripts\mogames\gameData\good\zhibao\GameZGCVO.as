package mogames.gameData.good.zhibao
{
   import com.mogames.utils.MathUtil;
   import mogames.Layers;
   import mogames.gameBuff.BuffProxy;
   import mogames.gameData.buff.base.BuffVO;
   import mogames.gameData.good.base.ConstEquipVO;
   import mogames.gameData.good.equip.GameZhiBaoVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameRole.base.IRole;
   
   public class GameZGCVO extends GameZhiBaoVO
   {
      
      public function GameZGCVO(param1:ConstEquipVO)
      {
         super(param1);
      }
      
      override public function handlerATK(param1:IRole) : void
      {
         if(!MathUtil.checkOdds(30) || param1.isDead)
         {
            return;
         }
         param1.roleVO.changeHP(param1.roleVO.totalHP * 0.05);
         var _loc2_:BuffVO = new BuffVO(1001,5,{
            "value":30,
            "isPer":true
         });
         var _loc3_:BuffVO = new BuffVO(1006,5,{
            "value":200,
            "isPer":true
         });
         BuffProxy.instance().addRoleBuff(_loc2_,param1);
         BuffProxy.instance().addRoleBuff(_loc3_,param1);
         EffectManager.addBMC("SEQ_BUFF_XI_XUE_CLIP",Layers.frontLayer,param1.x,param1.y);
      }
   }
}

