package mogames.gameData.map
{
   public class MapProxy
   {
      
      private static var _instance:MapProxy;
      
      public var curMode:int;
      
      public var curArea:int;
      
      public var secret:int;
      
      public function MapProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
      }
      
      public static function instance() : MapProxy
      {
         if(!_instance)
         {
            _instance = new MapProxy();
         }
         return _instance;
      }
      
      public function init() : void
      {
         this.curMode = 0;
         this.curArea = 0;
         this.secret = 0;
      }
      
      public function setArea(param1:int) : void
      {
         this.curArea = param1;
         this.secret = 0;
      }
      
      public function setSecret(param1:int) : void
      {
         this.secret = param1;
         this.curArea = 0;
         this.curMode = 0;
      }
   }
}

