package mogames.gameData.map
{
   import com.mogames.utils.TxtUtil;
   import mogames.gameData.master.MasterProxy;
   import mogames.gameData.mission.base.MissionBattleVO;
   import mogames.gameData.role.HeroProxy;
   import mogames.gameData.role.hero.HeroGameVO;
   
   public class SDHandler
   {
      
      public var expLead:Object;
      
      public var expHero:Object;
      
      public function SDHandler()
      {
         super();
      }
      
      public function handlerSD(param1:MissionBattleVO) : void
      {
         this.expLead = this.addLeadExp(param1.expLead);
         this.expHero = this.addHeroExp(param1.expHero * 0.4);
      }
      
      private function addLeadExp(param1:int) : Object
      {
         var _loc2_:int = MasterProxy.instance().masterVO.level;
         MasterProxy.instance().addExp(param1);
         var _loc3_:* = "";
         var _loc4_:* = MasterProxy.instance().masterVO.level > _loc2_;
         if(MasterProxy.instance().masterVO.level > _loc2_)
         {
            _loc3_ = "主公升至" + TxtUtil.setColor(MasterProxy.instance().masterVO.level,"99ff00") + "级！";
         }
         else
         {
            _loc3_ = "主公：" + MasterProxy.instance().masterVO.level + "级（" + MasterProxy.instance().masterVO.curExp + "/" + MasterProxy.instance().totalExp + "）";
         }
         _loc3_ += "<br><br>" + TxtUtil.setColor("当前等级上限为：" + MasterProxy.instance().masterVO.maxLevel + "级","FFFF00");
         return {
            "exp":param1,
            "tip":_loc3_,
            "isLevel":_loc4_
         };
      }
      
      private function addHeroExp(param1:int) : Object
      {
         var _loc3_:int = 0;
         var _loc5_:Boolean = false;
         var _loc6_:HeroGameVO = null;
         var _loc2_:Array = HeroProxy.instance().ownerHeros;
         var _loc4_:Array = [];
         for each(_loc6_ in _loc2_)
         {
            _loc3_ = int(_loc6_.level);
            _loc6_.addEXP(param1);
            if(_loc6_.level > _loc3_)
            {
               _loc5_ = true;
               _loc4_[_loc4_.length] = _loc6_.qualityName + "升至" + TxtUtil.setColor(_loc6_.level,"99ff00") + "级";
            }
         }
         return {
            "exp":param1,
            "tip":_loc4_.join("，"),
            "isLevel":_loc5_
         };
      }
      
      public function destroy() : void
      {
         this.expLead = null;
         this.expHero = null;
      }
   }
}

