package file
{
   import com.mogames.data.RandomVO;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.mache.MacheEventVO;
   import mogames.gameData.mache.MachePropVO;
   import mogames.gameData.mission.base.MissionConstVO;
   
   public class MacheConfig
   {
      
      private static var _instance:MacheConfig;
      
      private var _xian0:Array;
      
      private var _xian1:Array;
      
      private var _jun0:Array;
      
      private var _jun1:Array;
      
      private var _jun2:Array;
      
      public function MacheConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : MacheConfig
      {
         if(!_instance)
         {
            _instance = new MacheConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._xian0 = [];
         this._xian0[this._xian0.length] = new MacheEventVO(false,"沉迷怡红院，",[{
            "id":10002,
            "value":new RandomVO(1,5)
         },{
            "id":10001,
            "value":new RandomVO(3,8)
         }]);
         this._xian0[this._xian0.length] = new MacheEventVO(false,"沉迷赌场，输的精光。",[{
            "id":10002,
            "value":new RandomVO(3,7)
         },{
            "id":10001,
            "value":new RandomVO(1,6)
         },{
            "id":10000,
            "value":new RandomVO(5000,20000)
         }]);
         this._xian0[this._xian0.length] = new MacheEventVO(false,"碰到小偷，",[{
            "id":10000,
            "value":new RandomVO(3000,15000)
         }]);
         this._xian0[this._xian0.length] = new MacheEventVO(false,"碰到江洋大盗，",[{
            "id":10006,
            "value":new RandomVO(20,50)
         }]);
         this._xian0[this._xian0.length] = new MacheEventVO(false,"碰到绑匪，为保性命破财消灾。，",[{
            "id":10007,
            "value":new RandomVO(5,22)
         }]);
         this._xian1 = [];
         this._xian1[this._xian1.length] = new MacheEventVO(true,"鸿运当头，在赌场中赢钱！",[{
            "id":10000,
            "value":new RandomVO(4000,18000)
         }]);
         this._xian1[this._xian1.length] = new MachePropVO("私访被人认出，被大赞扬治国有道：",[new BaseRewardVO(10539,1),new BaseRewardVO(10540,1),new BaseRewardVO(10501,1),new BaseRewardVO(10503,1),new BaseRewardVO(10505,1),new BaseRewardVO(10510,1),new BaseRewardVO(10545,1)]);
         this._xian1[this._xian1.length] = new MachePropVO("帮助衙门破案,获得奖赏：",[new BaseRewardVO(10547,1),new BaseRewardVO(10548,1),new BaseRewardVO(10549,1)]);
         this._xian1[this._xian1.length] = new MachePropVO("在茶楼与人相谈甚欢：",[new BaseRewardVO(50027,1),new BaseRewardVO(50027,2)]);
         this._xian1[this._xian1.length] = new MachePropVO("打劫盗墓贼，获得：",[new BaseRewardVO(10851,1),new BaseRewardVO(10851,2)]);
         this._xian1[this._xian1.length] = new MachePropVO("鸿运当头，路边捡到好东西：",[new BaseRewardVO(10527,1),new BaseRewardVO(10528,1),new BaseRewardVO(10527,2),new BaseRewardVO(10528,2)]);
         this._xian1[this._xian1.length] = new MachePropVO("鸿运当头，路边捡到好东西：",[new BaseRewardVO(10980,1)]);
         this._xian1[this._xian1.length] = new MachePropVO("帮助衙门破案,获得奖赏：",[new BaseRewardVO(10521,1),new BaseRewardVO(10522,1),new BaseRewardVO(10521,2),new BaseRewardVO(10522,2)]);
         this._xian1[this._xian1.length] = new MachePropVO("帮助衙门破案,获得奖赏：",[new BaseRewardVO(10523,1),new BaseRewardVO(10524,1),new BaseRewardVO(10523,2),new BaseRewardVO(10524,2)]);
         this._xian1[this._xian1.length] = new MachePropVO("帮助衙门破案,获得奖赏：",[new BaseRewardVO(10525,1),new BaseRewardVO(10526,1),new BaseRewardVO(10525,2),new BaseRewardVO(10526,2)]);
         this._jun0 = [];
         this._jun0[this._jun0.length] = new MacheEventVO(false,"沉迷怡红院，",[{
            "id":10002,
            "value":new RandomVO(2,10)
         },{
            "id":10001,
            "value":new RandomVO(6,16)
         }]);
         this._jun0[this._jun0.length] = new MacheEventVO(false,"沉迷赌场，输的精光。",[{
            "id":10002,
            "value":new RandomVO(6,15)
         },{
            "id":10001,
            "value":new RandomVO(2,12)
         },{
            "id":10000,
            "value":new RandomVO(10000,40000)
         }]);
         this._jun0[this._jun0.length] = new MacheEventVO(false,"碰到小偷，",[{
            "id":10000,
            "value":new RandomVO(6000,30000)
         }]);
         this._jun0[this._jun0.length] = new MacheEventVO(false,"碰到江洋大盗，",[{
            "id":10006,
            "value":new RandomVO(40,100)
         }]);
         this._jun0[this._jun0.length] = new MacheEventVO(false,"碰到绑匪，为保性命破财消灾。",[{
            "id":10007,
            "value":new RandomVO(10,44)
         }]);
         this._jun1 = [];
         this._jun1[this._jun1.length] = new MachePropVO("德道高僧送上镇寺至宝",[new BaseRewardVO(10541,1),new BaseRewardVO(10541,1),new BaseRewardVO(10541,1),new BaseRewardVO(10541,2)]);
         this._jun1[this._jun1.length] = new MachePropVO("湖底打捞获得",[new BaseRewardVO(10280,1),new BaseRewardVO(10281,1)]);
         this._jun1[this._jun1.length] = new MachePropVO("贪官抄家，从金库中翻出",[new BaseRewardVO(10280,1),new BaseRewardVO(10282,1)]);
         this._jun1[this._jun1.length] = new MachePropVO("打劫盗墓贼，获得",[new BaseRewardVO(10281,1),new BaseRewardVO(10283,1)]);
         this._jun1[this._jun1.length] = new MachePropVO("偷偷潜入山贼后山宝库找到",[new BaseRewardVO(10852,1),new BaseRewardVO(10853,1),new BaseRewardVO(10983,1)]);
         this._jun2 = [];
         this._jun2[this._jun2.length] = new MachePropVO("帮助衙门破案：",[new BaseRewardVO(10564,1),new BaseRewardVO(10565,1),new BaseRewardVO(10566,1),new BaseRewardVO(10564,2),new BaseRewardVO(10565,2),new BaseRewardVO(10566,2)]);
         this._jun2[this._jun2.length] = new MachePropVO("鸿运当头！帮忙修建寺庙挖出了：",[new BaseRewardVO(10542,2),new BaseRewardVO(10543,2),new BaseRewardVO(10544,2),new BaseRewardVO(10280,1)]);
         this._jun2[this._jun2.length] = new MachePropVO("衙门县令送上珍藏的：",[new BaseRewardVO(10536,1),new BaseRewardVO(10537,1),new BaseRewardVO(10538,1),new BaseRewardVO(10536,2),new BaseRewardVO(10537,2),new BaseRewardVO(10538,2)]);
         this._jun2[this._jun2.length] = new MachePropVO("富商送上传家宝：：",[new BaseRewardVO(10564,1),new BaseRewardVO(10565,1),new BaseRewardVO(10566,1),new BaseRewardVO(10564,2),new BaseRewardVO(10565,2),new BaseRewardVO(10566,2)]);
         this._jun2[this._jun2.length] = new MachePropVO("在茶楼与人相谈甚欢：",[new BaseRewardVO(50023,1),new BaseRewardVO(50023,2),new BaseRewardVO(50023,3)]);
      }
      
      public function randomEvent(param1:MissionConstVO) : MacheEventVO
      {
         if(param1.isLarge)
         {
            return this.randomJun;
         }
         return this.randomXian;
      }
      
      public function get randomXian() : MacheEventVO
      {
         var _loc1_:int = Math.random() * 100 + 1;
         if(_loc1_ <= 1)
         {
            return null;
         }
         if(_loc1_ <= 10)
         {
            return this._xian0[int(Math.random() * this._xian0.length)];
         }
         return this._xian1[int(Math.random() * this._xian1.length)];
      }
      
      public function get randomJun() : MacheEventVO
      {
         var _loc1_:int = Math.random() * 100 + 1;
         if(_loc1_ <= 5)
         {
            return this._jun0[int(Math.random() * this._jun0.length)];
         }
         if(_loc1_ <= 50)
         {
            return this._jun1[int(Math.random() * this._jun1.length)];
         }
         return this._jun2[int(Math.random() * this._jun2.length)];
      }
   }
}

