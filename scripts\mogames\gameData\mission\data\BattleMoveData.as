package mogames.gameData.mission.data
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   
   public class BattleMoveData
   {
      
      public var friendPaths:Array;
      
      public var friendHeroP:Array;
      
      public var rectNear:Rectangle;
      
      public var rectFar:Rectangle;
      
      public var rectPet:Rectangle;
      
      public var pmStart:Rectangle;
      
      public var pmEnd:Rectangle;
      
      public var rectSkill:Rectangle;
      
      public var enemyBorn:Point;
      
      public var enemyRects:Array;
      
      public var enemyPatrol:Rectangle;
      
      public var boxRect:Rectangle;
      
      public function BattleMoveData()
      {
         super();
      }
      
      public function init() : void
      {
         this.rectNear = new Rectangle(750,190,200,300);
         this.rectFar = new Rectangle(530,190,200,300);
         this.rectSkill = new Rectangle(296,170,1500,355);
         this.rectPet = new Rectangle(615,190,220,200);
         this.enemyBorn = new Point(1808,311);
         this.enemyRects = [new Rectangle(1700,287,75,75),new Rectangle(1480,190,200,300)];
         this.enemyPatrol = new Rectangle(750,190,200,300);
         this.pmStart = new Rectangle(1550,210,100,250);
         this.pmEnd = new Rectangle(300,275,70,90);
         this.boxRect = new Rectangle(512,185,840,275);
      }
      
      public function get enemyPaths() : Array
      {
         var _loc2_:Rectangle = null;
         var _loc4_:Point = null;
         var _loc1_:Array = [];
         _loc1_[0] = this.enemyBorn;
         var _loc3_:int = 0;
         while(_loc3_ < 2)
         {
            _loc2_ = this.enemyRects[_loc3_];
            _loc4_ = new Point();
            _loc4_.x = int(_loc2_.x + Math.random() * _loc2_.width);
            _loc4_.y = int(_loc2_.y + Math.random() * _loc2_.height);
            _loc1_.push(_loc4_);
            _loc3_++;
         }
         return _loc1_;
      }
      
      public function findFriendRect(param1:Boolean) : Rectangle
      {
         if(param1)
         {
            return this.rectFar;
         }
         return this.rectNear;
      }
      
      public function findFriendPaths(param1:int) : Array
      {
         return this.friendPaths[param1];
      }
      
      public function findHeroP(param1:int) : Point
      {
         return this.friendHeroP[param1];
      }
   }
}

