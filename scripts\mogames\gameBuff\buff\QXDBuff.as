package mogames.gameBuff.buff
{
   import com.mogames.utils.TxtUtil;
   import mogames.gameBuff.base.LoopRoleBuff;
   import mogames.gameData.buff.ConstBuffVO;
   import mogames.gameEffect.EffectManager;
   
   public class QXDBuff extends LoopRoleBuff
   {
      
      public function QXDBuff(param1:ConstBuffVO)
      {
         super(param1);
      }
      
      override protected function onLoop() : void
      {
         if(_owner.isDead || _owner.roleVO.hpPer > _buffVO.args.hpPer * 0.01)
         {
            return;
         }
         var _loc1_:int = _owner.roleVO.totalHP * _buffVO.args.curePer * 0.01;
         _owner.roleVO.changeHP(_loc1_);
         EffectManager.addHeadWord(TxtUtil.setColor("生命+" + _loc1_,"99ff00"),_owner.x,_owner.y - _owner.height);
      }
   }
}

