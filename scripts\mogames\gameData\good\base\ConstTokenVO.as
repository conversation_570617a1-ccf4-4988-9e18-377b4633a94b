package mogames.gameData.good.base
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   
   public class ConstTokenVO extends ConstGoodVO
   {
      
      private var _heroID:Oint = new Oint();
      
      private var _need:Oint = new Oint();
      
      public function ConstTokenVO(param1:int, param2:int, param3:int, param4:int, param5:int, param6:String, param7:String, param8:String, param9:String)
      {
         super(param1,param4,1,param5,0,param6,param7,param8,param9);
         MathUtil.saveINT(this._heroID,param2);
         MathUtil.saveINT(this._need,param3);
      }
      
      public function get heroID() : int
      {
         return MathUtil.loadINT(this._heroID);
      }
      
      public function get needNum() : int
      {
         return MathUtil.loadINT(this._need);
      }
   }
}

